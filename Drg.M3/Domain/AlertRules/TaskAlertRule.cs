#nullable enable
using Drg.Core;
using Drg.M3.Dal;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Drg.M3.Domain.AlertRules
{
    [NonReportable, AutoMapped, Description("Task Alert")]
    public class TaskAlertRule : DateAndStatusAlertRule
    {
        public override Type DomainType => typeof(Task);

        public override Dictionary<string, string> GetDateFields() =>
            new Dictionary<string, string> { { "DueDate", "Due" }, { "CompletedDate", "Completed" } };

        public override Dictionary<string, ICriterion> GetStatuses() => new Dictionary<string, ICriterion>()
        {
            {"Incomplete", Restrictions.IsNull("CompletedDate")},
            {"Complete", Restrictions.IsNotNull("CompletedDate")}
        };

        public override bool ShouldDeactivate()
        {
            if (Record is Task t)
                return (t.DD != null || (t.CompletedDate != null && t.CompletedDate.AddDays(10) < NiftyDate.Now));
            return true;
        }
    }
}
