#nullable enable
using Drg.Core;
using Drg.M3.Dal;
using Drg.M3.Domain.QA;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Drg.M3.Domain.AlertRules
{
    [NonReportable, AutoMapped, Description("Permit Alert")]
    public class PermitAlertRule : DateAndStatusAlertRule
    {
        public override Type DomainType => typeof(Permit);

        public override Dictionary<string, string> GetDateFields()
            => new Dictionary<string, string> { { "Issued", "Issued" }, { "Expires", "Expires" }, { "ReviewDate", "Review Date" } };

        public override Dictionary<string, ICriterion> GetStatuses() => new Dictionary<string, ICriterion>()
        {
            {"Current", Restrictions.Or(Restrictions.IsNull("Expires"), Restrictions.Ge("Expires", NiftyDate.Now))},
            {"Expired", Restrictions.Lt("Expires", NiftyDate.Now)}
        };

        public override bool ShouldDeactivate()
        {
            if (Record is Permit p)
                return p.DD != null;
            return true;
        }
    }
}
