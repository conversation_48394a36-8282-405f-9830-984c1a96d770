using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Drg.M3.Domain.Radiology
{
    /// <summary>
    /// Document workflow step
    /// </summary>
    [NonQueryable]
    public class WorkflowStep : CommonBusinessObject, IHasNotes
    {
        public WorkflowStep()
            : base()
        {
        }

        /// <summary>
        /// Related workflow
        /// </summary>
        public virtual Workflow Workflow { get; set; }
        /// <summary>
        /// Related workflow template step
        /// </summary>
        public virtual WorkflowTemplateStep WorkflowTemplateStep { get; set; }
        /// <summary>
        /// Related task
        /// </summary>
        public virtual Task Task { get; set; }
        /// <summary>
        /// Collection of workflow step logs
        /// </summary>
        public virtual ISet<WorkflowStepLog> WorkflowStepLogs { get; set; } = new HashSet<WorkflowStepLog>();

        /// <summary>
        /// Instructions for this workflow step
        /// </summary>
        public virtual string Instructions { get; set; }
        //public virtual int DaysToComplete { get; set; }
        //public virtual Distribution Distribution { get; set; }

        /// <summary>
        /// Date the workflow step was assigned
        /// </summary>
        public virtual NiftyDate AssignedDate { get; set; }
    }
}