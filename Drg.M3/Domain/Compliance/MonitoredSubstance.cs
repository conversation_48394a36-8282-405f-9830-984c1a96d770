using Drg.M3.Dal;
using Drg.M3.Modules;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;

namespace Drg.M3.Domain.Compliance
{
    /// <summary>
    /// Class for substance to monitor
    /// </summary>
    [AutoMapped, Table("CMP_MonitoredSubstance")]
    [Module(typeof(Modules.Compliance))]
    public class MonitoredSubstance : CommonBusinessObject, IHasNotes
    {
#nullable enable
        protected MonitoredSubstance() { }
        public MonitoredSubstance(string name, Organization org)
        {
            this.Name = name;
            this.Organization = org;
        }
        /// <summary>
        /// This record is shared with other organizations
        /// </summary>
        public virtual bool IsShared { get; set; }
        /// <summary>
        /// Description of this object
        /// </summary>
        [MaxLength(int.MaxValue)]
        public virtual string? Description { get; set; }
        /// <summary>
        /// Components contained by this substance
        /// </summary>
        public virtual ISet<MonitoredComponent> Components { get; set; } = new HashSet<MonitoredComponent>();
        /// <summary>
        /// Samples of this substance
        /// </summary>
        public virtual ISet<Sample> Samples { get; set; } = new HashSet<Sample>();
    }
}
