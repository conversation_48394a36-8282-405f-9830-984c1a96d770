using Drg.M3.Dal;
using Drg.M3.Modules;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Drg.M3.Domain.HW
{
    /// <summary>
    /// Unit of Measure
    /// </summary>
    [AutoMapped, Discriminator(FullyQualified = true)]
    [Module(typeof(Modules.HW))]
    public class ContainerUnit : Reference
    {
        public ContainerUnit()
        {
            ReferenceType = ReferenceType.ContainerUnit;
        }

        [MaxLength(int.MaxValue)]
        public virtual string ManifestAbbreviation { get; set; }
    }
}
