using Drg.M3.Bll.HW.DAAS.Models.AG_824R;
using Drg.M3.Bll.HW.DAAS.Models.MD_527R;
using Drg.M3.Bll.HW.DAAS.Models;
using Drg.M3.Dal;
using System.IO;
using System.Xml.Serialization;
using System;
using System.Linq;
using System.ComponentModel.DataAnnotations;

namespace Drg.M3.Domain.HW
{
    [AutoMapped]
    public class DAASResponse  : BusinessObject
    {
        private const string ITEM_ACCEPT = "IA";
        private const string TRANSACTION_SET_REJECT = "TR";

        public virtual string Filename { get; set; }

        public virtual string Type { get; set; }

        [MaxLength(Int32.MaxValue)]
        public virtual string Content { get; set; }

        [MaxLength(Int32.MaxValue)]
        public virtual string Note { get; set; }

        public virtual bool IsAcceptance()
        {
            if (this.Type == typeof(F_File_527R).Name)
            {
                return true;
            }

            if (this.Type == typeof(F_File_824R).Name)
            {
                var seraializer = new XmlSerializer(typeof(F_File_824R));
                var file = seraializer.Deserialize(new StringReader(this.Content)) as F_File_824R;
                var advice = file.T_Application_Advice_824Rs.Single();
                var origTransId = advice.L_Original_Transaction_Identifications.Single();
                var ackCode = origTransId.S_Original_Transaction_Identification.E_Application_Acknowledgment_Code;

                if (ackCode == ITEM_ACCEPT)
                {
                    return true;
                }
                else if (ackCode == TRANSACTION_SET_REJECT)
                {
                    return false;
                }

                throw new UnknownValueException("E_Application_Acknowledgment_Code", ackCode);
            }

            throw new NotImplementedException($"Support to implement {this.Type} has not been implemented.");
        }
    }
}
