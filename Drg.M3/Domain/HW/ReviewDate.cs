using Drg.M3.Modules;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Drg.M3.Domain.HW
{
    /// <summary>
    /// Waste profile review log
    /// </summary>
    [Module(typeof(Modules.HW))]
    [Di<PERSON><PERSON><PERSON><PERSON>("Waste Profile Review")]
    public class ReviewDate : PersistentObject
    {
        /// <summary>
        /// User who reviewed
        /// </summary>
        public virtual User User { get; set; }
        /// <summary>
        /// Review date
        /// </summary>
        public virtual DateTime Date { get; set; }
        /// <summary>
        /// Waste profile that was reviewed
        /// </summary>
        public virtual WasteProfile WasteProfile { get; set; }
    }
}
