using System.Collections.Generic;
using System.Linq;

namespace Drg.M3.Domain
{
    /// <summary>
    /// Data call class
    /// </summary>
    public class DataCall : CommonBusinessObject, IAuditable
    {
        protected DataCall() { }

#nullable enable
        public DataCall(string name, Organization org, Quiz.Template quizTemplate)
        {
            Name = name;
            Organization = org;
            QuizTemplate = quizTemplate;
        }
        /// <summary>
        /// Data call description
        /// </summary>
        public virtual string? Description { get; set; }
        /// <summary>
        /// Location in the menu to place a shortcut to this data call
        /// </summary>
        public virtual string? <PERSON>u<PERSON>ey { get; set; }
        /// <summary>
        /// Back-reference to the data call this was copied from
        /// </summary>
        public virtual DataCall? PreviousDataCall { get; set; }

        /// <summary>
        /// The quiz template used for this data call
        /// </summary>
        public virtual Quiz.Template QuizTemplate { get; set; }

        public virtual int CountCompleted
        {
            get { return QuizTemplate.Quizzes.Count(q => q.Completed != null); }
        }
        public virtual int CountRecipients
        {
            get { return Assignments.Select(a => a.Organization).Distinct().Count(); }
        }

        /// <summary>
        /// Media this data call is related to
        /// </summary>
        public virtual Reference? Media { get; set; }
        /// <summary>
        /// Date the data call was closed
        /// </summary>
        public virtual NiftyDate? Closed { get; set; }
        /// <summary>
        /// This is a scored data call
        /// </summary>
        public virtual bool IsScored { get; set; }
        /// <summary>
        /// Date the data call was sent
        /// </summary>
        public virtual NiftyDate? Sent { get; set; }
        /// <summary>
        /// If this is a copy of another data call, specifies the original
        /// </summary>
        public virtual DataCall? Original { get; set; }
        /// <summary>
        /// Due date of assignments
        /// </summary>
        public virtual NiftyDate? DueDate { get; set; }
        /// <summary>
        /// Disable preloading data
        /// </summary>
        public virtual bool DisablePreload { get; set; }
        /// <summary>
        /// Disable grouping assignments
        /// </summary>
        public virtual bool DisableGrouping { get; set; }
        /// <summary>
        /// Disable negative report (no-answer submission)
        /// </summary>
        public virtual bool DisableNegativeReport { get; set; }
        /// <summary>
        /// Show data call in My Media
        /// </summary>
        public virtual bool ProgramStatistics { get; set; }

        /// <summary>
        /// Default data call group
        /// </summary>
        public virtual DataCallGroup? DefaultGroup { get; set; }

        /// <summary>
        /// All assignments (flat list) for the data call
        /// </summary>
        public virtual ISet<DataCallAssignment> Assignments { get; set; } = new HashSet<DataCallAssignment>();

        /// <summary>
        /// All data call groups for the data call
        /// </summary>
        public virtual ISet<DataCallGroup> Groups { get; set; } = new HashSet<DataCallGroup>();
    }
}