using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using Drg.Core;
using Drg.M3.Dal;
using System.Xml;
using Drg.M3.Client;
using System.ComponentModel;

namespace Drg.M3.Domain
{
    /// <summary>
    /// Error log entry
    /// </summary>
    [NonReportable]
    public class Error : PersistentObject, IHasNotes
    {
        public Error() { }
#nullable enable
        /// <summary>
        /// ASP.NET application name
        /// </summary>
        public virtual string Application { get; set; }
        /// <summary>
        /// Host/computer name
        /// </summary>
        public virtual string Host { get; set; }
        /// <summary>
        /// Exception type name
        /// </summary>
        public virtual string Type { get; set; }
        /// <summary>
        /// Assembly that originated the exeption
        /// </summary>
        public virtual string Source { get; set; }
        /// <summary>
        /// Exception message
        /// </summary>
        public virtual string Message { get; set; }
        /// <summary>
        /// HTTP Method
        /// </summary>
        public virtual string? Method { get; set; }
        /// <summary>
        /// String User ID returned by ELMAH
        /// </summary>
        public virtual string? StrUser { get; set; }
        /// <summary>
        /// URL that triggered the exception
        /// </summary>
        public virtual string? Url { get; set; }
        public virtual string? UrlWithQueryString
        {
            get
            {
                return Url == null ? null : Url + (string.IsNullOrWhiteSpace(QueryString) ? "" : ("?" + QueryString));
            }
        }

        /// <summary>
        /// Group of similar errors
        /// </summary>
        public virtual ErrorGroup ErrorGroup { get; set; }

        User? _user;
        public virtual User User
        {
            get
            {
                if (_user == null)
                {
#pragma warning disable 618
                    using (DeletedFilter.Disable(M3SessionSingleton.Instance))
                    {
                        _user = M3SessionSingleton.Instance.Get<User>(Parse.NullableInteger(StrUser));
                    }
#pragma warning restore 618
                }
                return _user;
            }
        }

        /// <summary>
        /// HTTP status code
        /// </summary>
        public virtual int StatusCode { get; set; }
        /// <summary>
        /// Date of error
        /// </summary>
        public virtual NiftyDate ErrorDate { get; set; }
        /// <summary>
        /// ELMAH generated error XML
        /// </summary>
        public virtual string AllXml { get; set; }
        /// <summary>
        /// Status/resolution of the error
        /// </summary>
        public virtual ErrorResolution Resolution { get; set; }

        public override string ToString()
        {
            return "ERR#" + Id;
        }

        #region Xml Properties
        bool _xmlLoaded = false;
        void LoadXml()
        {
            if (!_xmlLoaded)
            {
                try
                {
                    var xml = new XmlDocument();
                    xml.LoadXml(AllXml);

                    //var path_info = xml.SelectSingleNode("error/serverVariables/item[@name='PATH_INFO']/value/@string") as XmlAttribute;
                    var query_string = xml.SelectSingleNode("error/serverVariables/item[@name='QUERY_STRING']/value/@string") as XmlAttribute;
                    var http_referer = xml.SelectSingleNode("error/serverVariables/item[@name='HTTP_REFERER']/value/@string") as XmlAttribute;
                    var detail = xml.SelectSingleNode("error/@detail") as XmlAttribute;
                    
                    if (http_referer != null)
                        _referer = http_referer.Value;
                    if (detail != null)
                        _detail = detail.Value;
                    if (query_string != null)
                        _query_string = query_string.Value;
                }
                catch (XmlException) { }
                _xmlLoaded = true;
            }
        }

        string? _referer;
        public virtual string? Referer
        {
            get
            {
                LoadXml();
                return _referer;
            }
        }
        string? _detail;
        public virtual string? Detail
        {
            get
            {
                LoadXml();
                return _detail;
            }
        }
        string? _query_string;
        public virtual string? QueryString
        {
            get
            {
                LoadXml();
                return _query_string;
            }
        }
        #endregion
    }
    public enum ErrorResolution : int
    {
        Unresolved = 0,
        [Description("Unresolved (Priority)")]
        Priority = 1,
        Resolved = 2,
        Ignored = 3,
        Suppress = 4,
    }
}