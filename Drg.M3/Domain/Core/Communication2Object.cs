using Drg.M3.Dal;
using FluentNHibernate.Automapping;
using FluentNHibernate.Automapping.Alterations;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;


namespace Drg.M3.Domain
{
    /// <summary>
    /// Many-to-many relating a communication to an object in the database
    /// </summary>
    [AutoMapped]
    [NonReportable]
    public class Communication2Object : PersistentObject
    {
        public Communication2Object() { }
#nullable enable
        /// <summary>
        /// Class name of the related object
        /// </summary>
        public virtual string ClassName { get; protected set; }
        /// <summary>
        /// Id of the related object
        /// </summary>
        public virtual int ObjectId { get; protected set; }

        [Required]
        public virtual PersistentObject RelatedObject { get; set; }

        /// <summary>
        /// The related communication
        /// </summary>
        [Required]
        public virtual Communication Communication { get; set; }


        public class Mapping : IAutoMappingOverride<Communication2Object>
        {
            void IAutoMappingOverride<Communication2Object>.Override(AutoMapping<Communication2Object> mapping)
            {
                var metaTypes = typeof(IHasNotes).Assembly.GetTypes().Where(x => typeof(IHasNotes).IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract && x.Namespace.StartsWith("Drg.M3.Domain"));
               
                var any = mapping.ReferencesAny(x => x.RelatedObject)
                    .EntityIdentifierColumn("ObjectId").IdentityType<int>()
                    .EntityTypeColumn("ClassName").MetaType<string>();
                 
                foreach (var type in metaTypes)
                    any.AddMetaValue(type, type.FullName);

                mapping.Map(x => x.ObjectId).Formula("ObjectId").Not.Insert().Not.Update();
                mapping.Map(x => x.ClassName).Formula("ClassName").Not.Insert().Not.Update();
            }
        }
    }
}