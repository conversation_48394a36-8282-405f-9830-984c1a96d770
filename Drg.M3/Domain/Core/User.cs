using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Drg.M3.Dao;
using Drg.M3.Bll.Reports;
using System.Runtime.Serialization;
using Drg.M3.Domain.QA;
using System.Globalization;
using System.ComponentModel;
using Drg.Core;

namespace Drg.M3.Domain
{
    /// <summary>
    /// User class
    /// </summary>
    [Serializable]
    public class User : DeletableBusinessObject, IHasAssociation, IHasOrganization, IHasNotes, IHasInactive, IHasXml, IAuditable
    {
        public User()
        {
            CurrentRecursive = true;
        }

#nullable enable

        /// <summary>
        /// Used to segment data for different target audiences
        /// </summary>
        public virtual Association Association { get; set; }

        /// <summary>
        /// Default timezone Id for the user
        /// </summary>
        protected virtual string? TimeZoneId { get; set; }
        [NonReportable]
        public virtual TimeZoneInfo? TimeZoneInfo
        {
            get
            {
                return TimeZoneId != null ? TimeZoneInfo.FindSystemTimeZoneById(TimeZoneId) : null;
            }
            set
            {
                TimeZoneId = value == null ? null : value.Id;
            }
        }

        /// <summary>
        /// Identifier from legacy/external system
        /// </summary>
        public virtual int? OldPK { get; set; }

        /// <summary>
        /// User login credentials
        /// </summary>
        public virtual UserLogin UserLogin { get; set; }
        /// <summary>
        /// Rank and Title
        /// </summary>
        public virtual Reference? RankTitle { get; set; }
        /// <summary>
        /// User is a program manager
        /// </summary>
        public virtual bool IsProgramManager { get; set; }
        /// <summary>
        /// User is an organization administrator
        /// </summary>
        public virtual bool IsOrgAdmin { get; set; }
        /// <summary>
        /// User account is locked (disables logging in)
        /// </summary>
        public virtual bool IsLocked { get; set; }
        /// <summary>
        /// This is the system user account for this association
        /// </summary>
        public virtual bool IsSystem { get; set; }
        /// <summary>
        /// Suppress all emails to this user
        /// </summary>
        public virtual bool SuppressEmails { get; set; }
        /// <summary>
        /// Showing data for child organizations
        /// </summary>
		public virtual bool CurrentRecursive { get; set; }
        /// <summary>
        /// Personal homepage bookmark
        /// </summary>
        public virtual Bookmark? Homepage { get; set; }
        /// <summary>
        /// Warehouse of last login date
        /// </summary>
        public virtual NiftyDate? w_LastLoginDate { get; set; }
        /// <summary>
        /// Warehouse of the entity name
        /// </summary>
        public virtual string? w_EntityName { get; set; }
        
        /// <summary>
        /// Collection of user groups the member is in
        /// </summary>
        public virtual ISet<UserGroup> UserGroups { get; set; } = new HashSet<UserGroup>();
        /// <summary>
        /// Collection of assigned users
        /// </summary>
        public virtual ISet<User> AssignedUsers { get; set; } = new HashSet<User>();
        /// <summary>
        /// Collection of dashboard users
        /// </summary>
        public virtual ISet<User> DashboardUsers { get; set; } = new HashSet<User>();
        /// <summary>
        /// Collection of managers
        /// </summary>
        public virtual ISet<User> Managers { get; set; } = new HashSet<User>();
        /// <summary>
        /// Collection of media settings
        /// </summary>
        public virtual ISet<User2Media> Media { get; set; } = new HashSet<User2Media>();
        /// <summary>
        /// Comma-separated warehouse of related media
        /// </summary>
        public virtual string? w_Media { get; set; }

        /// <summary>
        /// User's default media
        /// </summary>
        public virtual Reference? DefaultMedia { get; set; }
        /// <summary>
        /// The Organization this record is related to
        /// </summary>
        public virtual Organization Organization { get; set; }
        /// <summary>
        /// The organization the user is currently viewing from
        /// </summary>
        public virtual Organization? CurrentOrganization { get; set; }
        /// <summary>
        /// The association the user is currently viewing from
        /// </summary>
        protected virtual Association? currentAssociation { get; set; }
        public virtual Association CurrentAssociation
        {
            get
            {
                return currentAssociation ?? Association;
            }
            set
            {
                currentAssociation = value;
            }
        }

        /// <summary>
        /// Enable Glimpse diagnostics
        /// </summary>
        [NonReportable]
        public virtual bool EnableGlimpse { get; set; }
        /// <summary>
        /// Related entity
        /// </summary>
        [DisplayName("Contact")]
        public virtual Entity Entity { get; set; }

        public virtual FindingsTab GetFindingsTabForAuditProfile()=> Audit_FindingsTab == FindingsTab.OtherOrgFindings ? FindingsTab.OrgFindings : Audit_FindingsTab;
        public virtual FindingsTab GetTabForAuditPoams() => Audit_FindingsTab == FindingsTab.MyFindings ? FindingsTab.OrgFindings : Audit_FindingsTab;

        public virtual FindingsTab Audit_FindingsTab { get; set; }
        public virtual FindingsClosedFilter Audit_ClosedFindings { get; set; }

        public override string ToString()
        {
            return Name + " (" + Organization.Name + ")";
        }

        #region Entity Mirror
        [NonReportable]
        public virtual bool IsInactive
        {
            get
            {
                if (Person != null)
                    return Person.IsInactive;
                return DD != null;
            }
            set
            {
                if (Person != null)
                    Person.IsInactive = value;
            }
        }

        [NonReportable]
        public virtual string EmailAddress
        {
            get
            {
                if (this.Person == null || this.Person.DefaultEmailAddress == null)
                    return this.UserLogin.UserName;
                return this.Person.DefaultEmailAddress.Address;
            }
        }

        [NonReportable]
        public virtual string FullName
        {
            get
            {
                if (this.Person == null)
                    return this.EmailAddress;
                return this.Person.FullName;
            }
        }

        [NonReportable]
        public virtual string Name
		{
			get
			{
                if (this.Person == null)
                    return this.EmailAddress;
                return this.Person.Name;
			}
		}

        [NonReportable]
        public virtual Person Person
        {
            get
            {
                return (Person)Entity;
            }
        }
        #endregion

        [NonReportable]
        public virtual string UserGroupsConcatToString
        {
            get
            {
                return NonPersonalUserGroups.ConcatToString();
            }
        }
        [NonReportable]
        public virtual IEnumerable<UserGroup> NonPersonalUserGroups
        {
            get
            {
                return ActiveUserGroups.Where(ug => ug.IsUserSpecific == false).AsToStringable();
            }
        }
        [NonReportable]
        public virtual IEnumerable<UserGroup> ActiveUserGroups
        {
            get
            {
                return UserGroups.Where(ug => ug.DD == null).AsToStringable();
            }
        }

        public virtual bool HasPermission(IHasOrganization obj)
        {
            return this.Organization.Equals(obj.Organization) || (obj.Organization != null && this.Organization.IsAncestor(obj.Organization));
        }

        #region IHasNotes Members

        int IHasNotes.Id
        {
            get { return Entity.Id; }
        }

        Type IHasNotes.GetType()
        {
            return Entity.GetType();
        }

        #endregion

        [NonReportable]
        public virtual CultureInfo? UICulture
        {
            get
            {
                return Settings.CultureName == null ? null : CultureInfo.GetCultureInfo(Settings.CultureName);
            }
        }

        protected virtual IDictionary<string, bool> ColumnVisibility { get; set; } = new Dictionary<string, bool>();

        public virtual bool IsColumnVisible(string gridName, string columnName, bool @default = true)
        {
            string key = string.Join(".", gridName, columnName);
            if (ColumnVisibility.ContainsKey(key))
                return ColumnVisibility[key];
            else
                return @default;
        }

        public virtual void SetColumnVisible(string gridName, string columnName, bool visible)
        {
            string key = string.Join(".", gridName, columnName);
            ColumnVisibility[key] = visible;
        }

        #region Settings
        /// <summary>
        /// Expando XML field
        /// </summary>
        public virtual string? Xml { get; set; }
        UserSettings? _settings;

        [NonReportable]
        public virtual UserSettings Settings
        {
            get
            {
                if (_settings == null)
                    _settings = UserSettings.Load(this);
                return _settings;
            }
            set
            {
                _settings = value;
                _settings.Parent = this;
                _settings.Save();
            }
        }
        
        #endregion

    }
    
    public class UserSettings : XmlSettings, IExtensibleDataObject
    {
        [DataMember]
        public string? CultureName { get; set; }
        [DataMember]
        public bool DefaultUnifiedDashboard { get; set; }
        [DataMember]
        public string? LastCalendarView { get; set; }
        [DataMember]
        public Dictionary<int, List<int>>? QueryOrgIdsDict { get; set; }

        public bool GetQueryOrgIds(out List<int>? orgIds, Organization? org = null)
        {
            orgIds = GetQueryOrgIds(org);
            return orgIds != null;
        }
        public List<int>? GetQueryOrgIds(Organization? org = null)
        {
            if (org == null)
                org = Drg.M3.Client.M3Context.Current.Organization;

            if (QueryOrgIdsDict != null && QueryOrgIdsDict.TryGetValue(org.Id, out List<int> result))
            {
                return result;
            }
            return null;
        }

        [DataMember]
        public bool SimpleFindings { get; set; }

        [DataMember]
        public List<DashboardTable>? DashboardTables { get; set; }
        [DataMember]
        public Dictionary<string, object>? DashboardFilters { get; set; }

        //public Dictionary<string, bool> ColumnVisibility { get; set; } = new Dictionary<string, bool>();

        //[DataMember]
        //public bool HideTutorial { get; set; }

        [DataMember]
        public Dictionary<Tuple<string, string, int>, string>? GridSettings { get; set; }

        [DataMember]
        public bool? HW_Filter_Pickup_Completed { get; set; }

        [DataMember]
        public int? LastGuidanceAreaId { get; set; }

        [DataMember]
        public bool? GuidanceRecursive { get; set; }

        public static UserSettings Load(User parent)
        {
            if (parent.Xml != null)
            {
                var result = Extensions.XmlToContractObject<UserSettings>(parent.Xml);
                result.Parent = parent;
                if (result.GridSettings == null)
                    result.GridSettings = new Dictionary<Tuple<string, string, int>, string>();
                if (result.DashboardTables == null)
                    result.DashboardTables = new Drg.M3.Bll.Common.UserSettingBll(null).GenerateDefaults();
                if (result.DashboardFilters == null)
                    result.DashboardFilters = new Drg.M3.Bll.Common.UserSettingBll(null).DefaultDashboardFilters(parent);
                return result;
            }
            else
            {
                return new UserSettings() 
                { 
                    Parent = parent,
                    DashboardTables = new Drg.M3.Bll.Common.UserSettingBll(null).GenerateDefaults(),
                    DashboardFilters = new Drg.M3.Bll.Common.UserSettingBll(null).DefaultDashboardFilters(parent),
                    GridSettings = new Dictionary<Tuple<string, string, int>, string>()
                };
            }
        }
    }

    public enum FindingsTab
    {
        [Description("All Findings at this Organization")]
        [CustomDescription("observation", "All Observations at this Organization")]
        [CustomDescription("poam", "POAMS from Findings at this Organization")]
        [CustomDescription("audit", "All Findings")]
        OrgFindings = 0,
        [Description("My Findings Only")]
        [CustomDescription("observation", "My Observations Only")]
        MyFindings = 1,
        [Description("Assigned Findings from other Organizations")]
        [CustomDescription("observation", "Assigned Observations from other Organizations")]
        [CustomDescription("poam", "POAMS from Assigned Findings from other Organizations")]
        OtherOrgFindings = 2
    }

    public enum FindingsClosedFilter
    {
        [Description("Open Findings")]
        [CustomDescription("observation", "Open Observations")]
        Open = 0,
        [Description("Closed Findings")]
        [CustomDescription("observation", "Closed Observations")]
        Closed = 1,
        [Description("Open & Closed Findings")]
        [CustomDescription("observation", "Open & Closed Observations")]
        Both = 2
    }

    [DataContract]
    public class DashboardTable
    {
        [DataMember]
        public bool SubordinateData { get; set; }
        [DataMember]
        public int? PageSize { get; set; }
        [DataMember]
        public string Name;
        [DataMember]
        public string DisplayName;
        [DataMember]
        public bool Visible;
        [DataMember]
        public int SortOrder;

        public DashboardTable(string name, string displayName, bool visible, int sortOrder, int pageSize = 10, bool subordinateData = false)
        {
            this.Name = name;
            this.DisplayName = displayName;
            this.Visible = visible;
            this.SortOrder = sortOrder;
            this.SubordinateData = subordinateData;
            this.PageSize = pageSize;
        }
    }
}
