using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

using Drg.M3.Bll.Reports;
using Drg.M3.Dal;
using Drg.M3.Modules;

namespace Drg.M3.Domain
{
    /// <summary>
    /// Note class
    /// </summary>
    [Module(typeof(Modules.QA))]
    [AutoMapped]
    public class Note : Communication, IHasOrganization
    {
        protected Note() : base() { }
#nullable enable
        public Note(Organization org)
            : base(org)
        { }
        
        /// <summary>
        /// Note type
        /// </summary>
        [DisplayName("Type")]
        public virtual Reference? NoteType { get; set; }
        /// <summary>
        /// System-generated message
        /// </summary>
        public virtual bool IsMessage { get; set; }
    }
}