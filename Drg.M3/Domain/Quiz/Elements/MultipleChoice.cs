using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Drg.Core;
using System.Collections.Specialized;
using Drg.M3.Dal;
using System.Web;
using System.ComponentModel;

namespace Drg.M3.Domain.Quiz.Elements
{
    /// <summary>
    /// Multiple choice question
    /// </summary>
    [Description("Multiple Choice")]
    public class MultipleChoice : CommentableQuestion, IHasOptions, IScorableQuestion
    {
        /// <summary>
        /// Collection of options for this question
        /// </summary>
        public virtual ISet<Option> Options { get; set; } = new HashSet<Option>();
        /// <summary>
        /// Collection of actual answers to this question
        /// </summary>
        public virtual ISet<Answer> Answers { get; set; } = new HashSet<Answer>();
        public override ColumnType ColumnType { get { return ColumnType.Option; } }

        public override object GetViewData(Quiz quiz)
        {
            var op = quiz.GetValue(this) as Option;
            if (op != null) return op.Id as int?;
            else return null;
        }
        public override object ParseValue(NameValueCollection form, string elementName)
        {
            if (!form.AllKeys.Contains(elementName))
                throw new Exception("Form key '" + elementName + "' expected");

            int? id = Parse.NullableInteger(form[elementName]);
            if (id == null)
                return null;
            else
                return Options.SingleOrDefault(op => op.Id == id.Value);
        }
        public virtual bool IsScored
        {
            get
            {
                return Options.Any(o => o.Score != null);
            }
        }
        public virtual float Weight
        {
            get
            {
                float result;
                if (ParametersDict["weight"] != null && float.TryParse(ParametersDict["weight"], out result))
                    return result;
                else
                    return 1;
            }
        }
        public override Element Clone()
        {
            var newEl = base.Clone() as MultipleChoice;
           
            foreach (var option in Options)
            {
                var newOption = option.Clone();
                newOption.Element = newEl;
                newOption.Original = option;
                newEl.Options.Add(newOption);
            }
            return newEl;
        }
    }
}
