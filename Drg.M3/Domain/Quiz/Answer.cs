using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;

namespace Drg.M3.Domain.Quiz
{
    /// <summary>
    /// Answer to a quiz question
    /// </summary>
    [DisplayName("Quiz Answer")]
    [NonQueryable]
    public class Answer : PersistentObject
    {
        protected Answer() { }
        public Answer(Question question, Quiz quiz)
        {
            this.Quiz = quiz;
            this.Question = question;
        }
        /// <summary>
        /// The quiz this answer is related to
        /// </summary>
        public virtual Quiz Quiz { get; set; }
        /// <summary>
        /// The question this answer answers
        /// </summary>
        public virtual Question Question { get; set; }
        /// <summary>
        /// The selected option
        /// </summary>
        public virtual Option Option { get; set; }
        /// <summary>
        /// The score this answer gets
        /// </summary>
        public virtual decimal? Score { get; set; }
        /// <summary>
        /// The text of this answer
        /// </summary>
        public virtual string AnswerText { get; set; }
        /// <summary>
        /// Additional comments
        /// </summary>
        public virtual string Comment { get; set; }
        //public virtual Distribution Responsible { get; set; }
    }
}
