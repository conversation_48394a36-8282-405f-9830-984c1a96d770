using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Drg.M3.Bll.Reports;
using System.Runtime.Serialization;
using Drg.Core;
using Drg.M3.Dal;
using Drg.M3.Bll;
using Drg.M3.Client;
using System.Text.RegularExpressions;
using Drg.M3.Bll.QA;
using Drg.M3.Modules;
using System.ComponentModel;

namespace Drg.M3.Domain.QA
{
    public enum DateScheme : int
    {
        None = 0,
        [Description("Start & End Date")]
        StartAndEndDate = 1,
        Date = 2,
        [Description("Date & Time")]
        DateAndTime = 3,
    }
    public interface IChecklistTemplate
    {
        string Name { get; }
        string Instructions { get; }
        ChecklistType ChecklistType { get; }
        Organization Organization { get; }
        Reference Media { get; }
    }
    /// <summary>
    /// Checklist template class
    /// </summary>
    [Module(typeof(Modules.QA))]
    public class ChecklistTemplate : CommonBusinessObject, IHasAssociation, IHasXml, IHasOrganization, IChecklistTemplate, IShareable, IHasNotes, ICustomDM, IAuditable
    {
        protected ChecklistTemplate() { }
        public ChecklistTemplate(string name, ChecklistType checklistType, Organization org) 
        {
            this.Name = name;
            this.ChecklistType = checklistType;
            this.Organization = org;
            this.CurrentVersion = new ChecklistTemplateVersion() { ChecklistTemplate = this };
            DM = CurrentVersion.Modified = NiftyDate.Now;
        }
        public virtual string UserCreated { get; set; }
        /// <summary>
        /// Instructional text
        /// </summary>
        public virtual string Instructions { get; set; }
        /// <summary>
        /// Checklist type
        /// </summary>
        public virtual ChecklistType ChecklistType { get; set; }
        /// <summary>
        /// This checklist template is shared with other organizations
        /// </summary>
        public virtual bool IsShared { get; set; }
        /// <summary>
        /// Expando XML field
        /// </summary>
        public virtual string Xml { get; set; }
        /// <summary>
        /// Legacy Template fingerprint
        /// </summary>
        public virtual string w_LegacyFingerprint { get; set; }
        /// <summary>
        /// The related media
        /// </summary>
        public virtual Reference Media { get; set; }
        /// <summary>
        /// Collection of inspections
        /// </summary>
        public virtual ISet<Inspection> Inspections { get; set; } = new HashSet<Inspection>();

        ///// <summary>
        ///// Supports dup-fixing utility
        ///// </summary>
        //public virtual bool DupIdsFixed { get; set; }

        ///// <summary>
        ///// Supports dup-fixing utility
        ///// </summary>
        //public virtual int DupQuestions { get; set; }

        ///// <summary>
        ///// Supports dup-fixing utility
        ///// </summary>
        //public virtual int DupChecklists { get; set; }

        /// <summary>
        /// All versions of this template
        /// </summary>
        public virtual ISet<ChecklistTemplateVersion> Versions { get; set; } = new HashSet<ChecklistTemplateVersion>();

        public virtual ChecklistTemplateVersion CurrentVersion { get; set; }

        [NonReportable]
        public virtual ChecklistTemplateVersion Settings => CurrentVersion;
        public virtual ISet<ChecklistAlert> Alerts { get { return Settings.Alerts; } }
        public virtual ISet<ChecklistQuestion> Questions { get { return Settings.Questions; } }
        public virtual ISet<ChecklistCustomField> CustomFields { get { return Settings.CustomFields; } }

        /// <summary>
        /// Collection of EMS objects related to this checklist template (Practices, Permits and Equipment(Assets))
        /// </summary>
        public virtual ISet<DeletableBusinessObject> EmsObjects { get; set; } = new HashSet<DeletableBusinessObject>();
        /// <summary>
        /// Collection of Object2AuditTemplates. For queries only
        /// </summary>
        public virtual ISet<Object2ChecklistTemplate> Object2ChecklistTemplates { get; protected set; }
        /// <summary>
        /// Collection of selected types related to the EMS Objects (AssetType or PracticeType)
        /// </summary>
        public virtual ISet<Reference> EmsObjectTypes { get; set; } = new HashSet<Reference>();

        public override string ToString()
        {
            return this.Name;
        }

        public virtual void AssertNotLocked()
        {
            if (CurrentVersion.IsLocked)
            {
                throw new InvalidOperationException("Cannot modify a locked checklist template version");
            }
        }
    }

    [Module(typeof(Modules.QA))]
    public class ChecklistTemplateVersion : PersistentObject
    {
        [Obsolete]
        public virtual string Xml { get; set; }

        public virtual NiftyDate Created { get; set; } = NiftyDate.Now;
        public virtual NiftyDate Modified { get; set; }

        public virtual int VersionNumber { get; set; } = 1;
        public virtual bool IsLocked { get; set; }

        public virtual ChecklistTemplate ChecklistTemplate { get; set; }

        public virtual DateScheme DateScheme { get; set; }
        public virtual bool RestartNumbering { get; set; }
        public virtual bool Building { get; set; }
        public virtual bool Room { get; set; }
        public virtual bool Location { get; set; }
        public virtual bool POCName { get; set; }
        public virtual bool ContactInfo { get; set; }
        public virtual bool SignatureBlock { get; set; }
        public virtual bool AdditionalComments { get; set; }
        public virtual bool Inspector { get; set; }
        public virtual bool SubmissionDate { get; set; }
        public virtual bool EvaluatedItem { get; set; }
        public virtual string Jurisdiction { get; set; }
        public virtual ISet<ChecklistAlert> Alerts { get; set; } = new HashSet<ChecklistAlert>(); 
        public virtual ISet<ChecklistQuestion> Questions { get; set; } = new HashSet<ChecklistQuestion>();
        public virtual ISet<ChecklistCustomField> CustomFields { get; set; } = new HashSet<ChecklistCustomField>(); 

        public virtual int CountCompletedChecklists { get; protected set; }
        public virtual int CountDraftChecklists { get; protected set; }

        public virtual ChecklistTemplateVersion CloneTo(ChecklistTemplate template, bool cloneAlerts = true)
        {
            var checklist = new ChecklistTemplateVersion()
            {
                ChecklistTemplate = template,
                DateScheme = this.DateScheme,
                RestartNumbering = this.RestartNumbering,
                Building = this.Building,
                Room = this.Room,
                Location = this.Location,
                POCName = this.POCName,
                ContactInfo = this.ContactInfo,
                SignatureBlock = this.SignatureBlock,
                AdditionalComments = this.AdditionalComments,
                Inspector = this.Inspector,
                SubmissionDate = this.SubmissionDate,
                EvaluatedItem = this.EvaluatedItem,
                Jurisdiction = this.Jurisdiction,
                VersionNumber = template.Versions.Any() ? template.Versions.Max(x => x.VersionNumber) + 1 : 1,
                Created = NiftyDate.Now
            };

            var questionMap = new Dictionary<ChecklistQuestion, ChecklistQuestion>();
            foreach (var q in Questions)
            {
                var clone = q.CloneTo(checklist);
                checklist.Questions.Add(clone);
                questionMap.Add(q, clone);
            }

            if (cloneAlerts)
            {
                foreach (var a in Alerts)
                {
                    var clone = a.CloneTo(checklist);
                    checklist.Alerts.Add(clone);
                    foreach (var ac in a.Criteria)
                        clone.Criteria.Add(ac.CloneTo(clone, ac.Question == null ? null : questionMap[ac.Question]));
                }
            }

            var customFieldMap = new Dictionary<ChecklistCustomField, ChecklistCustomField>();
            foreach (var cf in CustomFields)
            {
                var clone = cf.CloneTo(checklist);
                checklist.CustomFields.Add(clone);
                customFieldMap.Add(cf, clone);
            }

            return checklist;
        }
    }

    [Module(typeof(Modules.QA))]
    public class ChecklistAlert : PersistentObject, IAlertRule
    {
        protected ChecklistAlert() { }
        public ChecklistAlert(ChecklistTemplateVersion settings) 
        {
            this.ChecklistTemplateVersion = settings;
        }

        CustomAlertRuleType IAlertRule.AlertType { get; set; } = CustomAlertRuleType.Default;

        public virtual ChecklistTemplateVersion ChecklistTemplateVersion { get; set; }

        public virtual string Message { get; set; }
        public virtual bool Inactive { get; set; }
        public virtual ISet<ChecklistAlertCriterion> Criteria { get; set; } = new HashSet<ChecklistAlertCriterion>();
        public virtual string Type { get; set; }

        public virtual Distribution Distribution { get; set; }

        public virtual ChecklistAlert CloneTo(ChecklistTemplateVersion version)
        {
            return new ChecklistAlert()
            {
                ChecklistTemplateVersion = version,
                Message = Message,
                Inactive = Inactive,
                Type = Type,
                Distribution = Distribution
            };
        }

        public virtual bool Evaluate(PersistentObject record)
        {
            var i = record as Inspection;
            if (i == null) return false;

            if (Type == "checklist")
            {
                var cr = Criteria.Single().Answer;
                return i.Answers.Values.Any(b => b == cr);
            }
            else// if (Type == "question")
            {
                return Criteria.Any(cr => i.Answers[cr.Question.Id] == cr.Answer);
            }
        }

        public virtual Alert BuildAlert(IEnumerable<PersistentObject> records)
        {
            var i = records.First() as Inspection;

            return new Alert()
            {
                Message = this.Message + string.Format(@"
--------------------------------------------------
Organization: {0}
EMS Item: {1}
Checklist URL: {2}", i.Organization.Name, i.EmsObject?.Name, "https://" + M3Context.Current.Association.Domain + "/QA/Checklist/Inspection/" + i.Id),
                Distribution = Distribution,
                AlertRuleId = GetType().FullName,
                RecordId = i.Id,
                Subject = "EMSWeb2 - Checklist alert",
                Association = i.Association,
                Organization = i.Organization,
                Notify = true
            };
        }

        /// <summary>
        /// This method should not be called. Checklist template alerts are evaluated manually by QAController.Inspection().
        /// </summary>
        /// <param name="session"></param>
        /// <returns></returns>
        [Obsolete("This method should not be called. Checklist template alerts are evaluated manually by QAController.Inspection().")]
        public virtual IList<PersistentObject> GetRecords(NHibernate.ISession session) { return null; }
        
        public virtual bool SyndicateAlert() { return false; }
        public virtual bool ShouldEvaluate() { return true; }
    }

    [Module(typeof(Modules.QA))]
    public class ChecklistAlertCriterion : PersistentObject
    {
        protected ChecklistAlertCriterion() { }
        public ChecklistAlertCriterion(ChecklistAlert alert)
        {
            Alert = alert;
        }

        public virtual ChecklistAlert Alert { get; set; }

        public virtual ChecklistAnswer Answer { get; set; }
        public virtual ChecklistQuestion Question { get; set; }

        public virtual ChecklistAlertCriterion CloneTo(ChecklistAlert alert, ChecklistQuestion question)
        {
            return new ChecklistAlertCriterion()
            {
                Alert = alert,
                Question = question,
                Answer = Answer
            };
        }
    }

    [Module(typeof(Modules.QA))]
    public class ChecklistQuestion : PersistentObject
    {
        protected ChecklistQuestion() { }
        public ChecklistQuestion(ChecklistTemplateVersion settings)
        {
            ChecklistTemplateVersion = settings;
            Position = settings.Questions.Count == 0 ? 1 : settings.Questions.Max(x => x.Position) + 1;
        }

        public virtual ChecklistTemplateVersion ChecklistTemplateVersion { get; set; }

        public virtual int Position { get; set; }
        public virtual string Name { get; set; }
        public virtual string Category { get; set; }
        public virtual string ItemId { get; set; }
        public virtual string CitationCode { get; set; }
        public virtual string ReferenceDetails { get; set; }
        public virtual int? LegacyId { get; set; }

        public virtual ChecklistQuestion CloneTo(ChecklistTemplateVersion version)
        {
            return new ChecklistQuestion()
            {
                ChecklistTemplateVersion = version,
                Name = Name,
                Category = Category,
                ItemId = ItemId,
                CitationCode = CitationCode,
                ReferenceDetails = ReferenceDetails,
                LegacyId = LegacyId
            };
        }

        static Regex _sanitizeRegex = new Regex("\r?\n(\r?\n)+", RegexOptions.Compiled | RegexOptions.Multiline);
        public virtual string SanitizeReferenceDetails()
        {
            return ReferenceDetails == null ? null : _sanitizeRegex.Replace(ReferenceDetails, "\r\n\r\n");
        }

        public virtual string ItemIdSort
        {
            get
            {
                if (string.IsNullOrEmpty(ItemId)) 
                    return ItemId;

                return string.Join(".", ItemId.Split(new[] { '-', '.' }).Select(x => x.PadLeft(7, '0')));
            }
        }
    }

    [Module(typeof(Modules.QA))]
    public class ChecklistCustomField : PersistentObject
    {
        protected ChecklistCustomField() { }
        public ChecklistCustomField(ChecklistTemplateVersion settings)
        {
            ChecklistTemplateVersion = settings;
            Position = settings.CustomFields.Count == 0 ? 1 : settings.CustomFields.Max(x => x.Position) + 1;
        }

        public virtual ChecklistTemplateVersion ChecklistTemplateVersion { get; set; }

        public virtual int Position { get; set; }
        public virtual bool Top { get; set; }
        public virtual string Name { get; set; }
        public virtual int? LegacyId { get; set; }

        public virtual ChecklistCustomField CloneTo(ChecklistTemplateVersion version)
        {
            return new ChecklistCustomField()
            {
                ChecklistTemplateVersion = version,
                Name = Name,
                Top = Top,
                LegacyId = LegacyId
            };
        }
    }

    public enum ChecklistType
    {
        [Description("Simple Checklist")]
        Simple = 0,
        [Description("Planning Checklist")]
        Planning = 1,
        [Description("Compliance Audit")]
        ComplianceAudit = 2,
        [Description("EMS Audit")]
        EmsAudit = 3
    }
}