using Drg.M3.Modules;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Drg.M3.Domain.QA
{
    /// <summary>
    /// Requested document
    /// </summary>
    [Module(typeof(Modules.QA))]
    [NonReportable]
    public class RequestedDocument : CommonBusinessObject
    {
        /// <summary>
        /// Document is shared
        /// </summary>
        public virtual bool IsShared { get; set; }
        /// <summary>
        /// Document header
        /// </summary>
        public virtual string Header { get; set; }
        /// <summary>
        /// Collection of Lists
        /// </summary>
        public virtual ISet<DocumentLevel> Levels { get; set; } = new HashSet<DocumentLevel>();
    }
}
