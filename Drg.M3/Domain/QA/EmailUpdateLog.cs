using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Drg.M3.Dal;

namespace Drg.M3.Domain.QA
{
    [AutoMapped]
    public class EmailUpdateLog : PersistentObject, IHasAssociation
    {
        public virtual UserLogin UserLogin { get; set; }
        public virtual string OldEmail { get; set; }
        public virtual string NewEmail { get; set; }
        public virtual DateTimeOffset ChangeDate { get; set; }
        public virtual Association Association { get; set; }
    }
}
