using Drg.M3.Dal;
using Drg.M3.Modules;

namespace Drg.M3.Domain.QA
{
    [AutoMapped]
    [Module(typeof(Modules.QA))]
    public class ChecklistProfile : CommonBusinessObject, IHasOrganization, IShareable
    {
        public ChecklistProfile() { }

        public ChecklistProfile(string name, ChecklistType checklistType, Organization organization)
        {
            this.Name = name;
            this.ChecklistType = checklistType;
            this.Organization = organization;
        }

        public virtual ChecklistType ChecklistType { get; set; }
        public virtual string Instructions { get; set; }
        /// <summary>
        /// This checklist template is shared with other organizations
        /// </summary>
        public virtual bool IsShared { get; set; }
        /// <summary>
        /// The related media
        /// </summary>
        public virtual Reference Media { get; set; }

        public virtual DateScheme DateScheme { get; set; }
        public virtual bool RestartNumbering { get; set; }
        public virtual bool Building { get; set; }
        public virtual bool Room { get; set; }
        public virtual bool Location { get; set; }
        public virtual bool POCName { get; set; }
        public virtual bool ContactInfo { get; set; }
        public virtual bool SignatureBlock { get; set; }
        public virtual bool AdditionalComments { get; set; }
        public virtual bool Inspector { get; set; }
        public virtual bool SubmissionDate { get; set; }
        public virtual bool EvaluatedItem { get; set; }
        public virtual string Jurisdiction { get; set; }
    }
}
