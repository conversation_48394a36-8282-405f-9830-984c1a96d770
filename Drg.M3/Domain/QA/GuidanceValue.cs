using Drg.M3.Dal;
using Drg.M3.Modules;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Web;

namespace Drg.M3.Domain.QA
{
    [DisplayName("Guidance Value")]
    [AutoMapped]
    public class GuidanceValue : ModifiableBusinessObject, IHasOrganization
    {
        [Required]
        public virtual GuidanceRow Row { get; set; }

        [Required]
        public virtual GuidanceColumn Column { get; set; }

        [Required]
        public virtual Organization Organization { get; set; }

        [MaxLength(int.MaxValue)]
        public virtual string StringValue { get; set; }

        public virtual DateTime? DateValue { get; set; }

        public virtual Document DocumentValue { get; set; }

        public virtual object GetValue()
        {
            if (Column.Type == GuidanceColumnType.Document)
                return DocumentValue;
            else if (Column.Type == GuidanceColumnType.Date)
                return DateValue;
            else if (Column.Type == GuidanceColumnType.Text || Column.Type == GuidanceColumnType.Html)
                return StringValue;
            throw new InvalidOperationException("Unrecognized GuidanceColumnType");
        }

        public virtual void SetValue(object value)
        {
            if (Column.Type == GuidanceColumnType.Document)
                DocumentValue = value as Document;
            else if (Column.Type == GuidanceColumnType.Date)
                DateValue = value as DateTime?;
            else if (Column.Type == GuidanceColumnType.Text || Column.Type == GuidanceColumnType.Html)
                StringValue = value as string;
            throw new InvalidOperationException("Unrecognized GuidanceColumnType");
        }
    }
}


