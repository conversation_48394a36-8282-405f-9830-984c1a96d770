using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20250623142129)]
    public partial class ChecklistProfile : M3Migration
    {
        public override void Up()
        {
            Delete.Column("Category").FromTable("POL_InventoryItem");

            Create.Table("QA_ChecklistProfile")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_ChecklistProfile")
                .WithColumn("ChecklistType").AsInt32().NotNullable()
                .WithColumn("Instructions").AsString(255).Nullable()
                .WithColumn("IsShared").AsBoolean().NotNullable()
                .WithColumn("DateScheme").AsInt32().NotNullable()
                .WithColumn("RestartNumbering").AsBoolean().NotNullable()
                .WithColumn("Building").AsBoolean().NotNullable()
                .WithColumn("Room").AsBoolean().NotNullable()
                .WithColumn("Location").AsBoolean().NotNullable()
                .WithColumn("POCName").AsBoolean().NotNullable()
                .WithColumn("ContactInfo").AsBoolean().NotNullable()
                .WithColumn("SignatureBlock").AsBoolean().NotNullable()
                .WithColumn("AdditionalComments").AsBoolean().NotNullable()
                .WithColumn("Inspector").AsBoolean().NotNullable()
                .WithColumn("SubmissionDate").AsBoolean().NotNullable()
                .WithColumn("EvaluatedItem").AsBoolean().NotNullable()
                .WithColumn("Jurisdiction").AsString(255).Nullable()
                .WithColumn("IsInactive").AsBoolean().NotNullable()
                .WithColumn("Name").AsString(255).NotNullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("MediaId").AsInt32().Nullable()
                .WithColumn("OrganizationId").AsInt32().NotNullable()
                .WithColumn("AssociationId").AsInt32().NotNullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.ForeignKey("FK_ChecklistProfile_Media").FromTable("QA_ChecklistProfile").ForeignColumns("MediaId")
                .ToTable("COR_Reference").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfile_Organization").FromTable("QA_ChecklistProfile").ForeignColumns("OrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfile_Association").FromTable("QA_ChecklistProfile").ForeignColumns("AssociationId")
                .ToTable("COR_Association").PrimaryColumns("Id");

            Create.Index("IX_MediaId").OnTable("QA_ChecklistProfile").WithOptions().NonClustered()
                .OnColumn("MediaId").Ascending();

            Create.Index("IX_OrganizationId").OnTable("QA_ChecklistProfile").WithOptions().NonClustered()
                .OnColumn("OrganizationId").Ascending();

            Create.Index("IX_AssociationId").OnTable("QA_ChecklistProfile").WithOptions().NonClustered()
                .OnColumn("AssociationId").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_AssociationId").OnTable("QA_ChecklistProfile");

            Delete.Index("IX_OrganizationId").OnTable("QA_ChecklistProfile");

            Delete.Index("IX_MediaId").OnTable("QA_ChecklistProfile");

            Delete.ForeignKey("FK_ChecklistProfile_Association").OnTable("QA_ChecklistProfile");

            Delete.ForeignKey("FK_ChecklistProfile_Organization").OnTable("QA_ChecklistProfile");

            Delete.ForeignKey("FK_ChecklistProfile_Media").OnTable("QA_ChecklistProfile");

            Delete.Table("QA_ChecklistProfile");

            Create.Column("Category").OnTable("POL_InventoryItem").AsString(255).Nullable();
        }
    }
}
