using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20250624090052)]
    public partial class ChecklistProfile : M3Migration
    {
        public override void Up()
        {
            Create.Column("ChecklistProfileSectionId").OnTable("ENV_ChecklistQuestion").AsInt32().Nullable();

            Create.Column("IsInactive").OnTable("POL_InspectionCategory").AsBoolean().NotNullable();

            Delete.Column("Category").FromTable("POL_InventoryItem");

            Create.Table("QA_ChecklistProfileSection")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_ChecklistProfileSection")
                .WithColumn("SectionName").AsString(255).Nullable()
                .WithColumn("FilterCriteria").AsString(255).Nullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("ChecklistProfileId").AsInt32().NotNullable()
                .WithColumn("AssociationId").AsInt32().Nullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.Table("QA_ChecklistProfile")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_ChecklistProfile")
                .WithColumn("ChecklistType").AsInt32().NotNullable()
                .WithColumn("Instructions").AsString(255).Nullable()
                .WithColumn("IsShared").AsBoolean().NotNullable()
                .WithColumn("DateScheme").AsInt32().NotNullable()
                .WithColumn("RestartNumbering").AsBoolean().NotNullable()
                .WithColumn("Building").AsBoolean().NotNullable()
                .WithColumn("Room").AsBoolean().NotNullable()
                .WithColumn("Location").AsBoolean().NotNullable()
                .WithColumn("POCName").AsBoolean().NotNullable()
                .WithColumn("ContactInfo").AsBoolean().NotNullable()
                .WithColumn("SignatureBlock").AsBoolean().NotNullable()
                .WithColumn("AdditionalComments").AsBoolean().NotNullable()
                .WithColumn("Inspector").AsBoolean().NotNullable()
                .WithColumn("SubmissionDate").AsBoolean().NotNullable()
                .WithColumn("EvaluatedItem").AsBoolean().NotNullable()
                .WithColumn("Jurisdiction").AsString(255).Nullable()
                .WithColumn("IsInactive").AsBoolean().NotNullable()
                .WithColumn("Name").AsString(255).NotNullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("MediaId").AsInt32().Nullable()
                .WithColumn("OrganizationId").AsInt32().NotNullable()
                .WithColumn("AssociationId").AsInt32().NotNullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.ForeignKey("FK_9DABF82D").FromTable("ENV_ChecklistQuestion").ForeignColumns("ChecklistProfileSectionId")
                .ToTable("QA_ChecklistProfileSection").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfileSection_ChecklistProfile").FromTable("QA_ChecklistProfileSection").ForeignColumns("ChecklistProfileId")
                .ToTable("QA_ChecklistProfile").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfileSection_Association").FromTable("QA_ChecklistProfileSection").ForeignColumns("AssociationId")
                .ToTable("COR_Association").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfile_Media").FromTable("QA_ChecklistProfile").ForeignColumns("MediaId")
                .ToTable("COR_Reference").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfile_Organization").FromTable("QA_ChecklistProfile").ForeignColumns("OrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfile_Association").FromTable("QA_ChecklistProfile").ForeignColumns("AssociationId")
                .ToTable("COR_Association").PrimaryColumns("Id");

            Create.Index("IX_ChecklistProfileSectionId").OnTable("ENV_ChecklistQuestion").WithOptions().NonClustered()
                .OnColumn("ChecklistProfileSectionId").Ascending();

            Create.Index("IX_ChecklistProfileId").OnTable("QA_ChecklistProfileSection").WithOptions().NonClustered()
                .OnColumn("ChecklistProfileId").Ascending();

            Create.Index("IX_AssociationId").OnTable("QA_ChecklistProfileSection").WithOptions().NonClustered()
                .OnColumn("AssociationId").Ascending();

            Create.Index("IX_MediaId").OnTable("QA_ChecklistProfile").WithOptions().NonClustered()
                .OnColumn("MediaId").Ascending();

            Create.Index("IX_OrganizationId").OnTable("QA_ChecklistProfile").WithOptions().NonClustered()
                .OnColumn("OrganizationId").Ascending();

            Create.Index("IX_AssociationId").OnTable("QA_ChecklistProfile").WithOptions().NonClustered()
                .OnColumn("AssociationId").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_AssociationId").OnTable("QA_ChecklistProfile");

            Delete.Index("IX_OrganizationId").OnTable("QA_ChecklistProfile");

            Delete.Index("IX_MediaId").OnTable("QA_ChecklistProfile");

            Delete.Index("IX_AssociationId").OnTable("QA_ChecklistProfileSection");

            Delete.Index("IX_ChecklistProfileId").OnTable("QA_ChecklistProfileSection");

            Delete.Index("IX_ChecklistProfileSectionId").OnTable("ENV_ChecklistQuestion");

            Delete.ForeignKey("FK_ChecklistProfile_Association").OnTable("QA_ChecklistProfile");

            Delete.ForeignKey("FK_ChecklistProfile_Organization").OnTable("QA_ChecklistProfile");

            Delete.ForeignKey("FK_ChecklistProfile_Media").OnTable("QA_ChecklistProfile");

            Delete.ForeignKey("FK_ChecklistProfileSection_Association").OnTable("QA_ChecklistProfileSection");

            Delete.ForeignKey("FK_ChecklistProfileSection_ChecklistProfile").OnTable("QA_ChecklistProfileSection");

            Delete.ForeignKey("FK_9DABF82D").OnTable("ENV_ChecklistQuestion");

            Delete.Table("QA_ChecklistProfile");

            Delete.Table("QA_ChecklistProfileSection");

            Create.Column("Category").OnTable("POL_InventoryItem").AsString(255).Nullable();

            Delete.Column("IsInactive").FromTable("POL_InspectionCategory");

            Delete.Column("ChecklistProfileSectionId").FromTable("ENV_ChecklistQuestion");
        }
    }
}
