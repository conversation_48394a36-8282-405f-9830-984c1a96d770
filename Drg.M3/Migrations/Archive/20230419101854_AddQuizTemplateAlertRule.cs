using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20230419101854)]
    public partial class AddQuizTemplateAlertRule : M3Migration
    {
        public override void Up()
        {
            Create.Table("COR_QuizTemplateAlertRule_QuestionValues")
                .WithColumn("QuizTemplateAlertRuleId").AsInt32().NotNullable().PrimaryKey("PK_COR_QuizTemplateAlertRule_QuestionValues")
                .WithColumn("Value").AsString(255).NotNullable()
                .WithColumn("FieldName").AsString(255).NotNullable().PrimaryKey("PK_COR_QuizTemplateAlertRule_QuestionValues");

            Create.ForeignKey("FK_33CDC5BD").FromTable("COR_QuizTemplateAlertRule_QuestionValues").ForeignColumns("QuizTemplateAlertRuleId")
                .ToTable("COR_CustomAlertRule").PrimaryColumns("Id");

            Create.Index("IX_QuizTemplateAlertRuleId").OnTable("COR_QuizTemplateAlertRule_QuestionValues").WithOptions().NonClustered()
                .OnColumn("QuizTemplateAlertRuleId").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_QuizTemplateAlertRuleId").OnTable("COR_QuizTemplateAlertRule_QuestionValues");

            Delete.ForeignKey("FK_33CDC5BD").OnTable("COR_QuizTemplateAlertRule_QuestionValues");

            Delete.Table("COR_QuizTemplateAlertRule_QuestionValues");
        }
    }
}
