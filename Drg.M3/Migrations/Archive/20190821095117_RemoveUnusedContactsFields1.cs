using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20190821095117)]
    public partial class RemoveUnusedContactsFields1 : M3Migration
    {
        public override void Up()
        {
            Delete.ForeignKey("FK_5190F836").OnTable("CON_Company");

            Delete.Column("DefaultRoleType").FromTable("COR_Entity");

            Delete.Column("OkToFaxOnFile").FromTable("CON_Person");

            Delete.Column("OkToEmailOnFile").FromTable("CON_Person");

            Delete.Column("TaxId").FromTable("CON_Company");

            Delete.Column("NoPhoneEmployees").FromTable("CON_Company");

            Delete.Column("NoFaxEmployees").FromTable("CON_Company");

            Delete.Column("NoEmailEmployees").FromTable("CON_Company");

            Delete.Column("NoMailEmployees").FromTable("CON_Company");

            Delete.Column("OldPK").FromTable("CON_Company");

            Delete.Column("ParentCompanyId").FromTable("CON_Company");
        }

        public override void Down()
        {
            Create.Column("ParentCompanyId").OnTable("CON_Company").AsInt32().Nullable();

            Create.Column("OldPK").OnTable("CON_Company").AsString(255).Nullable();

            Create.Column("NoMailEmployees").OnTable("CON_Company").AsBoolean().NotNullable();

            Create.Column("NoEmailEmployees").OnTable("CON_Company").AsBoolean().NotNullable();

            Create.Column("NoFaxEmployees").OnTable("CON_Company").AsBoolean().NotNullable();

            Create.Column("NoPhoneEmployees").OnTable("CON_Company").AsBoolean().NotNullable();

            Create.Column("TaxId").OnTable("CON_Company").AsString(255).Nullable();

            Create.Column("OkToEmailOnFile").OnTable("CON_Person").AsBoolean().Nullable();

            Create.Column("OkToFaxOnFile").OnTable("CON_Person").AsBoolean().Nullable();

            Create.Column("DefaultRoleType").OnTable("COR_Entity").AsString(100).Nullable();

            Create.ForeignKey("FK_5190F836").FromTable("CON_Company").ForeignColumns("ParentCompanyId")
                .ToTable("CON_Company").PrimaryColumns("EntityId");
        }
    }
}
