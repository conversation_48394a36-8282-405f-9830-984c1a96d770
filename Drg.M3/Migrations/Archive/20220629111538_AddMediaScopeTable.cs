using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20220629111538)]
    public partial class AddMediaScopeTable : M3Migration
    {
        public override void Up()
        {
            Create.Table("ENV_AuditMediaScopeStatement")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_ENV_AuditMediaScopeStatement")
                .WithColumn("Statement").AsString(int.MaxValue).Nullable()
                .WithColumn("PercentRange").AsInt32().Nullable()
                .WithColumn("AuditId").AsInt32().Nullable()
                .WithColumn("MediaId").AsInt32().Nullable();

            Create.ForeignKey("FK_AuditMediaScopeStatement_Audit").FromTable("ENV_AuditMediaScopeStatement").ForeignColumns("AuditId")
                .ToTable("ENV_Schedule").PrimaryColumns("Id");

            Create.ForeignKey("FK_AuditMediaScopeStatement_Media").FromTable("ENV_AuditMediaScopeStatement").ForeignColumns("MediaId")
                .ToTable("COR_Reference").PrimaryColumns("Id");

            Create.Index("IX_AuditId").OnTable("ENV_AuditMediaScopeStatement").WithOptions().NonClustered()
                .OnColumn("AuditId").Ascending();

            Create.Index("IX_MediaId").OnTable("ENV_AuditMediaScopeStatement").WithOptions().NonClustered()
                .OnColumn("MediaId").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_MediaId").OnTable("ENV_AuditMediaScopeStatement");

            Delete.Index("IX_AuditId").OnTable("ENV_AuditMediaScopeStatement");

            Delete.ForeignKey("FK_AuditMediaScopeStatement_Media").OnTable("ENV_AuditMediaScopeStatement");

            Delete.ForeignKey("FK_AuditMediaScopeStatement_Audit").OnTable("ENV_AuditMediaScopeStatement");

            Delete.Table("ENV_AuditMediaScopeStatement");
        }
    }
}
