using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20200513131452)]
    public partial class AddJONSuccessor : M3Migration
    {
        public override void Up()
        {
            Create.Column("SuccessorId").OnTable("HW_JobOrder").AsInt32().Nullable();

            Create.ForeignKey("FK_6CACEDDE").FromTable("HW_JobOrder").ForeignColumns("SuccessorId")
                .ToTable("HW_JobOrder").PrimaryColumns("Id");
        }

        public override void Down()
        {
            Delete.ForeignKey("FK_6CACEDDE").OnTable("HW_JobOrder");

            Delete.Column("SuccessorId").FromTable("HW_JobOrder");
        }
    }
}
