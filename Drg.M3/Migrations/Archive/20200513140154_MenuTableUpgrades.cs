using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20200513140154)]
    public partial class MenuTableUpgrades : M3Migration
    {
        public override void Up()
        {
            Create.Column("AllowClone").OnTable("COR_Menu2List").AsBoolean().NotNullable().WithDefaultValue(false);

            Create.Column("IsPublic").OnTable("COR_Menu2List").AsBoolean().NotNullable().WithDefaultValue(false);

            Create.Column("HideColumn").OnTable("QUZ_Element").AsBoolean().NotNullable().WithDefaultValue(false);

            Execute.Sql(@"
insert SEC_Permission (FunctionKey,GroupId,AccessTypeId,PermissionTypeId,AssociationId,DC)
select 'Drg.M3.Client.Controllers.Admin.AffiliateSettings.CustomFieldsController.MenuLists', g.Id, 1, 2, 1, GETDATE()
from SEC_Group g
where
AssociationId=1 
and IsUserSpecific=0
and id!=18 --DOD Administrator
", "Default Menu Lists permissions to deny");
        }

        public override void Down()
        {
            Delete.Column("HideColumn").FromTable("QUZ_Element");

            Delete.Column("IsPublic").FromTable("COR_Menu2List");

            Delete.Column("AllowClone").FromTable("COR_Menu2List");
        }
    }
}
