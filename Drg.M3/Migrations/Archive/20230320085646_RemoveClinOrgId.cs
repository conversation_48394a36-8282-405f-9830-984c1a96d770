using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20230320085646)]
    public partial class RemoveClinOrgId : M3Migration
    {
        public override void Up()
        {
            Delete.Index("IX_ClinOrganizationId").OnTable("CON_Organization");

            Delete.ForeignKey("FK_Organization_ClinOrganization").OnTable("CON_Organization");

            Delete.Column("ClinOrganizationId").FromTable("CON_Organization");
        }

        public override void Down()
        {
            Create.Column("ClinOrganizationId").OnTable("CON_Organization").AsInt32().Nullable();

            Create.ForeignKey("FK_Organization_ClinOrganization").FromTable("CON_Organization").ForeignColumns("ClinOrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.Index("IX_ClinOrganizationId").OnTable("CON_Organization").WithOptions().NonClustered()
                .OnColumn("ClinOrganizationId").Ascending();
        }
    }
}
