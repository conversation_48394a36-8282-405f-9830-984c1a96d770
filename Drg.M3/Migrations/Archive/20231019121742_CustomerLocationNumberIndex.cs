using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20231019121742)]
    public partial class CustomerLocationNumberIndex : M3Migration
    {
        public override void Up()
        {
            Create.Index("IX_Number").OnTable("HW_CustomerLocation").WithOptions().NonClustered()
                .OnColumn("Number").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_Number").OnTable("HW_CustomerLocation");
        }
    }
}
