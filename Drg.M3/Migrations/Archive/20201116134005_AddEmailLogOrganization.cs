using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20201116134005)]
    public partial class AddEmailLogOrganization : M3Migration
    {
        public override void Up()
        {
            Create.Column("OrganizationId").OnTable("COR_EmailLog").AsInt32().Nullable();

            Create.ForeignKey("FK_EmailLog_Organization").FromTable("COR_EmailLog").ForeignColumns("OrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.Index("IX_OrganizationId").OnTable("COR_EmailLog").WithOptions().NonClustered()
                .OnColumn("OrganizationId").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_OrganizationId").OnTable("COR_EmailLog");

            Delete.ForeignKey("FK_EmailLog_Organization").OnTable("COR_EmailLog");

            Delete.Column("OrganizationId").FromTable("COR_EmailLog");
        }
    }
}
