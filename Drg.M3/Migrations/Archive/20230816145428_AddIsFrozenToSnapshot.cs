using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20230816145428)]
    public partial class AddIsFrozenToSnapshot : M3Migration
    {
        public override void Up()
        {
            Create.Column("IsFrozen").OnTable("QA_EmsMetricsSnapshot").AsBoolean().NotNullable().WithDefaultValue(false);
        }

        public override void Down()
        {
            Delete.Column("IsFrozen").FromTable("QA_EmsMetricsSnapshot");
        }
    }
}
