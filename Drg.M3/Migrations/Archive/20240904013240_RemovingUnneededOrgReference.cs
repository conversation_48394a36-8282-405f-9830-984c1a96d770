using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20240904013240)]
    public partial class RemovingUnneededOrgReference : M3Migration
    {
        public override void Up()
        {
            Delete.Index("IX_OrganizationId").OnTable("POL_InspectionEvent");

            Delete.ForeignKey("FK_InspectionEvent_Organization").OnTable("POL_InspectionEvent");

            Delete.Column("OrganizationId").FromTable("POL_InspectionEvent");
        }

        public override void Down()
        {
            Create.Column("OrganizationId").OnTable("POL_InspectionEvent").AsInt32().NotNullable();

            Create.ForeignKey("FK_InspectionEvent_Organization").FromTable("POL_InspectionEvent").ForeignColumns("OrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.Index("IX_OrganizationId").OnTable("POL_InspectionEvent").WithOptions().NonClustered()
                .OnColumn("OrganizationId").Ascending();
        }
    }
}
