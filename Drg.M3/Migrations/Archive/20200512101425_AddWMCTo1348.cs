using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20200512101425)]
    public partial class AddWMCTo1348 : M3Migration
    {
        public override void Up()
        {
            Create.Column("WasteMinimizationCodeId").OnTable("HW_Form1348").AsInt32().Nullable();

            Create.ForeignKey("FK_B9DBD058").FromTable("HW_Form1348").ForeignColumns("WasteMinimizationCodeId")
                .ToTable("COR_Reference").PrimaryColumns("Id");
        }

        public override void Down()
        {
            Delete.ForeignKey("FK_B9DBD058").OnTable("HW_Form1348");

            Delete.Column("WasteMinimizationCodeId").FromTable("HW_Form1348");
        }
    }
}
