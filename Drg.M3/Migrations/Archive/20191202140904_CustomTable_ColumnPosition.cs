using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20191202140904)]
    public partial class CustomTable_ColumnPosition : M3Migration
    {
        public override void Up()
        {
            Create.Column("Position").OnTable("RPT_CustomTableColumn").AsInt32().NotNullable().WithDefaultValue(0);
        }

        public override void Down()
        {
            Delete.Column("Position").FromTable("RPT_CustomTableColumn");
        }
    }
}
