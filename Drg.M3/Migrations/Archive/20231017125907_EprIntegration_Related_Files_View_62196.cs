using FluentMigrator;

namespace Drg.M3.Migrations
{
    [Migration(20231017125907)]
    public partial class EprIntegration_Related_Files_View_62196 : M3Migration
    {
        public override void Up()
        {
            Execute.Sql(@"create view vw_EprIntegration_RelatedFiles
as
    select 
		o2f.ClassName,
		o2f.ObjectId,
		f.Id,
		f.OriginalFileName,
		f.StorageFile<PERSON>ame,
		f.ContentType,
		f.ContentLength
	from COR_File f
		join COR_Object2File o2f on o2f.FileId = f.Id
		left join ENV_Finding finding on o2f.ClassName='Drg.M3.Domain.QA.Finding' and finding.Id=o2f.ObjectId
		left join ENV_Poam poam on poam.Id=finding.PoamId or (o2f.ClassName='Drg.M3.Domain.QA.Poam' and poam.Id=o2f.ObjectId)
	where
		f.DD is null
        and o2f.ClassName in ('Drg.M3.Domain.QA.Poam', 'Drg.M3.Domain.QA.Finding')
		and poam.EprPortalFundingRequest=1");
        }

        public override void Down()
        {
            Execute.Sql(@"drop view vw_EprIntegration_RelatedFiles");
        }
    }
}
