using Drg.M3.Dal;
using FluentMigrator.Expressions;
using FluentMigrator.NHibernateGenerator;
using FluentMigrator.NHibernateGenerator.Templates;
using FluentMigrator.NHibernateGenerator.Templates.CSharp;
using FluentNHibernate.Cfg;
using NHibernate;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;

namespace Drg.M3.Migrations
{
    public class M3MigrationsConfiguration : MigrationConfigurationBase
    {
        ISessionFactory _sessionFactory;

        public M3MigrationsConfiguration()
        {
            MigrationAssembly = typeof(M3Configuration).Assembly;
            MigrationNamespace = typeof(M3Migration).Namespace;
            MigrationBaseClassName = typeof(M3Migration).Name;
        }

        protected override NHibernate.Cfg.Configuration GetConfiguration()
        {
            try
            {
                var config = new M3Configuration();
                _sessionFactory = config.BuildSessionFactory();

                return config;
            }
            catch (Exception ex)
            {
                throw new Exception("Exception encountered while instantiating M3Configuration. " +
                    string.Join(Environment.NewLine, ex.Flatten(x => x.InnerException).Select(x => x.Message)));
            }
        }


        protected override long GenerateNextVersionNumber()
        {
            return long.Parse(DateTime.Now.ToString("yyyyMMddHHmmss"));
        }

        public override ITemplateFromExpressionFactory GetTemplateFromExpressionFactory()
        {
            return new M3CSharpTemplateFromExpressionFactory();
        }

        protected override List<MigrationExpressionBase> GetToExpressions()
        {
            var nhExpressions = base.GetToExpressions();
            var indexExpressions = Indexes.GetAll(_sessionFactory).Select(x => new CreateIndexExpression()
            {
                Index = x
            });

            //Every FK deserves an index
            var fkIndexes = nhExpressions.OfType<CreateForeignKeyExpression>()
                .Where(e => e.ForeignKey.ForeignTable != "QUZ_Quiz")
                .Select(e => new CreateIndexExpression()
                {
                    Index = new FluentMigrator.Model.IndexDefinition()
                    {
                        SchemaName = e.ForeignKey.ForeignTableSchema,
                        TableName = e.ForeignKey.ForeignTable,
                        Name = "IX_" + string.Join("_", e.ForeignKey.ForeignColumns),
                        Columns = e.ForeignKey.ForeignColumns.Select(c => new FluentMigrator.Model.IndexColumnDefinition() { Name = c }).ToList()
                    }
                });

            return nhExpressions.Concat(indexExpressions).Concat(fkIndexes).ToList();
        }
    }

    public class M3CSharpTemplateFromExpressionFactory : CSharpTemplateFromExpressionFactory
    {
        private Dictionary<Type, Func<MigrationExpressionBase, ITemplate>> _templateLookup = InitTemplates();

        private static Dictionary<Type, Func<MigrationExpressionBase, ITemplate>> InitTemplates()
        {
            return new Dictionary<Type, Func<MigrationExpressionBase, ITemplate>>
            {
                { typeof(DeleteForeignKeyExpression), e => new M3DropForeignKeyExpressionTemplate { Expression = (DeleteForeignKeyExpression)e } },
            };
        }

        public override ITemplate GetTemplate(MigrationExpressionBase expr)
        {
            var expressionType = expr.GetType();
            if (_templateLookup.ContainsKey(expressionType))
                return _templateLookup[expressionType](expr);

            return base.GetTemplate(expr);
        }
    }

    public class M3DropForeignKeyExpressionTemplate : DeleteForeignKeyExpressionTemplate
    {
        static readonly Regex _regex = new Regex("^(FK|KF)[0-9A-Fa-f]{6,16}$", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        public override void WriteTo(TextWriter tw)
        {
            if (_regex.IsMatch(Expression.ForeignKey.Name) && Expression.ForeignKey.ForeignColumns.Count == 1)
            {
                //This is the NH-generated FK name, we can't count on being consistent.
                //Use the sproc and match on Table/Column name instead of FK name
                tw.Write($@"
            DropForeignKey(""{Expression.ForeignKey.ForeignTable}"", ""{Expression.ForeignKey.ForeignColumns.Single()}"")");
            }
            else
            {
                //Use the default implementation which matches on FK name
                base.WriteTo(tw);
            }
        }
    }
}
