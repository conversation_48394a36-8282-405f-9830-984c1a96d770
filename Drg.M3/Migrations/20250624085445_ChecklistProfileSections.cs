using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Linq.Expressions;
using FluentMigrator;
using FluentMigrator.SqlServer;

namespace Drg.M3.Migrations
{
    [Migration(20250624085445)]
    public partial class ChecklistProfileSections : M3Migration
    {
        public override void Up()
        {
            Create.Column("ChecklistProfileSectionId").OnTable("ENV_ChecklistQuestion").AsInt32().Nullable();

            Create.Table("QA_GuidanceValue")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_GuidanceValue")
                .WithColumn("StringValue").AsString(int.MaxValue).Nullable()
                .WithColumn("DateValue").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("RowId").AsInt32().NotNullable()
                .WithColumn("ColumnId").AsInt32().NotNullable()
                .WithColumn("OrganizationId").AsInt32().NotNullable()
                .WithColumn("DocumentValueId").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable()
                .WithColumn("GuidanceRowId").AsInt32().Nullable();

            Create.Table("QA_ChecklistProfileSection")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_ChecklistProfileSection")
                .WithColumn("SectionName").AsString(255).Nullable()
                .WithColumn("FilterCriteria").AsString(255).Nullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("ChecklistProfileId").AsInt32().NotNullable()
                .WithColumn("AssociationId").AsInt32().Nullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.Table("QA_GuidanceRow")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_GuidanceRow")
                .WithColumn("IsLinkedRow").AsBoolean().NotNullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("TableId").AsInt32().NotNullable()
                .WithColumn("AreaId").AsInt32().NotNullable()
                .WithColumn("OrganizationId").AsInt32().NotNullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.Table("QA_GuidanceColumn")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_GuidanceColumn")
                .WithColumn("Type").AsInt32().NotNullable()
                .WithColumn("EditPermissions").AsInt32().NotNullable()
                .WithColumn("PopulateFromQuiz").AsBoolean().NotNullable()
                .WithColumn("IsInactive").AsBoolean().NotNullable()
                .WithColumn("DisplayOrder").AsInt32().Nullable()
                .WithColumn("Name").AsString(255).NotNullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("QuizQuestionId").AsInt32().Nullable()
                .WithColumn("AssociationId").AsInt32().NotNullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.Table("QA_GuidanceTable")
                .WithColumn("Id").AsInt32().NotNullable().Identity().PrimaryKey("PK_QA_GuidanceTable")
                .WithColumn("IsInactive").AsBoolean().NotNullable()
                .WithColumn("Type").AsInt32().NotNullable()
                .WithColumn("AddPermissions").AsInt32().NotNullable()
                .WithColumn("DeletePermissions").AsInt32().NotNullable()
                .WithColumn("DisplayOrder").AsInt32().Nullable()
                .WithColumn("CollapseByDefault").AsBoolean().NotNullable()
                .WithColumn("CanEnableRecursiveData").AsBoolean().NotNullable()
                .WithColumn("FirstColumnIsTitle").AsBoolean().NotNullable()
                .WithColumn("HideEmpty").AsBoolean().NotNullable()
                .WithColumn("Name").AsString(255).NotNullable()
                .WithColumn("DD").AsDateTime().Nullable()
                .WithColumn("DM").AsDateTime().Nullable()
                .WithColumn("DC").AsDateTime().Nullable()
                .WithColumn("AssociationId").AsInt32().NotNullable()
                .WithColumn("UD").AsInt32().Nullable()
                .WithColumn("UM").AsInt32().Nullable()
                .WithColumn("UC").AsInt32().Nullable();

            Create.Table("QA_GuidanceColumn_Table")
                .WithColumn("GuidanceColumnId").AsInt32().NotNullable().PrimaryKey("PK_QA_GuidanceColumn_Table")
                .WithColumn("GuidanceTableId").AsInt32().NotNullable().PrimaryKey("PK_QA_GuidanceColumn_Table");

            Create.Table("QA_GuidanceTable_Area")
                .WithColumn("GuidanceTableId").AsInt32().NotNullable().PrimaryKey("PK_QA_GuidanceTable_Area")
                .WithColumn("ReferenceId").AsInt32().NotNullable().PrimaryKey("PK_QA_GuidanceTable_Area");

            Create.Table("ENV_Finding2Location")
                .WithColumn("FindingId").AsInt32().NotNullable().PrimaryKey("PK_ENV_Finding2Location")
                .WithColumn("LocationId").AsInt32().NotNullable().PrimaryKey("PK_ENV_Finding2Location");

            Create.ForeignKey("FK_9DABF82D").FromTable("ENV_ChecklistQuestion").ForeignColumns("ChecklistProfileSectionId")
                .ToTable("QA_ChecklistProfileSection").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceValue_Row").FromTable("QA_GuidanceValue").ForeignColumns("RowId")
                .ToTable("QA_GuidanceRow").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceValue_Column").FromTable("QA_GuidanceValue").ForeignColumns("ColumnId")
                .ToTable("QA_GuidanceColumn").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceValue_Organization").FromTable("QA_GuidanceValue").ForeignColumns("OrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceValue_DocumentValue").FromTable("QA_GuidanceValue").ForeignColumns("DocumentValueId")
                .ToTable("COR_Document").PrimaryColumns("Id");

            Create.ForeignKey("FK_CC544FE8").FromTable("QA_GuidanceValue").ForeignColumns("GuidanceRowId")
                .ToTable("QA_GuidanceRow").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfileSection_ChecklistProfile").FromTable("QA_ChecklistProfileSection").ForeignColumns("ChecklistProfileId")
                .ToTable("QA_ChecklistProfile").PrimaryColumns("Id");

            Create.ForeignKey("FK_ChecklistProfileSection_Association").FromTable("QA_ChecklistProfileSection").ForeignColumns("AssociationId")
                .ToTable("COR_Association").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceRow_Table").FromTable("QA_GuidanceRow").ForeignColumns("TableId")
                .ToTable("QA_GuidanceTable").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceRow_Area").FromTable("QA_GuidanceRow").ForeignColumns("AreaId")
                .ToTable("COR_Reference").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceRow_Organization").FromTable("QA_GuidanceRow").ForeignColumns("OrganizationId")
                .ToTable("CON_Organization").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceColumn_QuizQuestion").FromTable("QA_GuidanceColumn").ForeignColumns("QuizQuestionId")
                .ToTable("QUZ_Element").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceColumn_Association").FromTable("QA_GuidanceColumn").ForeignColumns("AssociationId")
                .ToTable("COR_Association").PrimaryColumns("Id");

            Create.ForeignKey("FK_GuidanceTable_Association").FromTable("QA_GuidanceTable").ForeignColumns("AssociationId")
                .ToTable("COR_Association").PrimaryColumns("Id");

            Create.ForeignKey("FK_C8D55626").FromTable("QA_GuidanceColumn_Table").ForeignColumns("GuidanceTableId")
                .ToTable("QA_GuidanceTable").PrimaryColumns("Id");

            Create.ForeignKey("FK_7E7E1C72").FromTable("QA_GuidanceColumn_Table").ForeignColumns("GuidanceColumnId")
                .ToTable("QA_GuidanceColumn").PrimaryColumns("Id");

            Create.ForeignKey("FK_6E6B8DFE").FromTable("QA_GuidanceTable_Area").ForeignColumns("ReferenceId")
                .ToTable("COR_Reference").PrimaryColumns("Id");

            Create.ForeignKey("FK_CA934D4D").FromTable("QA_GuidanceTable_Area").ForeignColumns("GuidanceTableId")
                .ToTable("QA_GuidanceTable").PrimaryColumns("Id");

            Create.ForeignKey("FK_DAF725AD").FromTable("ENV_Finding2Location").ForeignColumns("LocationId")
                .ToTable("ENV_Location").PrimaryColumns("Id");

            Create.ForeignKey("FK_8BDD9D68").FromTable("ENV_Finding2Location").ForeignColumns("FindingId")
                .ToTable("ENV_Finding").PrimaryColumns("Id");

            Create.Index("IX_ChecklistProfileSectionId").OnTable("ENV_ChecklistQuestion").WithOptions().NonClustered()
                .OnColumn("ChecklistProfileSectionId").Ascending();

            Create.Index("IX_RowId").OnTable("QA_GuidanceValue").WithOptions().NonClustered()
                .OnColumn("RowId").Ascending();

            Create.Index("IX_ColumnId").OnTable("QA_GuidanceValue").WithOptions().NonClustered()
                .OnColumn("ColumnId").Ascending();

            Create.Index("IX_OrganizationId").OnTable("QA_GuidanceValue").WithOptions().NonClustered()
                .OnColumn("OrganizationId").Ascending();

            Create.Index("IX_DocumentValueId").OnTable("QA_GuidanceValue").WithOptions().NonClustered()
                .OnColumn("DocumentValueId").Ascending();

            Create.Index("IX_GuidanceRowId").OnTable("QA_GuidanceValue").WithOptions().NonClustered()
                .OnColumn("GuidanceRowId").Ascending();

            Create.Index("IX_ChecklistProfileId").OnTable("QA_ChecklistProfileSection").WithOptions().NonClustered()
                .OnColumn("ChecklistProfileId").Ascending();

            Create.Index("IX_AssociationId").OnTable("QA_ChecklistProfileSection").WithOptions().NonClustered()
                .OnColumn("AssociationId").Ascending();

            Create.Index("IX_TableId").OnTable("QA_GuidanceRow").WithOptions().NonClustered()
                .OnColumn("TableId").Ascending();

            Create.Index("IX_AreaId").OnTable("QA_GuidanceRow").WithOptions().NonClustered()
                .OnColumn("AreaId").Ascending();

            Create.Index("IX_OrganizationId").OnTable("QA_GuidanceRow").WithOptions().NonClustered()
                .OnColumn("OrganizationId").Ascending();

            Create.Index("IX_QuizQuestionId").OnTable("QA_GuidanceColumn").WithOptions().NonClustered()
                .OnColumn("QuizQuestionId").Ascending();

            Create.Index("IX_AssociationId").OnTable("QA_GuidanceColumn").WithOptions().NonClustered()
                .OnColumn("AssociationId").Ascending();

            Create.Index("IX_AssociationId").OnTable("QA_GuidanceTable").WithOptions().NonClustered()
                .OnColumn("AssociationId").Ascending();

            Create.Index("IX_GuidanceTableId").OnTable("QA_GuidanceColumn_Table").WithOptions().NonClustered()
                .OnColumn("GuidanceTableId").Ascending();

            Create.Index("IX_GuidanceColumnId").OnTable("QA_GuidanceColumn_Table").WithOptions().NonClustered()
                .OnColumn("GuidanceColumnId").Ascending();

            Create.Index("IX_ReferenceId").OnTable("QA_GuidanceTable_Area").WithOptions().NonClustered()
                .OnColumn("ReferenceId").Ascending();

            Create.Index("IX_GuidanceTableId").OnTable("QA_GuidanceTable_Area").WithOptions().NonClustered()
                .OnColumn("GuidanceTableId").Ascending();

            Create.Index("IX_LocationId").OnTable("ENV_Finding2Location").WithOptions().NonClustered()
                .OnColumn("LocationId").Ascending();

            Create.Index("IX_FindingId").OnTable("ENV_Finding2Location").WithOptions().NonClustered()
                .OnColumn("FindingId").Ascending();
        }

        public override void Down()
        {
            Delete.Index("IX_FindingId").OnTable("ENV_Finding2Location");

            Delete.Index("IX_LocationId").OnTable("ENV_Finding2Location");

            Delete.Index("IX_GuidanceTableId").OnTable("QA_GuidanceTable_Area");

            Delete.Index("IX_ReferenceId").OnTable("QA_GuidanceTable_Area");

            Delete.Index("IX_GuidanceColumnId").OnTable("QA_GuidanceColumn_Table");

            Delete.Index("IX_GuidanceTableId").OnTable("QA_GuidanceColumn_Table");

            Delete.Index("IX_AssociationId").OnTable("QA_GuidanceTable");

            Delete.Index("IX_AssociationId").OnTable("QA_GuidanceColumn");

            Delete.Index("IX_QuizQuestionId").OnTable("QA_GuidanceColumn");

            Delete.Index("IX_OrganizationId").OnTable("QA_GuidanceRow");

            Delete.Index("IX_AreaId").OnTable("QA_GuidanceRow");

            Delete.Index("IX_TableId").OnTable("QA_GuidanceRow");

            Delete.Index("IX_AssociationId").OnTable("QA_ChecklistProfileSection");

            Delete.Index("IX_ChecklistProfileId").OnTable("QA_ChecklistProfileSection");

            Delete.Index("IX_GuidanceRowId").OnTable("QA_GuidanceValue");

            Delete.Index("IX_DocumentValueId").OnTable("QA_GuidanceValue");

            Delete.Index("IX_OrganizationId").OnTable("QA_GuidanceValue");

            Delete.Index("IX_ColumnId").OnTable("QA_GuidanceValue");

            Delete.Index("IX_RowId").OnTable("QA_GuidanceValue");

            Delete.Index("IX_ChecklistProfileSectionId").OnTable("ENV_ChecklistQuestion");

            Delete.ForeignKey("FK_8BDD9D68").OnTable("ENV_Finding2Location");

            Delete.ForeignKey("FK_DAF725AD").OnTable("ENV_Finding2Location");

            Delete.ForeignKey("FK_CA934D4D").OnTable("QA_GuidanceTable_Area");

            Delete.ForeignKey("FK_6E6B8DFE").OnTable("QA_GuidanceTable_Area");

            Delete.ForeignKey("FK_7E7E1C72").OnTable("QA_GuidanceColumn_Table");

            Delete.ForeignKey("FK_C8D55626").OnTable("QA_GuidanceColumn_Table");

            Delete.ForeignKey("FK_GuidanceTable_Association").OnTable("QA_GuidanceTable");

            Delete.ForeignKey("FK_GuidanceColumn_Association").OnTable("QA_GuidanceColumn");

            Delete.ForeignKey("FK_GuidanceColumn_QuizQuestion").OnTable("QA_GuidanceColumn");

            Delete.ForeignKey("FK_GuidanceRow_Organization").OnTable("QA_GuidanceRow");

            Delete.ForeignKey("FK_GuidanceRow_Area").OnTable("QA_GuidanceRow");

            Delete.ForeignKey("FK_GuidanceRow_Table").OnTable("QA_GuidanceRow");

            Delete.ForeignKey("FK_ChecklistProfileSection_Association").OnTable("QA_ChecklistProfileSection");

            Delete.ForeignKey("FK_ChecklistProfileSection_ChecklistProfile").OnTable("QA_ChecklistProfileSection");

            Delete.ForeignKey("FK_CC544FE8").OnTable("QA_GuidanceValue");

            Delete.ForeignKey("FK_GuidanceValue_DocumentValue").OnTable("QA_GuidanceValue");

            Delete.ForeignKey("FK_GuidanceValue_Organization").OnTable("QA_GuidanceValue");

            Delete.ForeignKey("FK_GuidanceValue_Column").OnTable("QA_GuidanceValue");

            Delete.ForeignKey("FK_GuidanceValue_Row").OnTable("QA_GuidanceValue");

            Delete.ForeignKey("FK_9DABF82D").OnTable("ENV_ChecklistQuestion");

            Delete.Table("ENV_Finding2Location");

            Delete.Table("QA_GuidanceTable_Area");

            Delete.Table("QA_GuidanceColumn_Table");

            Delete.Table("QA_GuidanceTable");

            Delete.Table("QA_GuidanceColumn");

            Delete.Table("QA_GuidanceRow");

            Delete.Table("QA_ChecklistProfileSection");

            Delete.Table("QA_GuidanceValue");

            Delete.Column("ChecklistProfileSectionId").FromTable("ENV_ChecklistQuestion");
        }
    }
}
