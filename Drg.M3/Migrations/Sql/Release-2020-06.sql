/* Beginning Transaction */
BEGIN TRANSACTION
GO
begin try
/* 20200513140154: MenuTableUpgrades migrating =============================== */

/* CreateColumn COR_Menu2List Allow<PERSON>lone Boolean */
ALTER TABLE [dbo].[COR_Menu2List] ADD [AllowClone] BIT NOT NULL CONSTRAINT [DF_COR_Menu2List_AllowClone] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn COR_Menu2List IsPublic Boolean */
ALTER TABLE [dbo].[COR_Menu2List] ADD [IsPublic] BIT NOT NULL CONSTRAINT [DF_COR_Menu2List_IsPublic] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn QUZ_Element HideColumn Boolean */
ALTER TABLE [dbo].[QUZ_Element] ADD [HideColumn] BIT NOT NULL CONSTRAINT [DF_QUZ_Element_HideColumn] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* Default Menu Lists permissions to deny */

insert SEC_Permission (FunctionKey,GroupId,AccessTypeId,PermissionTypeId,AssociationId,DC)
select 'Drg.M3.Client.Controllers.Admin.AffiliateSettings.CustomFieldsController.MenuLists', g.Id, 1, 2, 1, GETDATE()
from SEC_Group g
where
AssociationId=1 
and IsUserSpecific=0
and id!=18 --DOD Administrator

end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20200513140154, getutcdate(), N'MenuTableUpgrades')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20200513140154: MenuTableUpgrades migrated */
/* 20200605134242: AddDispositionBillingFields migrating ===================== */

/* CreateColumn COR_OrganizationSettings DispositionBilling_Vendor String */
ALTER TABLE [dbo].[COR_OrganizationSettings] ADD [DispositionBilling_Vendor] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn COR_OrganizationSettings DispositionBilling_Buyer String */
ALTER TABLE [dbo].[COR_OrganizationSettings] ADD [DispositionBilling_Buyer] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn COR_OrganizationSettings DispositionBilling_RequestedBy String */
ALTER TABLE [dbo].[COR_OrganizationSettings] ADD [DispositionBilling_RequestedBy] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn COR_OrganizationSettings DispositionBilling_ShipTo String */
ALTER TABLE [dbo].[COR_OrganizationSettings] ADD [DispositionBilling_ShipTo] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn COR_OrganizationSettings DispositionBilling_Itemnum String */
ALTER TABLE [dbo].[COR_OrganizationSettings] ADD [DispositionBilling_Itemnum] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* ExecuteSqlStatement update s set DispositionBilling_Itemnum='S222000000000' from COR_OrganizationSettings s join CON_Organization o on s.OrganizationId=o.Id where o.AssociationId=3 */
update s set DispositionBilling_Itemnum='S222000000000' from COR_OrganizationSettings s join CON_Organization o on s.OrganizationId=o.Id where o.AssociationId=3
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20200605134242, getutcdate(), N'AddDispositionBillingFields')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
/* 20200605134242: AddDispositionBillingFields migrated */
/* Committing Transaction */
COMMIT TRANSACTION
GO