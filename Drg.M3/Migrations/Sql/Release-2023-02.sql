/* Beginning Transaction */
BEGIN TRANSACTION
GO
begin try
/* 20221104131437: AddIncludeInFleetReportColumn migrating =================== */

/* CreateColumn HW_ResponsibleParty IncludeInFleetReport Boolean */
ALTER TABLE [dbo].[HW_ResponsibleParty] ADD [IncludeInFleetReport] BIT NOT NULL CONSTRAINT [DF_HW_ResponsibleParty_IncludeInFleetReport] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20221104131437, getutcdate(), N'AddIncludeInFleetReportColumn')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20221104131437: AddIncludeInFleetReportColumn migrated */
/* 20230119101042: RefactorDecimalAgain migrating ============================ */

/* AlterColumn QUZ_Quiz _de1 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de1] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de2 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de2] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de3 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de3] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de4 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de4] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de5 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de5] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de6 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de6] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de7 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de7] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de8 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de8] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de9 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de9] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de10 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de10] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de11 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de11] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de12 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de12] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de13 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de13] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de14 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de14] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de15 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de15] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de16 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de16] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de17 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de17] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de18 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de18] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de19 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de19] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de20 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de20] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de21 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de21] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de22 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de22] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de23 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de23] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de24 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de24] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de25 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de25] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de26 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de26] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de27 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de27] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de28 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de28] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de29 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de29] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de30 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de30] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de31 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de31] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de32 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de32] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de33 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de33] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de34 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de34] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de35 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de35] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de36 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de36] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de37 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de37] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de38 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de38] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de39 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de39] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de40 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de40] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de41 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de41] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de42 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de42] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de43 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de43] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de44 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de44] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de45 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de45] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de46 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de46] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de47 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de47] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de48 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de48] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de49 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de49] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* AlterColumn QUZ_Quiz _de50 Decimal */
ALTER TABLE [dbo].[QUZ_Quiz] ALTER COLUMN [_de50] DECIMAL(19,6)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* ExecuteSqlStatement alter table QUZ_Quiz rebuild */
alter table QUZ_Quiz rebuild
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20230119101042, getutcdate(), N'RefactorDecimalAgain')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20230119101042: RefactorDecimalAgain migrated */
/* 20230202114403: AddIsRadioactive migrating ================================ */

/* CreateColumn HW_WasteProfile IsRadioactive Boolean */
ALTER TABLE [dbo].[HW_WasteProfile] ADD [IsRadioactive] BIT NOT NULL CONSTRAINT [DF_HW_WasteProfile_IsRadioactive] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn HW_WasteRecord IsRadioactive Boolean */
ALTER TABLE [dbo].[HW_WasteRecord] ADD [IsRadioactive] BIT NOT NULL CONSTRAINT [DF_HW_WasteRecord_IsRadioactive] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20230202114403, getutcdate(), N'AddIsRadioactive')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20230202114403: AddIsRadioactive migrated */
/* 20230202121030: AddWATreatmentByGenerator migrating ======================= */

/* CreateColumn HW_WasteProfile WATreatmentByGenerator Boolean */
ALTER TABLE [dbo].[HW_WasteProfile] ADD [WATreatmentByGenerator] BIT NOT NULL CONSTRAINT [DF_HW_WasteProfile_WATreatmentByGenerator] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20230202121030, getutcdate(), N'AddWATreatmentByGenerator')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20230202121030: AddWATreatmentByGenerator migrated */
/* 20230203115559: AddPageComment_Url migrating ============================== */

/* CreateColumn COR_PageComment Url String */
ALTER TABLE [dbo].[COR_PageComment] ADD [Url] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20230203115559, getutcdate(), N'AddPageComment_Url')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20230203115559: AddPageComment_Url migrated */
/* 20230208122606: AddGMReportUsesEpaCodesFromWP migrating =================== */

/* CreateColumn COR_OrganizationSettings HW_GMReportUsesEpaCodesFromWP Boolean */
ALTER TABLE [dbo].[COR_OrganizationSettings] ADD [HW_GMReportUsesEpaCodesFromWP] BIT NOT NULL CONSTRAINT [DF_COR_OrganizationSettings_HW_GMReportUsesEpaCodesFromWP] DEFAULT 0
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20230208122606, getutcdate(), N'AddGMReportUsesEpaCodesFromWP')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20230208122606: AddGMReportUsesEpaCodesFromWP migrated */
/* 20230213080408: AddWPCommentsFields migrating ============================= */

/* CreateColumn HW_WasteProfile GMReportComments String */
ALTER TABLE [dbo].[HW_WasteProfile] ADD [GMReportComments] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateColumn HW_WasteProfile WRReportComments String */
ALTER TABLE [dbo].[HW_WasteProfile] ADD [WRReportComments] NVARCHAR(255)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20230213080408, getutcdate(), N'AddWPCommentsFields')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
/* 20230213080408: AddWPCommentsFields migrated */
/* Committing Transaction */
COMMIT TRANSACTION