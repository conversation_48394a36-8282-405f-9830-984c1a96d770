/* Beginning Transaction */
BEGIN TRANSACTION
GO
begin try
/* 20220629111538: AddMediaScopeTable migrating ============================== */

/* CreateTable ENV_AuditMediaScopeStatement */
CREATE TABLE [dbo].[ENV_AuditMediaScopeStatement] ([Id] INT NOT NULL IDENTITY(1,1), [Statement] NVARCHAR(MAX), [PercentRange] INT, [AuditId] INT, [MediaId] INT, CONSTRAINT [PK_ENV_AuditMediaScopeStatement] PRIMARY KEY ([Id]))
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateForeignKey FK_AuditMediaScopeStatement_Audit ENV_AuditMediaScopeStatement(AuditId) ENV_Schedule(Id) */
ALTER TABLE [dbo].[ENV_AuditMediaScopeStatement] ADD CONSTRAINT [FK_AuditMediaScopeStatement_Audit] FOREIGN KEY ([AuditId]) REFERENCES [dbo].[ENV_Schedule] ([Id])
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateForeignKey FK_AuditMediaScopeStatement_Media ENV_AuditMediaScopeStatement(MediaId) COR_Reference(Id) */
ALTER TABLE [dbo].[ENV_AuditMediaScopeStatement] ADD CONSTRAINT [FK_AuditMediaScopeStatement_Media] FOREIGN KEY ([MediaId]) REFERENCES [dbo].[COR_Reference] ([Id])
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateIndex ENV_AuditMediaScopeStatement (AuditId) */
CREATE INDEX [IX_AuditId] ON [dbo].[ENV_AuditMediaScopeStatement] ([AuditId] ASC)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* CreateIndex ENV_AuditMediaScopeStatement (MediaId) */
CREATE INDEX [IX_MediaId] ON [dbo].[ENV_AuditMediaScopeStatement] ([MediaId] ASC)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20220629111538, getutcdate(), N'AddMediaScopeTable')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
/* 20220629111538: AddMediaScopeTable migrated */
/* 20220629122355: AddMediaScopeUniqueIndex migrating ======================== */

/* CreateIndex ENV_AuditMediaScopeStatement (AuditId, MediaId) */
CREATE UNIQUE INDEX [UQ_AuditMediaScopeStatement] ON [dbo].[ENV_AuditMediaScopeStatement] ([AuditId] ASC, [MediaId] ASC)
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
begin try
INSERT INTO [dbo].[VersionInfo] ([Version], [AppliedOn], [Description]) VALUES (20220629122355, getutcdate(), N'AddMediaScopeUniqueIndex')
end try begin catch rollback transaction; raiserror('Error, stopping execution', 20, -1) with log; end catch 
GO
/* 20220629122355: AddMediaScopeUniqueIndex migrated */
/* Committing Transaction */
COMMIT TRANSACTION
