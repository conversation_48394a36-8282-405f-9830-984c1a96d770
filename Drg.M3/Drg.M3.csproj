<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{48D81930-0168-4D9B-B5B9-EB915264D8DB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Drg.M3</RootNamespace>
    <AssemblyName>Drg.M3</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <WarningsAsErrors>nullable</WarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>
    </DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\packages\Antlr3.Runtime.3.5.1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Aspose.Pdf">
      <HintPath>..\packages\Aspose.Pdf.9.4.0\lib\net40-client\Aspose.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=8.1.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.8.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Ciloci.Flee, Version=0.9.26.0, Culture=neutral, PublicKeyToken=c8526a021ef298ed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Trove.FLEE.0.9.26.0\lib\net20\Ciloci.Flee.dll</HintPath>
    </Reference>
    <Reference Include="Cronos, Version=0.7.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Cronos.0.7.1\lib\net45\Cronos.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=27.0.0.0, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>..\packages\CsvHelper.27.1.1\lib\net45\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.2.0.90\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="DataAnnotationsExtensions">
      <HintPath>..\packages\DataAnnotationsExtensions.1.1.0.0\lib\NETFramework40\DataAnnotationsExtensions.dll</HintPath>
    </Reference>
    <Reference Include="DataAnnotationsExtensions.ClientValidation">
      <HintPath>..\packages\DataAnnotationsExtensions.MVC3.1.1.0.0\lib\NETFramework40\DataAnnotationsExtensions.ClientValidation.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.5.5631.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.5\lib\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="DotLiquid">
      <HintPath>..\packages\DotLiquid.1.8.0\lib\NET45\DotLiquid.dll</HintPath>
    </Reference>
    <Reference Include="Drg.M3.dll.GrapeCity.Licenses">
      <HintPath>..\lib\Active Reports 7.2\Drg.M3.dll.GrapeCity.Licenses.dll</HintPath>
    </Reference>
    <Reference Include="EdiFabric, Version=10.6.4.0, Culture=neutral, PublicKeyToken=30198c5f4974e51a, processorArchitecture=MSIL">
      <HintPath>..\packages\EdiFabric.10.6.4\lib\net45\EdiFabric.dll</HintPath>
    </Reference>
    <Reference Include="EdiFabric.Templates.X12, Version=2.7.1.0, Culture=neutral, PublicKeyToken=30198c5f4974e51a, processorArchitecture=MSIL">
      <HintPath>..\packages\EdiFabric.Templates.X12.2.7.1\lib\net45\EdiFabric.Templates.X12.dll</HintPath>
    </Reference>
    <Reference Include="Elmah, Version=1.2.14706.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\elmah.corelibrary.1.2.2\lib\Elmah.dll</HintPath>
    </Reference>
    <Reference Include="ephtmltopdf">
      <HintPath>..\lib\ExpertPdf 5.4\ephtmltopdf.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.3.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.5.3\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="evohtmltopdf">
      <HintPath>..\Components\EvoHtmlToPdf 3.5\evohtmltopdf.dll</HintPath>
    </Reference>
    <Reference Include="ExpressionEvaluator">
      <HintPath>..\packages\ExpressionEvaluator.2.0.3.0\lib\net40\ExpressionEvaluator.dll</HintPath>
    </Reference>
    <Reference Include="ExpressiveAnnotations, Version=2.2.7.0, Culture=neutral, PublicKeyToken=d3e372f3c1e83cec, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Components\ExpressiveAnnotations 2.1.DRG\ExpressiveAnnotations.dll</HintPath>
    </Reference>
    <Reference Include="FlatFiles">
      <HintPath>..\packages\FlatFiles.0.1.9.0\lib\net40\FlatFiles.dll</HintPath>
    </Reference>
    <Reference Include="FlexCel, Version=4.9.6.0, Culture=neutral, PublicKeyToken=cb8f6080e6d5a4d6, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Components\FlexCel 5.7\FlexCel.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.3.2.1\lib\net461\FluentMigrator.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Abstractions, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Abstractions.3.2.1\lib\net461\FluentMigrator.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Extensions.Oracle, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Extensions.Oracle.3.2.1\lib\net461\FluentMigrator.Extensions.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Extensions.SqlAnywhere, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Extensions.SqlAnywhere.3.2.1\lib\net461\FluentMigrator.Extensions.SqlAnywhere.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Extensions.SqlServer, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Extensions.SqlServer.3.2.1\lib\net461\FluentMigrator.Extensions.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.NHibernateGenerator, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigratorNHibernateGenerator.1.1.4\lib\FluentMigrator.NHibernateGenerator.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.3.2.1\lib\net461\FluentMigrator.Runner.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Core, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Core.3.2.1\lib\net461\FluentMigrator.Runner.Core.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Db2, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Db2.3.2.1\lib\net461\FluentMigrator.Runner.Db2.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Firebird, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Firebird.3.2.1\lib\net461\FluentMigrator.Runner.Firebird.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Hana, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Hana.3.2.1\lib\net461\FluentMigrator.Runner.Hana.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Jet, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Jet.3.2.1\lib\net461\FluentMigrator.Runner.Jet.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.MySql, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.MySql.3.2.1\lib\net461\FluentMigrator.Runner.MySql.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Oracle, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Oracle.3.2.1\lib\net461\FluentMigrator.Runner.Oracle.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Postgres, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Postgres.3.2.1\lib\net461\FluentMigrator.Runner.Postgres.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.Redshift, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.Redshift.3.2.1\lib\net461\FluentMigrator.Runner.Redshift.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.SqlAnywhere, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.SqlAnywhere.3.2.1\lib\net461\FluentMigrator.Runner.SqlAnywhere.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.SQLite, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.SQLite.3.2.1\lib\net461\FluentMigrator.Runner.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.SqlServer, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.SqlServer.3.2.1\lib\net461\FluentMigrator.Runner.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FluentMigrator.Runner.SqlServerCe, Version=3.2.1.0, Culture=neutral, PublicKeyToken=aacfc7de5acabf05, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentMigrator.Runner.SqlServerCe.3.2.1\lib\net461\FluentMigrator.Runner.SqlServerCe.dll</HintPath>
    </Reference>
    <Reference Include="FluentNHibernate, Version=2.1.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\FluentNHibernate.2.1.2\lib\net461\FluentNHibernate.dll</HintPath>
    </Reference>
    <Reference Include="GemBox.Document, Version=3*******020, Culture=neutral, PublicKeyToken=b1b72c69714d4847, processorArchitecture=MSIL">
      <HintPath>..\packages\GemBox.Document.35.0.1020\lib\net35\GemBox.Document.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Chart.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Chart.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Design.Win.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Design.Win.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Diagnostics.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Diagnostics.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Document.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Document.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Excel.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Export.Excel.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Html.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Export.Html.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Image.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Export.Image.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Pdf.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Export.Pdf.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Export.Word.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Export.Word.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Extensibility.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Extensibility.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Viewer.Win.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Viewer.Win.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.VisualStudio.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.VisualStudio.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.ActiveReports.Web.v7">
      <HintPath>..\lib\Active Reports 7.2\GrapeCity.ActiveReports.Web.v7.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.DataVisualization.Chart, Version=0.4.125.0, Culture=neutral, PublicKeyToken=8dbeb54e07a9de91, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Excel.6.0.4\lib\net461\GrapeCity.DataVisualization.Chart.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.Documents.Barcode, Version=*******, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Barcode.6.0.0\lib\net461\GrapeCity.Documents.Barcode.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.Documents.DX.Windows, Version=*******, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.DX.Windows.6.0.0\lib\net461\GrapeCity.Documents.DX.Windows.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.Documents.Excel, Version=6.0.4.0, Culture=neutral, PublicKeyToken=c57ae7fc6891b71d, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Excel.6.0.4\lib\net461\GrapeCity.Documents.Excel.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.Documents.Imaging, Version=*******, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Imaging.6.0.0\lib\net461\GrapeCity.Documents.Imaging.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.Documents.Imaging.Windows, Version=*******, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Imaging.Windows.6.0.0\lib\net461\GrapeCity.Documents.Imaging.Windows.dll</HintPath>
    </Reference>
    <Reference Include="GrapeCity.Documents.Pdf, Version=*******, Culture=neutral, PublicKeyToken=d55d733d2bfd5065, processorArchitecture=MSIL">
      <HintPath>..\packages\GrapeCity.Documents.Pdf.6.0.0\lib\net461\GrapeCity.Documents.Pdf.dll</HintPath>
    </Reference>
    <Reference Include="Griddly.Mvc, Version=3.6.25.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Griddly.Core.3.6.25\lib\net461\Griddly.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Console, Version=1.4.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Console.1.4.3\lib\net45\Hangfire.Console.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Core, Version=1.8.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Core.1.8.2\lib\net46\Hangfire.Core.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Pro">
      <HintPath>..\lib\Hangfire.Pro\2.3.0\netstandard2.0\Hangfire.Pro.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.SqlServer, Version=1.8.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.SqlServer.1.8.2\lib\net451\Hangfire.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.11.60.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.11.60\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="Humanizer, Version=*******, Culture=neutral, PublicKeyToken=979442b78dfc278e, processorArchitecture=MSIL">
      <HintPath>..\packages\Humanizer.Core.2.0.1\lib\dotnet\Humanizer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zip">
      <HintPath>..\packages\DotNetZip.1.9.3\lib\net20\Ionic.Zip.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\log4net.2.0.3\lib\net40-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MarkdownDeep">
      <HintPath>..\packages\MarkdownDeep.NET.1.5\lib\.NetFramework 3.5\MarkdownDeep.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.5.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Build.Framework" />
    <Reference Include="Microsoft.Build.Utilities.v4.0" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.2.0.1\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.2.0.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.2.0.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.2.0.1\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=3.1.7.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.7\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.2.0.1\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=2.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.2.0.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.0.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.0.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.*******\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=*******, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\packages\NHibernate.5.2.5\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NodaTime, Version=*******, Culture=neutral, PublicKeyToken=4226afe0d9b296d1, processorArchitecture=MSIL">
      <HintPath>..\packages\NodaTime.3.0.0\lib\netstandard2.0\NodaTime.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=*******, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="ReachFramework" />
    <Reference Include="Remotion.Linq, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=2.2.0.0, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet, Version=2024.0.0.0, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
      <HintPath>..\packages\SSH.NET.2024.0.0\lib\netstandard2.0\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="SmartAssembly.Attributes, Version=8.1.1.4963, Culture=neutral, PublicKeyToken=7f465a1c156d4d57, processorArchitecture=MSIL">
      <HintPath>..\packages\RedGate.SmartAssembly.Attributes.8.1.1.4963\lib\net20\SmartAssembly.Attributes.dll</HintPath>
    </Reference>
    <Reference Include="SX">
      <HintPath>..\lib\SX.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.4.1\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.2.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.4.1\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Common, Version=4.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Drawing.Common.5.0.2\lib\net461\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.4.5.0\lib\net46\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Printing" />
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.4.7.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Xml, Version=4.0.3.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Xml.4.7.1\lib\net461\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.4.7.0\lib\net461\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.4.7.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ServiceModel.Primitives.4.6.0\lib\net461\System.ServiceModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encoding.CodePages, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.6.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.5.0.1\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=5.0.0.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.5.0.2\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Abstractions">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivator">
      <HintPath>..\packages\WebActivator.1.4\lib\NETFramework40\WebActivator.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="xunit, Version=1.9.2.1705, Culture=neutral, PublicKeyToken=8d05b1bb7a6fdb6c, processorArchitecture=MSIL">
      <HintPath>..\packages\xunit.1.9.2\lib\net20\xunit.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Z.ExtensionMethods.WithNamespace, Version=2.1.1.0, Culture=neutral, PublicKeyToken=59b66d028979105b, processorArchitecture=MSIL">
      <HintPath>..\packages\Z.ExtensionMethods.WithNamespace.2.1.1\lib\net45\Z.ExtensionMethods.WithNamespace.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionDisposable.cs" />
    <Compile Include="Bll\AbstractBll.cs" />
    <Compile Include="Bll\AXLibrary\AXExceptions.cs" />
    <Compile Include="Bll\AXLibrary\AXParser.cs" />
    <Compile Include="Bll\AXLibrary\AXTokenizer.cs" />
    <Compile Include="Bll\AXLibrary\AXValidator.cs" />
    <Compile Include="Bll\AXLibrary\ExpressionContext.cs" />
    <Compile Include="Bll\AXLibrary\ExpressionFactory.cs" />
    <Compile Include="Bll\AXLibrary\Expressions.cs" />
    <Compile Include="Bll\Cms\CmsBll.cs" />
    <Compile Include="Bll\Compliance\MonitoringBll.cs" />
    <Compile Include="Bll\Core\ISequentialNumberProvider.cs" />
    <Compile Include="Bll\Core\SequentialNumberBll.cs" />
    <Compile Include="Bll\Core\ZipBll.cs" />
    <Compile Include="Bll\Core\TimelineBll.cs" />
    <Compile Include="Bll\Core\LabelBll.cs" />
    <Compile Include="Bll\Core\AlertBll.cs" />
    <Compile Include="Bll\Core\AutoMapperHelper.cs" />
    <Compile Include="Bll\Core\CommunicationBll.cs" />
    <Compile Include="Bll\Core\ChangeHistoryBll.cs" />
    <Compile Include="Bll\Core\DataCallBll.cs" />
    <Compile Include="Bll\Core\DocumentBll.cs" />
    <Compile Include="Bll\Core\FileBll.cs" />
    <Compile Include="Bll\Core\CryptographyBll.cs" />
    <Compile Include="Bll\HW\DAAS\DAASBll.cs" />
    <Compile Include="Bll\HW\DAAS\DAASFile.cs" />
    <Compile Include="Bll\HW\DAAS\Extensions.cs" />
    <Compile Include="Bll\HW\DAAS\EnumAttribute.cs" />
    <Compile Include="Bll\HW\DAAS\FileResolvers\AG_527R_Resolver.cs" />
    <Compile Include="Bll\HW\DAAS\FileResolvers\AG_824_Resolver.cs" />
    <Compile Include="Bll\HW\DAAS\FileResolvers\FileResolver.cs" />
    <Compile Include="Bll\HW\DAAS\FileResolvers\FunctionalAcknowledgement997Resolver.cs" />
    <Compile Include="Bll\HW\DAAS\FileResolvers\IFileResolver.cs" />
    <Compile Include="Bll\HW\DAAS\FileResolvers\R_Resolver.cs" />
    <Compile Include="Bll\HW\DAAS\Hangfire\DAASExportPollerJob.cs" />
    <Compile Include="Bll\HW\DAAS\IDAASFileHandler.cs" />
    <Compile Include="Bll\HW\DAAS\LocalFolderDAASFileHandler.cs" />
    <Compile Include="Bll\HW\DAAS\Models\Ack_997.cs" />
    <Compile Include="Bll\HW\DAAS\Models\Extensions.cs" />
    <Compile Include="Bll\HW\DAAS\Models\IDescribableDaasRecord.cs" />
    <Compile Include="Bll\HW\DAAS\Models\SH_856S.cs" />
    <Compile Include="Bll\HW\DAAS\Models\AG_824R.cs" />
    <Compile Include="Bll\HW\DAAS\Models\MD_527R.cs" />
    <Compile Include="Bll\HW\DAAS\Models\SP_841W.cs" />
    <Compile Include="Bll\HW\DAAS\Models\UnknownValueException.cs" />
    <Compile Include="Bll\HW\HwReportsBll.Models.cs" />
    <Compile Include="Bll\HW\DAAS\DaasFileGenerator.cs" />
    <Compile Include="Bll\HW\TurboWasteBll.cs" />
    <Compile Include="Bll\ImportExport\DeficiencyImport.cs" />
    <Compile Include="Bll\ImportExport\Framework\IImport.cs" />
    <Compile Include="Bll\ImportExport\Framework\EvaluateConstantsExpressionVisitor.cs" />
    <Compile Include="Bll\ImportExport\Framework\ImportType.cs" />
    <Compile Include="Bll\ImportExport\Framework\M3AbstractImport.cs" />
    <Compile Include="Bll\ImportExport\Framework\AbstractImport.cs" />
    <Compile Include="Bll\ImportExport\Framework\ImportProperty.Builder.cs" />
    <Compile Include="Bll\ImportExport\Framework\ImportProperty.cs" />
    <Compile Include="Bll\ImportExport\Framework\ImportValueDictionary.cs" />
    <Compile Include="Bll\ImportExport\Framework\MetadataDictionary.cs" />
    <Compile Include="Bll\ImportExport\AssetImport.cs" />
    <Compile Include="Bll\ImportExport\InventoryItemImport.cs" />
    <Compile Include="Bll\ImportExport\LocalPracticeTypeImport.cs" />
    <Compile Include="Bll\ImportExport\AspectImport.cs" />
    <Compile Include="Bll\ImportExport\PermitImport.cs" />
    <Compile Include="Bll\ImportExport\QuizImport.cs" />
    <Compile Include="Bll\Core\ReassignmentBll.cs" />
    <Compile Include="Bll\Core\ReferenceBll.cs" />
    <Compile Include="Bll\Core\RoleBll.cs" />
    <Compile Include="Bll\Dashboard\DashboardBll.cs" />
    <Compile Include="Bll\Dashboard\NhQueryable.cs" />
    <Compile Include="Bll\ImportExport\LocationImport.cs" />
    <Compile Include="Bll\ImportExport\SinglePoamImport.cs" />
    <Compile Include="Bll\ImportExport\SingleFindingImport.cs" />
    <Compile Include="Bll\POL\ScheduleBll.cs" />
    <Compile Include="Bll\QA\GuidanceBll.cs" />
    <Compile Include="Bll\QA\EmsMetricsBll.cs" />
    <Compile Include="Bll\Reports\SpreadsheetColumn.cs" />
    <Compile Include="Bll\Reports\SpreadsheetColumn[T].cs" />
    <Compile Include="Bll\Reports\FormattedReportablePropertyDef.cs" />
    <Compile Include="Client\Configuration\ConfigurationManagerProvider.cs" />
    <Compile Include="Client\DateTimeOffsetModelBinder.cs" />
    <Compile Include="Client\FileResolver.cs" />
    <Compile Include="Client\IFileResolver.cs" />
    <Compile Include="Client\Models\BootstrapDrilldownNode.cs" />
    <Compile Include="Client\Models\Dashboard\MilestoneFilterModel.cs" />
    <Compile Include="Client\Models\Dashboard\PoamFilterModel.cs" />
    <Compile Include="Client\Models\Dashboard\FindingFilterModel.cs" />
    <Compile Include="Client\Models\Dashboard\AuditFilterModel.cs" />
    <Compile Include="Bll\Events\EventBll.cs" />
    <Compile Include="Bll\HW\ExportBll.cs" />
    <Compile Include="Bll\HW\DeliveryOrderBll.cs" />
    <Compile Include="Bll\HW\Form1348Bll.cs" />
    <Compile Include="Bll\HW\GencommBll.cs" />
    <Compile Include="Bll\HW\MetricsBll.cs" />
    <Compile Include="Bll\HW\HwReportsBll.cs" />
    <Compile Include="Bll\HW\ImportBll.cs" />
    <Compile Include="Bll\HW\LabelsBll.cs" />
    <Compile Include="Bll\HW\LookupTableBll.cs" />
    <Compile Include="Bll\HW\ManifestBll.cs" />
    <Compile Include="Bll\HW\WasteRecordBll.cs" />
    <Compile Include="Bll\HW\PickupBll.cs" />
    <Compile Include="Bll\QA\ChecklistReportsBll.cs" />
    <Compile Include="Bll\QA\ChecklistBll.cs" />
    <Compile Include="Bll\QA\EMSDataBll.cs" />
    <Compile Include="Bll\QA\MediaBll.cs" />
    <Compile Include="Bll\QA\PoamBll.cs" />
    <Compile Include="Bll\QA\ProgramStatisticBll.cs" />
    <Compile Include="Bll\Reports\WordReportBll.cs" />
    <Compile Include="Client\Models\Dashboard\IDashboardFilter.cs" />
    <Compile Include="Client\Models\BootstrapTreeNode.cs" />
    <Compile Include="Client\Models\HW\UpdateJONsModel.cs" />
    <Compile Include="Client\Models\ImportModel.cs" />
    <Compile Include="Client\Models\TimelineEventModel.cs" />
    <Compile Include="Client\QueryableResultEx.cs" />
    <Compile Include="Client\Bootstrap4\HtmlHelperExtensions.cs" />
    <Compile Include="Client\Emails\HW\PickupAssigned.cs" />
    <Compile Include="Client\Localization\DbStringProvider.cs" />
    <Compile Include="Client\Localization\ILocalizedStringProvider.cs" />
    <Compile Include="Client\Localization\LocalizableAttribute.cs" />
    <Compile Include="Client\Localization\LocalizedModelMetadataProvider.cs" />
    <Compile Include="Client\M3ViewPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Client\Models\FormButtonsModel.cs" />
    <Compile Include="Client\Models\HW\BillingMetricsOrgModel.cs" />
    <Compile Include="Client\Models\HW\EditVPICModel.cs" />
    <Compile Include="Client\Models\HW\OWFlowLogModel.cs" />
    <Compile Include="Client\Models\Dashboard\DashboardChecklistModel.cs" />
    <Compile Include="Client\Models\HW\BillingMetricsModel.cs" />
    <Compile Include="Client\Models\NOV\NovModel.cs" />
    <Compile Include="Client\Models\NOV\ActivityModel.cs" />
    <Compile Include="Client\Models\QA\DocumentTrackingModel.cs" />
    <Compile Include="Client\Models\HW\EditLocationPOCModel.cs" />
    <Compile Include="Client\Models\HW\IMapsTo.cs" />
    <Compile Include="Client\Models\HW\MonthlyReportModel.cs" />
    <Compile Include="Client\Models\HW\PcbLogModel.cs" />
    <Compile Include="Client\Models\HW\FleetReportModel.cs" />
    <Compile Include="Client\Models\HW\SummaryMetricsModel.cs" />
    <Compile Include="Client\Models\HW\SummaryMetricsOrgModel.cs" />
    <Compile Include="Client\Models\NavyExternalAuditDashboardModel.cs" />
    <Compile Include="Client\PropertyBinderAttribute.cs" />
    <Compile Include="Client\DateTimeModelBinder.cs" />
    <Compile Include="Client\SelectListItemEx.cs" />
    <Compile Include="Client\UrlHelperExtensions.cs" />
    <Compile Include="Client\ViewExtensions.cs" />
    <Compile Include="Dal\AutoMappedAttribute.cs" />
    <Compile Include="Dal\BaselineIndexes.cs" />
    <Compile Include="Dal\EmptyDisposable.cs" />
    <Compile Include="Dal\Indexes.cs" />
    <Compile Include="Dal\LazyLoadAttribute.cs" />
    <Compile Include="Dal\M3Configuration.cs" />
    <Compile Include="Dal\M3Sql2012Dialect.cs" />
    <Compile Include="Dal\MappingConventions.cs" />
    <EmbeddedResource Include="Aspose.Pdf.lic" />
    <None Include="connectionStrings.config">
      <DependentUpon>connectionStrings.config.template</DependentUpon>
    </None>
    <None Include="connectionStrings.config.template" />
    <Compile Include="Dal\Wrappers\IM3Session.cs" />
    <Compile Include="Dao\Base\IEnumerableExtensions.cs" />
    <Compile Include="Dao\Base\IQueryableExtensions.cs" />
    <Compile Include="Dao\Base\M3Queryable.cs" />
    <Compile Include="Dao\Core\DashboardDao.cs" />
    <Compile Include="Dao\Core\PageCommentDao.cs" />
    <Compile Include="Dao\HW\WasteRecordDao.cs" />
    <Compile Include="Dao\QA\NovDao.cs" />
    <Compile Include="Dao\Quiz\ElementDao.cs" />
    <Compile Include="Bll\Quiz\ScoresBll.cs" />
    <Compile Include="Bll\Quiz\QuizBll.cs" />
    <Compile Include="Bll\Radiology\WorkflowBll.cs" />
    <Compile Include="Bll\Reports\DesignerARTemplateHelper.cs" />
    <Compile Include="Bll\Reports\ExcelBll.cs" />
    <Compile Include="Bll\Reports\NoPrintLinkAttribute.cs" />
    <Compile Include="Bll\Reports\PdfPrintOptionsStream.cs" />
    <Compile Include="Bll\Reports\SearchOperators\IsNull.cs" />
    <Compile Include="Bll\Reports\SearchOperators\ListContains.cs" />
    <Compile Include="Bll\Reports\SpreadsheetTemplateBll.cs" />
    <Compile Include="Client\AllowAnyOriginAttribute.cs" />
    <Compile Include="Dal\AssociationFilter.cs" />
    <Compile Include="Dal\DapperNiftyDateHandler.cs" />
    <Compile Include="Dao\Reporting\ChartTemplateDao.cs" />
    <Compile Include="Dao\Reporting\MsWordTemplateDao.cs" />
    <Compile Include="Dao\Reporting\DesignerARTemplateDao.cs" />
    <Compile Include="Dao\Reporting\SpreadsheetTemplateDao.cs" />
    <Compile Include="Domain\AlertRules\AuditMilestoneAlertRule.cs" />
    <Compile Include="Domain\AlertRules\AuditPoamAlertRule.cs" />
    <Compile Include="Domain\AlertRules\OrgDocumentAlertRule.cs" />
    <Compile Include="Domain\AlertRules\OrgPermitAlertRule.cs" />
    <Compile Include="Domain\AlertRules\OrgPoamAlertRule.cs" />
    <Compile Include="Domain\AlertRules\OrgMilestoneAlertRule.cs" />
    <Compile Include="Domain\AlertRules\OrgFindingAlertRule.cs" />
    <Compile Include="Domain\Core\ExchangeRate.cs" />
    <Compile Include="Domain\Base\OrganizationBusinessObject.cs" />
    <Compile Include="Domain\Base\AssociationBusinessObject.cs" />
    <Compile Include="Domain\Base\ICustomDM.cs" />
    <Compile Include="Dao\Radiology\Workflow\WorkflowTemplateDao.cs" />
    <Compile Include="Domain\Contacts\IHasWasteProfile.cs" />
    <Compile Include="Domain\Contacts\OrganizationSettings\HazWasteSettings.cs" />
    <Compile Include="Domain\Contacts\OrganizationSettings\EmsSettings.cs" />
    <Compile Include="Domain\Contacts\OrganizationSettings\OrganizationSettings.cs" />
    <Compile Include="Domain\AlertRules\QuizTemplateAlertRule.cs" />
    <Compile Include="Domain\Contacts\OrganizationSettings\POLSettings.cs" />
    <Compile Include="Domain\Contacts\OrganizationSettings\POLSettings2InspectionRequirements.cs" />
    <Compile Include="Domain\Base\CurrencyHelper.cs" />
    <Compile Include="Domain\Base\CurrencyType.cs" />
    <Compile Include="Domain\Core\EmailLog.cs" />
    <Compile Include="Domain\Core\AdvancedSearch2Control.cs" />
    <Compile Include="Domain\Core\Object2Document.cs" />
    <Compile Include="Domain\Core\SavedDashboardFilter.cs" />
    <Compile Include="Domain\Core\IHasScope.cs" />
    <Compile Include="Domain\Core\PropertyMetadata.cs" />
    <Compile Include="Domain\Core\ChartTemplate.cs" />
    <Compile Include="Domain\Core\IDashboardMetric.cs" />
    <Compile Include="Domain\Core\LocalizedTerm.cs" />
    <Compile Include="Domain\Core\DesignerARTemplate.cs" />
    <Compile Include="Domain\Core\MsWordTemplate.cs" />
    <Compile Include="Domain\Core\ReferenceTypeAttribute.cs" />
    <Compile Include="Domain\Core\RenderedReport.cs" />
    <Compile Include="Domain\Core\ReportTemplate2Control.cs" />
    <Compile Include="Domain\Core\ReportTemplateMapping.cs" />
    <Compile Include="Domain\Core\SequentialNumber.cs" />
    <Compile Include="Domain\Core\SpreadsheetTemplate.cs" />
    <Compile Include="Domain\Core\SummaryEmailQueue.cs" />
    <Compile Include="Domain\Core\PageComment.cs" />
    <Compile Include="Domain\Core\ToastMessage.cs" />
    <Compile Include="Domain\Core\UserLogin.cs" />
    <Compile Include="Domain\Framework\NonQueryableAttribute.cs" />
    <Compile Include="Domain\Framework\NonReportableAttribute.cs" />
    <Compile Include="Domain\Framework\OrganizationPathAttribute.cs" />
    <Compile Include="Domain\HW\BiennialReport.cs" />
    <Compile Include="Domain\HW\ContainerUnit.cs" />
    <Compile Include="Domain\HW\Contract.cs" />
    <Compile Include="Domain\HW\DAASFileHistory.cs" />
    <Compile Include="Domain\HW\DAASForm1348Status.cs" />
    <Compile Include="Domain\HW\DAASResponse.cs" />
    <Compile Include="Domain\HW\IWasteDisposition.cs" />
    <Compile Include="Domain\HW\JobOrderUpdateLog.cs" />
    <Compile Include="Domain\HW\TrendChartSeries.cs" />
    <Compile Include="Domain\HW\TrendChart.cs" />
    <Compile Include="Domain\HW\ImportFile.cs" />
    <Compile Include="Domain\HW\BillOfLading.cs" />
    <Compile Include="Domain\HW\ContainerPreset.cs" />
    <Compile Include="Domain\HW\Customer2Person.cs" />
    <Compile Include="Domain\HW\ImportDetailPreview.cs" />
    <Compile Include="Domain\HW\ManifestContact.cs" />
    <Compile Include="Domain\HW\DeliveryOrder.cs" />
    <Compile Include="Domain\HW\ReviewDate.cs" />
    <Compile Include="Domain\HW\Facility.cs" />
    <Compile Include="Domain\HW\VPIC.cs" />
    <Compile Include="Domain\HW\BillingRun.cs" />
    <Compile Include="Domain\HW\WasteStream.cs" />
    <Compile Include="Domain\HW\CustomerLocation.cs" />
    <Compile Include="Domain\HW\ShippingHazard.cs" />
    <Compile Include="Domain\HW\Form1348.cs" />
    <Compile Include="Domain\HW\CLINSuffix.cs" />
    <Compile Include="Domain\HW\CLIN.cs" />
    <Compile Include="Domain\HW\BaseCLIN.cs" />
    <Compile Include="Domain\HW\Chemical.cs" />
    <Compile Include="Domain\HW\Manifest.cs" />
    <Compile Include="Domain\Core\GeoCoords.cs" />
    <Compile Include="Domain\AlertRules\AuditFindingAlertRule.cs" />
    <Compile Include="Domain\AlertRules\AuditAlertRule.cs" />
    <Compile Include="Domain\AlertRules\DocumentAlertRule.cs" />
    <Compile Include="Domain\AlertRules\TaskAlertRule.cs" />
    <Compile Include="Domain\AlertRules\ObjectiveAlertRule.cs" />
    <Compile Include="Domain\AlertRules\MilestoneAlertRule.cs" />
    <Compile Include="Domain\AlertRules\AuditItemAlertRule.cs" />
    <Compile Include="Domain\AlertRules\PermitAlertRule.cs" />
    <Compile Include="Domain\AlertRules\PoamAlertRule.cs" />
    <Compile Include="Domain\AlertRules\FindingAlertRule.cs" />
    <Compile Include="Domain\POL\Deficiency.cs" />
    <Compile Include="Domain\POL\InspectionCategory.cs" />
    <Compile Include="Domain\POL\InspectionEvent.cs" />
    <Compile Include="Domain\POL\InspectionRequirement.cs" />
    <Compile Include="Domain\POL\InspectionType.cs" />
    <Compile Include="Domain\POL\InventoryItem.cs" />
    <Compile Include="Domain\POL\ScheduleCriteria.cs" />
    <Compile Include="Domain\QA\GuidanceRow.cs" />
    <Compile Include="Domain\QA\GuidanceTable.cs" />
    <Compile Include="Domain\QA\GuidanceValue.cs" />
    <Compile Include="Domain\QA\GuidanceColumn.cs" />
    <Compile Include="Domain\QA\ChecklistProfile.cs" />
    <Compile Include="Domain\QA\EmsMetricsSnapshot.cs" />
    <Compile Include="Domain\QA\AuditInfo.cs" />
    <Compile Include="Domain\QA\AuditMediaScopeStatement.cs" />
    <Compile Include="Domain\QA\EmailUpdateLog.cs" />
    <Compile Include="Domain\QA\PoamAndMilestone.cs" />
    <Compile Include="Drg.Core\CustomDescriptionAttribute.cs" />
    <Compile Include="Drg.Core\Extensions.cs" />
    <Compile Include="Drg.Core\IO.cs" />
    <Compile Include="Drg.Core\JSON\Disposable.cs" />
    <Compile Include="Drg.Core\JSON\IJsonArray.cs" />
    <Compile Include="Drg.Core\JSON\IJsonBoolean.cs" />
    <Compile Include="Drg.Core\JSON\IJsonNull.cs" />
    <Compile Include="Drg.Core\JSON\IJsonNumber.cs" />
    <Compile Include="Drg.Core\JSON\IJsonObject.cs" />
    <Compile Include="Drg.Core\JSON\IJsonString.cs" />
    <Compile Include="Drg.Core\JSON\IJsonType.cs" />
    <Compile Include="Drg.Core\JSON\IJsonTypeFactory.cs" />
    <Compile Include="Drg.Core\JSON\IJsonWriter.cs" />
    <Compile Include="Drg.Core\JSON\JsonArray.cs" />
    <Compile Include="Drg.Core\JSON\JsonBoolean.cs" />
    <Compile Include="Drg.Core\JSON\JsonNull.cs" />
    <Compile Include="Drg.Core\JSON\JsonNumber.cs" />
    <Compile Include="Drg.Core\JSON\JsonObject.cs" />
    <Compile Include="Drg.Core\JSON\JsonParser.cs" />
    <Compile Include="Drg.Core\JSON\JsonString.cs" />
    <Compile Include="Drg.Core\JSON\JsonStructType.cs" />
    <Compile Include="Drg.Core\JSON\JsonTokenType.cs" />
    <Compile Include="Drg.Core\JSON\JsonTypeCode.cs" />
    <Compile Include="Drg.Core\JSON\JsonTypeFactory.cs" />
    <Compile Include="Drg.Core\JSON\JsonTypeSkeleton.cs" />
    <Compile Include="Drg.Core\JSON\JsonWriter.cs" />
    <Compile Include="Drg.Core\Parse.cs" />
    <Compile Include="Drg.Core\Security.cs" />
    <Compile Include="Extensions\DateTimeOffsetExtensions.cs" />
    <Compile Include="Extensions\DirectoryExtensions.cs" />
    <Compile Include="Extensions\ReflectionExtensions.cs" />
    <Compile Include="IM3Context.cs" />
    <Compile Include="Jobs\AlertRunner_HighFrequency.cs" />
    <Compile Include="Jobs\AuditInfoWarehouseService.cs" />
    <Compile Include="Bll\HW\DAAS\Hangfire\DAASResponsePollerJob.cs" />
    <Compile Include="Jobs\Framework\AbstractSessionJob.cs" />
    <Compile Include="Jobs\Framework\AbstractSessionJob[T].cs" />
    <Compile Include="Jobs\Framework\AbstractJob.cs" />
    <Compile Include="Jobs\Framework\AbstractJob[T].cs" />
    <Compile Include="Jobs\Framework\BackgroundJobChainer.cs" />
    <Compile Include="Jobs\Framework\BackgroundJobExtensions.cs" />
    <Compile Include="Jobs\Framework\CancelTransactionException.cs" />
    <Compile Include="Jobs\Framework\ContinuationsSupportIncludingFailedStateAttribute.cs" />
    <Compile Include="Jobs\Framework\DisableConcurrentExecutionWithReschedulingAttribute.cs" />
    <Compile Include="Jobs\Framework\DisableMultipleQueuedItemsFilterAttribute.cs" />
    <Compile Include="Jobs\Framework\Hangfire.RecurringJobCleanUpManager\RecurringJobCleanUpManager.cs" />
    <Compile Include="Jobs\Framework\Hangfire.RecurringJobCleanUpManager\RecurringJobCollection.cs" />
    <Compile Include="Jobs\Framework\Hangfire.RecurringJobCleanUpManager\RecurringJobDescriptor.cs" />
    <Compile Include="Jobs\Framework\Hangfire.RecurringJobCleanUpManager\RecurringJobRepository.cs" />
    <Compile Include="Jobs\Framework\HangfireConfigurator.cs" />
    <Compile Include="Jobs\Framework\HangfireExtensions.cs" />
    <Compile Include="Jobs\Framework\JobRequeuer.cs" />
    <Compile Include="Jobs\EmsMetricsGenerator.cs" />
    <Compile Include="Jobs\JobRegistry.cs" />
    <Compile Include="Jobs\POLAccessImportJob.cs" />
    <Compile Include="Jobs\HazWasteWarehouseFixer.cs" />
    <Compile Include="Jobs\UserNameUpdaterFailureNotifications.cs" />
    <Compile Include="Jobs\UserNameUpdater.cs" />
    <Compile Include="Jobs\SummaryEmails.cs" />
    <Compile Include="Jobs\AlertRunner_Nightly.cs" />
    <Compile Include="Jobs\SystemARTemplatePopulater.cs" />
    <Compile Include="Migrations\20250428081645_UpdatePoamOrg.cs" />
    <Compile Include="Migrations\20250428081645_UpdatePoamOrg.Designer.cs">
      <DependentUpon>20250428081645_UpdatePoamOrg.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250508174034_AddRelatedLocationsTable.cs" />
    <Compile Include="Migrations\20250508174034_AddRelatedLocationsTable.Designer.cs">
      <DependentUpon>20250508174034_AddRelatedLocationsTable.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250501134912_AddPickupDodaacFieldTo1348.cs" />
    <Compile Include="Migrations\20250501134912_AddPickupDodaacFieldTo1348.Designer.cs">
      <DependentUpon>20250501134912_AddPickupDodaacFieldTo1348.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250502052452_FixProxyChangeHistoryClassName.cs" />
    <Compile Include="Migrations\20250502052452_FixProxyChangeHistoryClassName.Designer.cs">
      <DependentUpon>20250502052452_FixProxyChangeHistoryClassName.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250525080538_InspectionCategoryDeactivate.cs" />
    <Compile Include="Migrations\20250525080538_InspectionCategoryDeactivate.Designer.cs">
      <DependentUpon>20250525080538_InspectionCategoryDeactivate.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250513164430_FixMilestoneOrg.cs" />
    <Compile Include="Migrations\20250513164430_FixMilestoneOrg.Designer.cs">
      <DependentUpon>20250513164430_FixMilestoneOrg.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250522141251_AddGuidanceTables.cs" />
    <Compile Include="Migrations\20250522141251_AddGuidanceTables.Designer.cs">
      <DependentUpon>20250522141251_AddGuidanceTables.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250522143551_AddGuidanceDefaultData.cs" />
    <Compile Include="Migrations\20250522143551_AddGuidanceDefaultData.Designer.cs">
      <DependentUpon>20250522143551_AddGuidanceDefaultData.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250530105435_AddFirstColumnIsTitle.cs" />
    <Compile Include="Migrations\20250530105435_AddFirstColumnIsTitle.Designer.cs">
      <DependentUpon>20250530105435_AddFirstColumnIsTitle.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250609095253_AddHideEmpty.cs" />
    <Compile Include="Migrations\20250609095253_AddHideEmpty.Designer.cs">
      <DependentUpon>20250609095253_AddHideEmpty.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\20250624090052_ChecklistProfile.cs" />
    <Compile Include="Migrations\20250624090052_ChecklistProfile.Designer.cs">
      <DependentUpon>20250624090052_ChecklistProfile.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\Archive\20230814100416_AddPreviousSnapshot.cs" />
    <Compile Include="Migrations\Archive\20230816145428_AddIsFrozenToSnapshot.cs" />
    <Compile Include="Migrations\Archive\20230915104416_AddCapaRelationshipFiled.cs" />
    <Compile Include="Migrations\Archive\20230911165247_AddAdditionalNotificationEmailsToFinding.cs" />
    <Compile Include="Migrations\Archive\20230913091528_AddEprPortalFundingFields.cs" />
    <Compile Include="Migrations\Archive\20230913163605_AddAssignedOrganizationsQueryable.cs" />
    <Compile Include="Migrations\Archive\20230908094525_WasteRecordRemovalDateToInheritFromTopLevelCWR.cs" />
    <Compile Include="Migrations\Archive\20231019121742_CustomerLocationNumberIndex.cs" />
    <Compile Include="Migrations\Archive\20231019122054_WasteProfileNumberIndex.cs" />
    <Compile Include="Migrations\Archive\20231031093744_RGN.cs" />
    <Compile Include="Migrations\Archive\20231114140109_WasteRecordClosedDateToInheritFromTopLevelCWR.cs" />
    <Compile Include="Migrations\Archive\20231017105317_EprIntegration_POAM_View_62196.cs" />
    <Compile Include="Migrations\Archive\20231017115919_EprIntegration_Milestone_View_62196.cs" />
    <Compile Include="Migrations\Archive\20231017125907_EprIntegration_Related_Files_View_62196.cs" />
    <Compile Include="Migrations\Archive\20231017175832_EprIntegration_Finding_View_62196.cs" />
    <Compile Include="Migrations\Archive\20240103102059_SequentialNumbers.cs" />
    <Compile Include="Migrations\Archive\20240103102253_SequentialNumbersIndex.cs" />
    <Compile Include="Migrations\Archive\20240104094752_RemovingSubjectIndex.cs" />
    <Compile Include="Migrations\Archive\20240104094916_AddingSubjectOrgIndex.cs" />
    <Compile Include="Migrations\Archive\20240124083352_AddAnalysisCompleteToWasteRecord.cs" />
    <Compile Include="Migrations\Archive\20240209102251_CloneIncludesMMC.cs" />
    <Compile Include="Migrations\Archive\20240226132353_DAASResponse.cs" />
    <Compile Include="Migrations\Archive\20240226141637_DAAS_Status.cs" />
    <Compile Include="Migrations\Archive\20240227145606_Form1348DAASReady.cs" />
    <Compile Include="Migrations\Archive\20240320093645_AddBillingRunTable.cs" />
    <Compile Include="Migrations\Archive\20240410163908_AddHasRecentInternalAudit.cs" />
    <Compile Include="Migrations\Archive\20240528151904_AddAuditInfoWarehouseFields.cs" />
    <Compile Include="Migrations\Archive\20240227174907_DAASTransmissinRecords.cs" />
    <Compile Include="Migrations\Archive\20240305135602_DAASFileHistoryIsSuccessful.cs" />
    <Compile Include="Migrations\Archive\20240305153138_DaasRecords.cs" />
    <Compile Include="Migrations\Archive\20240402125305_DAASExportStatus.cs" />
    <Compile Include="Migrations\Archive\20240402143858_DAASExportStatusNotNullable.cs" />
    <Compile Include="Migrations\Archive\20240403084254_SentContentMaxValue.cs" />
    <Compile Include="Migrations\Archive\20240403122338_FileHistoryNote.cs" />
    <Compile Include="Migrations\Archive\20240403172122_MaxFileHistoryResponse.cs" />
    <Compile Include="Migrations\Archive\20240405114227_DaasTransactionNumber.cs" />
    <Compile Include="Migrations\Archive\20240410123000_DaasFromToDetails.cs" />
    <Compile Include="Migrations\Archive\20240410140741_DaasResponseMaxLength.cs" />
    <Compile Include="Migrations\Archive\20240410141109_DaasResponseNote.cs" />
    <Compile Include="Migrations\Archive\20240514170822_AddOverrideSurchargeToRateCode.cs" />
    <Compile Include="Migrations\Archive\20240529092759_DAASResponseFilename.cs" />
    <Compile Include="Migrations\Archive\20240529102604_DaasFileHistoryFilename.cs" />
    <Compile Include="Migrations\Archive\20240529160334_SeqentialNumberNoOrg.cs" />
    <Compile Include="Migrations\Archive\20240708203042_File856UploadedAcknowledgement.cs" />
    <Compile Include="Migrations\Archive\20240702155539_MoveDcaDisplayNameOutOfXml.cs" />
    <Compile Include="Migrations\Archive\20240822093724_FixBlankAssignmentNames.cs" />
    <Compile Include="Migrations\Archive\20240826141414_POLTables.cs" />
    <Compile Include="Migrations\Archive\20241015085039_CustomAlertRule.cs" />
    <Compile Include="Migrations\Archive\20241015160258_PerRecordCustomAlerts.cs" />
    <Compile Include="Migrations\Archive\20241017135551_EMS_MAINT_62341.cs" />
    <Compile Include="Migrations\Archive\20241030113746_DoNotTruncateLongText.cs" />
    <Compile Include="Migrations\Archive\20240826142739_POLTables1.cs" />
    <Compile Include="Migrations\Archive\20240827151804_InspectionEventInspectionType.cs" />
    <Compile Include="Migrations\Archive\20240829091715_RequiringInspectionDate.cs" />
    <Compile Include="Migrations\Archive\20240903112725_InspectionEventStatus.cs" />
    <Compile Include="Migrations\Archive\20240904013240_RemovingUnneededOrgReference.cs" />
    <Compile Include="Migrations\Archive\20240904013639_OptionalInspectionDate.cs" />
    <Compile Include="Migrations\Archive\20241204113432_QuizIndex.cs" />
    <Compile Include="Migrations\Archive\20241213091216_AddClearOptions.cs" />
    <Compile Include="Migrations\Archive\20241204154621_RenameCultureToUICulture.cs" />
    <Compile Include="Migrations\Archive\20241204160740_AddCurrencyAndCultureToOrganization.cs" />
    <Compile Include="Migrations\Archive\20241206145120_CreateExchangeRate.cs" />
    <Compile Include="Migrations\Archive\20241206163149_AddExchangeRateIndex.cs" />
    <Compile Include="Migrations\Archive\20241211093322_AddCurrentExchangeRateView.cs" />
    <Compile Include="Migrations\Archive\20241212041543_AddExchangeRateTo1348AndWR.cs" />
    <Compile Include="Migrations\Archive\20241212143208_RemoveUnusedBillingRateField.cs" />
    <Compile Include="Migrations\Archive\20241220102133_RemoveDalTaskLog.cs" />
    <Compile Include="Migrations\Archive\20241220094756_AssociationNameNotNull.cs" />
    <Compile Include="Migrations\Archive\20250122095255_MakeDeliveryOrderOrganizationIdRequired.cs" />
    <Compile Include="Migrations\Archive\20250205091021_AddInheritsContractsSetting.cs" />
    <Compile Include="Migrations\Archive\20250127161733_AdditionalFilters.cs" />
    <Compile Include="Migrations\Archive\20250127161958_AdditionalFilters2.cs" />
    <Compile Include="Migrations\Archive\20250208174402_DeficiencyDescMaxLen.cs" />
    <Compile Include="Migrations\Archive\20250208175410_DeficiencyDescMaxLen2.cs" />
    <Compile Include="Migrations\Archive\20250208194204_reference_index.cs" />
    <Compile Include="Migrations\20250224100421_CleanupMilestoneEmptyJustificationNotes.cs" />
    <Compile Include="Migrations\20250224100421_CleanupMilestoneEmptyJustificationNotes.Designer.cs">
      <DependentUpon>20250224100421_CleanupMilestoneEmptyJustificationNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\Archive\20250221061100_BetaLogo.cs" />
    <Compile Include="Migrations\Archive\20230404092244_AddHomepageUrl.cs" />
    <Compile Include="Migrations\Archive\20230417102931_RedefineLatestExternalAudit.cs" />
    <Compile Include="Migrations\Archive\20230417111754_DataSharingTypeUpdate.cs" />
    <Compile Include="Migrations\Archive\20230419055521_AddShowOrganizationColumn.cs" />
    <Compile Include="Migrations\Archive\20230419101854_AddQuizTemplateAlertRule.cs" />
    <Compile Include="Migrations\Archive\20230517101314_AddAdditionalSDSs.cs" />
    <Compile Include="Migrations\Archive\20230516135015_AddMenu2TemplateOrdering.cs" />
    <Compile Include="Migrations\Archive\20230516143319_AddMenu2TemplateAlertAdmins.cs" />
    <Compile Include="Migrations\Archive\20230522095720_Add_COR_Menu2List_WordTemplate.cs" />
    <Compile Include="Migrations\Archive\20230613084426_AddEmsMetricsColumns.cs" />
    <Compile Include="Migrations\Archive\20230613092018_AddEmsMetricsOutputTemplates.cs" />
    <Compile Include="Migrations\Archive\20230614105831_AddInternalAndExternalScoresForEmsMetrics.cs" />
    <Compile Include="Migrations\Archive\20230614123500_AddAllowOtherOrgsToUseImportExport.cs" />
    <Compile Include="Migrations\Archive\20230614134600_AddFindingTabSetting.cs" />
    <Compile Include="Migrations\Archive\20230614142701_AddFindingClosedFilter.cs" />
    <Compile Include="Migrations\Archive\20230616080036_AddTotalScore.cs" />
    <Compile Include="Migrations\Archive\20230707092444_UpdateObject2AuditItemView.cs" />
    <Compile Include="Migrations\Archive\20220822123037_RedefineMediaScope.cs" />
    <Compile Include="Migrations\Archive\20220822124802_MoveDrgPersonnelToUserLogin.cs" />
    <Compile Include="Migrations\Archive\20221101134637_ConvertQuizFloatToDecimal.cs" />
    <Compile Include="Migrations\Archive\20220919142812_AddBiennialReport.cs" />
    <Compile Include="Migrations\Archive\20220920091711_AddIsBilledToWasteRecord.cs" />
    <Compile Include="Migrations\Archive\20220920092219_AddOmitFromCustomerBillingToWasteRecord.cs" />
    <Compile Include="Migrations\Archive\20221006145431_AddHW_GMReportUsesWPData.cs" />
    <Compile Include="Migrations\Archive\20221008173232_AddRequireCustomer.cs" />
    <Compile Include="Migrations\Archive\20221205083631_FixQuizDecimalColumnNames.cs" />
    <Compile Include="Migrations\Archive\20221104131437_AddIncludeInFleetReportColumn.cs" />
    <Compile Include="Migrations\Archive\20230202114403_AddIsRadioactive.cs" />
    <Compile Include="Migrations\Archive\20230202121030_AddWATreatmentByGenerator.cs" />
    <Compile Include="Migrations\Archive\20230208122606_AddGMReportUsesEpaCodesFromWP.cs" />
    <Compile Include="Migrations\Archive\20230213080408_AddWPCommentsFields.cs" />
    <Compile Include="Migrations\Archive\20230119101042_RefactorDecimalAgain.cs" />
    <Compile Include="Migrations\Archive\20230203115559_AddPageComment_Url.cs" />
    <Compile Include="Migrations\Archive\20230309124915_AddExcelTemplateFile.cs" />
    <Compile Include="Migrations\Archive\20230314143034_AddLatestExternalAuditView.cs" />
    <Compile Include="Migrations\Archive\20230315151055_AddAdvancedSearch2Control.cs" />
    <Compile Include="Migrations\Archive\20230317134409_AddFindingAndMilestoneView.cs" />
    <Compile Include="Migrations\Archive\20230308130034_AddTransferInDefaults.cs" />
    <Compile Include="Migrations\Archive\20230308132036_AddAutoSignWasteProfile.cs" />
    <Compile Include="Migrations\Archive\20230320085646_RemoveClinOrgId.cs" />
    <Compile Include="Migrations\Archive\20211123135536_AddLabPackField.cs" />
    <Compile Include="Migrations\Archive\20220111111432_ConvertFindingHistoryFromXml.cs" />
    <Compile Include="Migrations\Archive\20220126144201_AddIsExpressImportColumn.cs" />
    <Compile Include="Migrations\Archive\20220412133958_AddGencommDODAAC.cs" />
    <Compile Include="Migrations\Archive\20220420141829_AddThreeMoreTransportersOnManifest.cs" />
    <Compile Include="Migrations\Archive\20220606105524_AddNewClinFields.cs" />
    <Compile Include="Migrations\Archive\20220629111538_AddMediaScopeTable.cs" />
    <Compile Include="Migrations\Archive\20220629122355_AddMediaScopeUniqueIndex.cs" />
    <Compile Include="Migrations\Archive\20210726105400_AddEmailUpdateLogTable.cs" />
    <Compile Include="Migrations\Archive\20210818164409_AddSplitParent1348Id.cs" />
    <Compile Include="Migrations\Archive\20210902130256_MakeForm1348DCRequired.cs" />
    <Compile Include="Migrations\Archive\20210916124254_AddDtidCutoffDate.cs" />
    <Compile Include="Migrations\Archive\20211005122506_AddCustomerBillingSurcharge.cs" />
    <Compile Include="Migrations\Archive\20211005142512_AddCustomer2Location.cs" />
    <Compile Include="Migrations\Archive\20211005090857_RefactorMenu2Template.cs" />
    <Compile Include="Migrations\Archive\20211008091658_ConvertOverrideClosedFromXml.cs" />
    <Compile Include="Migrations\Archive\20211020091140_RenameDmilCode.cs" />
    <Compile Include="Migrations\Archive\20211104154353_AddedColumnAllowCreationOfBlankCWR.cs" />
    <Compile Include="Migrations\Archive\20210427083414_AutoMapOrganization.cs" />
    <Compile Include="Migrations\Archive\20210427090109_EMSOrgSettingsComponent.cs" />
    <Compile Include="Migrations\Archive\20210427092451_HWOrgSettingsComponent.cs" />
    <Compile Include="Migrations\Archive\20210427095355_AddForm1348_RI.cs" />
    <Compile Include="Migrations\Archive\20210521144909_AddMaximoCode.cs" />
    <Compile Include="Migrations\Archive\20210722142905_FixMissingDeliveryOrderOrgIds.cs" />
    <Compile Include="Migrations\Archive\20210816143819_AdditionalIWOWFields.cs" />
    <Compile Include="Migrations\Archive\20210610163636_AddAutoCloseSignOnly.cs" />
    <Compile Include="Migrations\Archive\20210602100710_AddSavedDashboardFilter.cs" />
    <Compile Include="Migrations\Archive\20210723113311_AddForm1348_FillSignatureSetting.cs" />
    <Compile Include="Migrations\Archive\20210811083754_AddCustomerLocationOnPickup.cs" />
    <Compile Include="Migrations\Archive\20210309111741_AddPhoneDescription.cs" />
    <Compile Include="Migrations\Archive\20210217142512_AddDateRangeToAlertRules.cs" />
    <Compile Include="Migrations\Archive\20210316143900_AddPropertyMetadataText.cs" />
    <Compile Include="Migrations\Archive\20210316153949_AddRegionId.cs" />
    <Compile Include="Migrations\Archive\20210401123025_AddDisableDispositionBillingValidation.cs" />
    <Compile Include="Migrations\Archive\20210408131214_AddDLA_RIC.cs" />
    <Compile Include="Migrations\Archive\20210408134547_RefactorShip.cs" />
    <Compile Include="Migrations\Archive\20210416080414_CustomerLocationRefactor2.cs" />
    <Compile Include="Migrations\Archive\20210125171813_AutoMapWasteProfile.cs" />
    <Compile Include="Migrations\Archive\20210125172705_AddWPAvailableWasteStreams.cs" />
    <Compile Include="Migrations\Archive\20210126125811_AddJobOrderComment.cs" />
    <Compile Include="Migrations\Archive\20200804143124_RemoveRadiology.cs" />
    <Compile Include="Migrations\Archive\20200811151248_AutoMapWasteRecord.cs" />
    <Compile Include="Migrations\Archive\20200811151739_AddTrackForService1348InventoryReport.cs" />
    <Compile Include="Migrations\Archive\20200812093102_AddRequireJonForService1348.cs" />
    <Compile Include="Migrations\Archive\20200812123558_AutoMapForm1348.cs" />
    <Compile Include="Migrations\Archive\20200814092820_AddFKIndexes.cs" />
    <Compile Include="Migrations\Archive\20200819145120_DeliveryOrderStaysOn1348AndWR.cs" />
    <Compile Include="Migrations\Archive\20201102111835_ReaddRegulatoryMediaTable.cs" />
    <Compile Include="Migrations\Archive\20201214101846_AddClinEstimatedQuantity.cs" />
    <Compile Include="Migrations\Archive\20210115095757_FixSpellingEstimatedQuantity.cs" />
    <Compile Include="Migrations\Archive\20210121142143_SplitTransferCounterpart.cs" />
    <Compile Include="Migrations\Archive\20200925094921_CreateMissingOrgCompanies.cs" />
    <Compile Include="Migrations\Archive\20201002145520_AddPropertyMetadata.cs" />
    <Compile Include="Migrations\Archive\20201002153500_AddPropertyMetadataIndex.cs" />
    <Compile Include="Migrations\Archive\20201002162145_InsertMetadataFromXml.cs" />
    <Compile Include="Migrations\Archive\20201006151452_SetPropertyMetadataIsAdvancedToNullByDefault.cs" />
    <Compile Include="Migrations\Archive\20201014100925_AddAdvSearchQuizTemplate.cs" />
    <Compile Include="Migrations\Archive\20201014135851_AddTemplateQuizTemplateFields.cs" />
    <Compile Include="Migrations\Archive\20201014150344_AddQuizTemplateOnChartTemplate.cs" />
    <Compile Include="Migrations\Archive\20201023131846_AutoMapFinding.cs" />
    <Compile Include="Migrations\Archive\20201023132526_AddFindingIsStandard.cs" />
    <Compile Include="Migrations\Archive\20201105134507_SpreadsheetTemplate_AddScope.cs" />
    <Compile Include="Migrations\Archive\20201106115134_RefactorOutAlertRuleXml.cs" />
    <Compile Include="Migrations\Archive\20201106124936_AddCustomAlertRule_HighFrequency.cs" />
    <Compile Include="Migrations\Archive\20201106155736_AutoMapCustomAlertRule.cs" />
    <Compile Include="Migrations\Archive\20201110112943_AddEmailLog.cs" />
    <Compile Include="Migrations\Archive\20201116134005_AddEmailLogOrganization.cs" />
    <Compile Include="Migrations\Archive\20201117140840_AddPageCommentTable.cs" />
    <Compile Include="Migrations\Archive\20200702124142_AddJobOrderUpdateLog.cs" />
    <Compile Include="Migrations\Archive\20200702125422_AutoMapJobOrder.cs" />
    <Compile Include="Migrations\Archive\20200702133503_AddCustomerNoBilling.cs" />
    <Compile Include="Migrations\Archive\20200702134807_AddResponsiblePartyType.cs" />
    <Compile Include="Migrations\Archive\20200702140909_AddResponsiblePartyNoDispositionBilling.cs" />
    <Compile Include="Migrations\Archive\20200803130538_DefaultPermissionForRestoreDeletedAudit.cs" />
    <Compile Include="Migrations\Archive\20200320110137_AddColumnVisibilityTable.cs" />
    <Compile Include="Migrations\Archive\20200513140154_MenuTableUpgrades.cs" />
    <Compile Include="Migrations\Archive\20200430135614_AddMultiplierOnCLINSuffix.cs" />
    <Compile Include="Migrations\Archive\20200504110648_AddDefaultStorageLocationId.cs" />
    <Compile Include="Migrations\Archive\20200512101425_AddWMCTo1348.cs" />
    <Compile Include="Migrations\Archive\20200513131452_AddJONSuccessor.cs" />
    <Compile Include="Migrations\Archive\20200514120806_AddToastMessages.cs" />
    <Compile Include="Migrations\Archive\20200519095704_AddContractTo1348.cs" />
    <Compile Include="Migrations\Archive\20200519161518_AddGLReport_WRFormat.cs" />
    <Compile Include="Migrations\Archive\20200520150021_ToastMessage_ColumnLengths.cs" />
    <Compile Include="Migrations\Archive\20200520152624_Form1348_ContainerIDVarcharMax.cs" />
    <Compile Include="Migrations\Archive\20200605134242_AddDispositionBillingFields.cs" />
    <Compile Include="Migrations\Archive\20200123150352_RenameCommunicationFKs.cs" />
    <Compile Include="Migrations\Archive\20200123152851_AutoMapCommunication2Object.cs" />
    <Compile Include="Migrations\Archive\20200130163749_RenameLocationToCustomer.cs" />
    <Compile Include="Migrations\Archive\20200131130756_AddCustomerOrganizationId.cs" />
    <Compile Include="Migrations\Archive\20200204152144_AutoMapReference.cs" />
    <Compile Include="Migrations\Archive\20200206083119_AddContainerUnit_ManifestAbbreviation.cs" />
    <Compile Include="Migrations\Archive\20200305093813_UpdateMaxLengths.cs" />
    <Compile Include="Migrations\Archive\20200204093554_RefactorWMC.cs" />
    <Compile Include="Migrations\Archive\20190809122529_Baseline.cs" />
    <Compile Include="Migrations\Archive\20190809130806_UpgradeToNHibernate5.cs" />
    <Compile Include="Migrations\Archive\20190812075352_RemoveCMS.cs" />
    <Compile Include="Migrations\Archive\20190813085735_RemoveObsoleteColumns.cs" />
    <Compile Include="Migrations\Archive\20190819122459_AutoMap_Compliance.cs" />
    <Compile Include="Migrations\Archive\20190819172550_FixManyToManyPrimaryKeys.cs" />
    <Compile Include="Migrations\Archive\20190820083230_FixMissingForeignKeysOnNotFoundIgnore.cs" />
    <Compile Include="Migrations\Archive\20190820132407_RefactorAuditFields.cs" />
    <Compile Include="Migrations\Archive\20190821095117_RemoveUnusedContactsFields1.cs" />
    <Compile Include="Migrations\Archive\20190913113401_FixAssetDiscriminators.cs" />
    <Compile Include="Migrations\Archive\20190925140333_ClearReviewGroupOnNonDraftFindings.cs" />
    <Compile Include="Migrations\Archive\20190821125917_AddOrganizationSettingsTable.cs" />
    <Compile Include="Migrations\Archive\20190821135132_AddLockWasteName.cs" />
    <Compile Include="Migrations\Archive\20190821160955_RefactorManifest.cs" />
    <Compile Include="Migrations\Archive\20190911092127_UndoRefactorDateArrived.cs" />
    <Compile Include="Migrations\Archive\20190924113530_FixBadEnum.cs" />
    <Compile Include="Migrations\Archive\20190926092937_Location_AddUIC.cs" />
    <Compile Include="Migrations\Archive\20190926082611_RefactorDeliveryOrder.cs" />
    <Compile Include="Migrations\Archive\20190923120619_AddContractTable.cs" />
    <Compile Include="Migrations\Archive\20190923150614_AutomapCLIN_AddOriginal.cs" />
    <Compile Include="Migrations\Archive\20191118092723_FixJapaneseTerms.cs" />
    <Compile Include="Migrations\Archive\20191106074404_RefactorWrTransferStatus.cs" />
    <Compile Include="Migrations\Archive\20191106083533_AddReadyToManifestOnDO.cs" />
    <Compile Include="Migrations\Archive\20191115084828_AutomapDeliveryOrder.cs" />
    <Compile Include="Migrations\Archive\20191119115741_AddChartTemplateSettingsToTable.cs" />
    <Compile Include="Migrations\Archive\20191120094143_RenameChartTitle.cs" />
    <Compile Include="Migrations\Archive\20191120100002_AddNewChartFields.cs" />
    <Compile Include="Migrations\Archive\20191125082715_RefactorCommunicationAsTPCH.cs" />
    <Compile Include="Migrations\Archive\20191202122544_CustomTable_AddNewDashboardColumns.cs" />
    <Compile Include="Migrations\Archive\20191202140904_CustomTable_ColumnPosition.cs" />
    <Compile Include="Migrations\Archive\20191204145549_RemoveDupTableIdColumn.cs" />
    <Compile Include="Migrations\Archive\20191204165302_MakeEntityTPCH.cs" />
    <Compile Include="Migrations\Archive\20191212131320_ManifestIdGenerated.cs" />
    <Compile Include="Migrations\Archive\20191230162419_RefactorRiser.cs" />
    <Compile Include="Migrations\Archive\20191206110342_DropAlertRuleCriteriaData.cs" />
    <Compile Include="Migrations\Archive\20191206112915_Sql2016CompatLevel.cs" />
    <Compile Include="Migrations\Archive\20200108084900_AddSummaryEmailQueueComment.cs" />
    <Compile Include="Migrations\Framework\M3ExecuteSqlStatementExpression.cs" />
    <Compile Include="Migrations\Framework\M3Migration.cs" />
    <Compile Include="Migrations\Framework\M3MigrationsConfiguration.cs" />
    <Compile Include="Modules\POL.cs" />
    <Compile Include="POLImport\Importers\DeficiencyImporter.cs" />
    <Compile Include="POLImport\Importers\ErrorMessageExtensions.cs" />
    <Compile Include="Reports\Core\IInstallationSummary.cs" />
    <Compile Include="Reports\Core\InstallationSummaryModelFactory.cs" />
    <Compile Include="POLImport\Importers\InstallationImporter.cs" />
    <Compile Include="POLImport\Importers\InventoryImporter.cs" />
    <Compile Include="POLImport\Models\AccessCitationReference.cs" />
    <Compile Include="POLImport\Models\AccessDeficiency.cs" />
    <Compile Include="POLImport\Models\BaseModel.cs" />
    <Compile Include="POLImport\Models\ImportReference.cs" />
    <Compile Include="POLImport\Importers\ReferenceImporter.cs" />
    <Compile Include="POLImport\Models\Installation.cs" />
    <Compile Include="POLImport\Models\Inventory.cs" />
    <Compile Include="POLImport\POLAccessImport.cs" />
    <Compile Include="POLImport\SupportsReferenceMergeAttribute.cs" />
    <Compile Include="Reports\Core\InternalAuditReportModel.cs" />
    <Compile Include="Reports\Core\ExternalAuditReportModel.cs" />
    <Compile Include="Reports\Core\RegulatoryDeskModel.cs" />
    <Compile Include="Reports\Core\SLCCReportModel.cs" />
    <Compile Include="Reports\HW\WeeklyInventory_Corpus.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\WeeklyInventory_Corpus.Designer.cs">
      <DependentUpon>WeeklyInventory_Corpus.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\WRAndGMForBiennial.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\WRAndGMForBiennial.Designer.cs">
      <DependentUpon>WRAndGMForBiennial.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\DispositionCostByResponsibleParty_OtherServices.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\DispositionCostByResponsibleParty_OtherServices.Designer.cs">
      <DependentUpon>DispositionCostByResponsibleParty_OtherServices.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\DispositionCostByResponsibleParty.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\DispositionCostByResponsibleParty.Designer.cs">
      <DependentUpon>DispositionCostByResponsibleParty.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Models\GMModel.cs" />
    <Compile Include="Reports\HW\Models\WRExportModel.cs" />
    <Compile Include="Reports\HW\Models\P2ADSExportModel.cs" />
    <Compile Include="Reports\HW\Models\WRModel.cs" />
    <Compile Include="Reports\HW\GM_ByWasteStream.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Service1348InventoryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Service1348InventoryReport.Designer.cs">
      <DependentUpon>Service1348InventoryReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\P2ADS_ByRp.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_WithTier2_Sec41.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_WithTier2_Sec41.Designer.cs">
      <DependentUpon>InternalAuditReport_WithTier2_Sec41.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_WithTier2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalComplianceDeficiencyForm_CFAY.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalComplianceDeficiencyForm_CFAY.Designer.cs">
      <DependentUpon>InternalComplianceDeficiencyForm_CFAY.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\ReportModel.cs" />
    <Compile Include="StringWriterWithEncoding.cs" />
    <Compile Include="System.Linq.Dynamic\BasicQueryable.cs" />
    <Compile Include="System.Linq.Dynamic\ClassFactory.cs" />
    <Compile Include="System.Linq.Dynamic\DynamicClass.cs" />
    <Compile Include="System.Linq.Dynamic\DynamicExpression.cs" />
    <Compile Include="System.Linq.Dynamic\DynamicLinqTypeProvider.cs" />
    <Compile Include="System.Linq.Dynamic\DynamicOrdering.cs" />
    <Compile Include="System.Linq.Dynamic\DynamicProperty.cs" />
    <Compile Include="System.Linq.Dynamic\DynamicQueryable.cs" />
    <Compile Include="System.Linq.Dynamic\ExpressionParser.cs" />
    <Compile Include="System.Linq.Dynamic\GlobalConfig.cs" />
    <Compile Include="System.Linq.Dynamic\GroupResult.cs" />
    <Compile Include="System.Linq.Dynamic\IDynamicLinkTypeProvider.cs" />
    <Compile Include="System.Linq.Dynamic\ParseException.cs" />
    <Compile Include="System.Linq.Dynamic\Res.cs" />
    <Compile Include="Bll\HW\DAAS\SSHDAASFileHandler.cs" />
    <Compile Include="WordReports\AbstractSystemWordReportModel.cs" />
    <Compile Include="WordReports\QA\Checklist\ComplianceTrackingReport.cs" />
    <Compile Include="WordReports\QA\Checklist\NegativeChecklistReportByEquipment.cs" />
    <Compile Include="WordReports\QA\Checklist\NegativeChecklistReport.cs" />
    <Compile Include="Domain\HW\RateCode.cs" />
    <Compile Include="Domain\HW\LSN.cs" />
    <Compile Include="Domain\HW\StorageLocation.cs" />
    <Compile Include="Domain\HW\Customer.cs" />
    <Compile Include="Domain\HW\Pickup.cs" />
    <Compile Include="Domain\HW\JobOrder.cs" />
    <Compile Include="Domain\HW\ResponsibleParty.cs" />
    <Compile Include="Domain\HW\WasteProfile.cs" />
    <Compile Include="Domain\HW\WasteRecordCLIN.cs" />
    <Compile Include="Domain\HW\WasteRecordRate.cs" />
    <Compile Include="Domain\HW\WasteRecord.cs" />
    <Compile Include="Domain\Core\SystemARTemplate.cs" />
    <Compile Include="Domain\Core\TextTemplate.cs" />
    <Compile Include="Domain\QA\DocumentResponse.cs" />
    <Compile Include="Domain\QA\DocumentLevel.cs" />
    <Compile Include="Domain\QA\ChecklistImportDetail.cs" />
    <Compile Include="Domain\QA\ChecklistImport.cs" />
    <Compile Include="Domain\QA\Object2AuditItem.cs" />
    <Compile Include="Domain\QA\RequestedDocument.cs" />
    <Compile Include="Domain\QA\SimpleAuditPoam.cs" />
    <Compile Include="Modules\HW.cs" />
    <Compile Include="Dto\ReportEntityDto.cs" />
    <Compile Include="PeriodicService\ChecklistTemplateFixer.cs" />
    <Compile Include="PeriodicService\SummaryEmailService.cs" />
    <Compile Include="Client\CommaSeparatedModelBinder.cs" />
    <Compile Include="Client\ControlGroupExtensions.cs" />
    <Compile Include="Client\DecimalModelBinder.cs" />
    <Compile Include="Client\DefaultDictionaryBinder.cs" />
    <Compile Include="Client\Emails\QA\FindingTenantPOCsAssignmentNotification.cs" />
    <Compile Include="Client\Emails\QA\FindingTenantPOCsNotification.cs" />
    <Compile Include="Client\Emails\QA\FindingUpdatedNotification.cs" />
    <Compile Include="Client\Fields\AbstractField.cs" />
    <Compile Include="Client\Fields\AddressField.cs" />
    <Compile Include="Client\Fields\CheckBoxField.cs" />
    <Compile Include="Client\Fields\DdlField.cs" />
    <Compile Include="Client\Fields\PhoneField.cs" />
    <Compile Include="Client\Fields\DateField.cs" />
    <Compile Include="Client\Fields\IHtmlField.cs" />
    <Compile Include="Client\Fields\ReferenceDdlField.cs" />
    <Compile Include="Client\Fields\TextField.cs" />
    <Compile Include="Client\FileStreamDownloadResult.cs" />
    <Compile Include="Client\FixedLengthResult.cs" />
    <Compile Include="Client\HtmlHelperExtensions.cs" />
    <Compile Include="Client\CssMinifier.cs" />
    <Compile Include="Client\Emails\Core\TaskNotification.cs" />
    <Compile Include="Client\InheritedClassModelBinder.cs" />
    <Compile Include="Client\InputExtensions.cs" />
    <Compile Include="Client\M3ApplicationState.cs" />
    <Compile Include="Client\M3DataException.cs" />
    <Compile Include="Client\Models\AddressEditorModel.cs" />
    <Compile Include="Client\NamespacedRazorViewEngine.cs" />
    <Compile Include="Client\NiftyDateModelBinder.cs" />
    <Compile Include="Client\NodaTimeExtensions.cs" />
    <Compile Include="Client\NonValidatingModelBinder.cs" />
    <Compile Include="Client\RazorComponentScriptExtensions.cs" />
    <Compile Include="Client\RouteUtils.cs" />
    <Compile Include="Client\TemplateControlExtensions.cs" />
    <Compile Include="Client\TimeSpanModelBinder.cs" />
    <Compile Include="Client\Validation\ValidationException.cs" />
    <Compile Include="Client\Validation\Validators\Address.cs" />
    <Compile Include="Client\Validation\Validators\TrimmedEmail.cs" />
    <Compile Include="Client\Validation\Validators\MinLength.cs" />
    <Compile Include="Controls\Grid\Enums.cs" />
    <Compile Include="Dal\ConnectionHelper.cs" />
    <Compile Include="Dal\DatabaseUpdater.cs" />
    <Compile Include="Reports\Core\DataCallModel.cs" />
    <Compile Include="Reports\HW\BillOfLading.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\BillOfLading.Designer.cs">
      <DependentUpon>BillOfLading.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\BillingSummaryByCustomer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\BillingSummaryByCustomer.Designer.cs">
      <DependentUpon>BillingSummaryByCustomer.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\ContainerReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\ContainerReport.Designer.cs">
      <DependentUpon>ContainerReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\BillingSummaryByRP.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\BillingSummaryByRP.Designer.cs">
      <DependentUpon>BillingSummaryByRP.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\WasteRecordsByStorageLocation.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\WasteRecordsByStorageLocation.Designer.cs">
      <DependentUpon>WasteRecordsByStorageLocation.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\DispositionDetailByBillingUIC.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\DispositionDetailByBillingUIC.Designer.cs">
      <DependentUpon>DispositionDetailByBillingUIC.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Form1348SeparatePages.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\HwDrumLabelCleanSW.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\HwDrumLabelCleanSW.Designer.cs">
      <DependentUpon>HwDrumLabelCleanSW.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\HwDrumLabelClean.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\HwDrumLabelClean.Designer.cs">
      <DependentUpon>HwDrumLabelClean.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\NonRegulatedDrumLabelClean.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\NonRegulatedDrumLabelClean.Designer.cs">
      <DependentUpon>NonRegulatedDrumLabelClean.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\ManifestContinuationPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\ManifestContinuationPage.Designer.cs">
      <DependentUpon>ManifestContinuationPage.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Form1348PackingDetails.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Form1348PackingDetails.Designer.cs">
      <DependentUpon>Form1348PackingDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\DrumLabelPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\DrumLabelPage.Designer.cs">
      <DependentUpon>DrumLabelPage.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\IHasDateRange.cs" />
    <Compile Include="Reports\HW\OffsiteGM.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\P2ADS.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\P2ADS.Designer.cs">
      <DependentUpon>P2ADS.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\InventoryByLocation.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\InventoryByLocation.Designer.cs">
      <DependentUpon>InventoryByLocation.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\GM.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\GM.Designer.cs">
      <DependentUpon>GM.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Form1348PackingList.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Form1348PackingList.Designer.cs">
      <DependentUpon>Form1348PackingList.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\CwrPackingList.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\CwrPackingList.Designer.cs">
      <DependentUpon>CwrPackingList.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\DrumList.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\DrumList.Designer.cs">
      <DependentUpon>DrumList.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Form1348.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Form1348.Designer.cs">
      <DependentUpon>Form1348.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\DrumLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\DrumLabel.Designer.cs">
      <DependentUpon>DrumLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\HwDrumLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\HwDrumLabel.Designer.cs">
      <DependentUpon>HwDrumLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\UsedOilDrumLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\UsedOilDrumLabel.Designer.cs">
      <DependentUpon>UsedOilDrumLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\UniversalDrumLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\UniversalDrumLabel.Designer.cs">
      <DependentUpon>UniversalDrumLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\NonRcraRegulatedDrumLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\NonRcraRegulatedDrumLabel.Designer.cs">
      <DependentUpon>NonRcraRegulatedDrumLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\NonRegulatedDrumLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\NonRegulatedDrumLabel.Designer.cs">
      <DependentUpon>NonRegulatedDrumLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\LDR.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\LDR.Designer.cs">
      <DependentUpon>LDR.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Manifest.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Manifest.Designer.cs">
      <DependentUpon>Manifest.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Manifest_Detail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Manifest_Detail.Designer.cs">
      <DependentUpon>Manifest_Detail.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Manifest_Page2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Manifest_Page2.Designer.cs">
      <DependentUpon>Manifest_Page2.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\PickupList.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\PickupList.Designer.cs">
      <DependentUpon>PickupList.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\Labels\SmallLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\Labels\SmallLabel.Designer.cs">
      <DependentUpon>SmallLabel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\PickupReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\PickupReceipt.Designer.cs">
      <DependentUpon>PickupReceipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\SupplementalBiennialReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\SupplementalBiennialReport.Designer.cs">
      <DependentUpon>SupplementalBiennialReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\SupplementalBiennialReport_Codes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\SupplementalBiennialReport_Codes.Designer.cs">
      <DependentUpon>SupplementalBiennialReport_Codes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\WasteProfile\sub_ChemicalComponents.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\WasteProfile\sub_ChemicalComponents.Designer.cs">
      <DependentUpon>sub_ChemicalComponents.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\WasteProfile\WasteProfile.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\WasteProfile\WasteProfile.Designer.cs">
      <DependentUpon>WasteProfile.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\HW\WR.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\HW\WR.Designer.cs">
      <DependentUpon>WR.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\ICanHazFilterValues.cs" />
    <Compile Include="Reports\INeedsOrganization.cs" />
    <Compile Include="Reports\INeedsUser.cs" />
    <Compile Include="Reports\INoPrintScaling.cs" />
    <Compile Include="Reports\QA\AuditChecklistReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditChecklistReport.Designer.cs">
      <DependentUpon>AuditChecklistReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ChecklistReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ChecklistReport.Designer.cs">
      <DependentUpon>ChecklistReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ChecklistReport_SimpleTable.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ChecklistReport_SimpleTable.Designer.cs">
      <DependentUpon>ChecklistReport_SimpleTable.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ChecklistReport_AuditTable.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ChecklistReport_AuditTable.Designer.cs">
      <DependentUpon>ChecklistReport_AuditTable.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\DetailsTable.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\DetailsTable.Designer.cs">
      <DependentUpon>DetailsTable.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\IReportModel.cs" />
    <Compile Include="Reports\SectionReportDesignerShell.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\StripHtmlReportOutputHandler.cs" />
    <None Include="Tests\DevOnly\DatabaseUpdateFacts.cs" />
    <Compile Include="TypeDescriptorDynamicWrapper.cs" />
    <Compile Include="Upload\FineUpload.cs" />
    <Compile Include="Upload\FineUploadResult.cs" />
    <Compile Include="Dal\M3SessionSingleton.cs" />
    <EmbeddedResource Include="Reports\HW\BillOfLading.resx">
      <DependentUpon>BillOfLading.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\BillingSummaryByCustomer.resx">
      <DependentUpon>BillingSummaryByCustomer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\ContainerReport.resx">
      <DependentUpon>ContainerReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\BillingSummaryByRP.resx">
      <DependentUpon>BillingSummaryByRP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\WeeklyInventory_Corpus.resx">
      <DependentUpon>WeeklyInventory_Corpus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\WRAndGMForBiennial.resx">
      <DependentUpon>WRAndGMForBiennial.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\DispositionCostByResponsibleParty_OtherServices.resx">
      <DependentUpon>DispositionCostByResponsibleParty_OtherServices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\DispositionCostByResponsibleParty.resx">
      <DependentUpon>DispositionCostByResponsibleParty.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Service1348InventoryReport.resx">
      <DependentUpon>Service1348InventoryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\WasteRecordsByStorageLocation.resx">
      <DependentUpon>WasteRecordsByStorageLocation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\DispositionDetailByBillingUIC.resx">
      <DependentUpon>DispositionDetailByBillingUIC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\HwDrumLabelCleanSW.resx">
      <DependentUpon>HwDrumLabelCleanSW.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\HwDrumLabelClean.resx">
      <DependentUpon>HwDrumLabelClean.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\NonRegulatedDrumLabelClean.resx">
      <DependentUpon>NonRegulatedDrumLabelClean.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\ManifestContinuationPage.resx">
      <DependentUpon>ManifestContinuationPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Form1348PackingDetails.resx">
      <DependentUpon>Form1348PackingDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\DrumLabelPage.resx">
      <DependentUpon>DrumLabelPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\P2ADS.resx">
      <DependentUpon>P2ADS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\InventoryByLocation.resx">
      <DependentUpon>InventoryByLocation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\GM.resx">
      <DependentUpon>GM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Form1348PackingList.resx">
      <DependentUpon>Form1348PackingList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\CwrPackingList.resx">
      <DependentUpon>CwrPackingList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\DrumList.resx">
      <DependentUpon>DrumList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Form1348.resx">
      <DependentUpon>Form1348.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\DrumLabel.resx">
      <DependentUpon>DrumLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\HwDrumLabel.resx">
      <DependentUpon>HwDrumLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\UsedOilDrumLabel.resx">
      <DependentUpon>UsedOilDrumLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\UniversalDrumLabel.resx">
      <DependentUpon>UniversalDrumLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\NonRcraRegulatedDrumLabel.resx">
      <DependentUpon>NonRcraRegulatedDrumLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\NonRegulatedDrumLabel.resx">
      <DependentUpon>NonRegulatedDrumLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\LDR.resx">
      <DependentUpon>LDR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Manifest.resx">
      <DependentUpon>Manifest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Manifest_Detail.resx">
      <DependentUpon>Manifest_Detail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Manifest_Page2.resx">
      <DependentUpon>Manifest_Page2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\PickupList.resx">
      <DependentUpon>PickupList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\Labels\SmallLabel.resx">
      <DependentUpon>SmallLabel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\PickupReceipt.resx">
      <DependentUpon>PickupReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\SupplementalBiennialReport.resx">
      <DependentUpon>SupplementalBiennialReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\SupplementalBiennialReport_Codes.resx">
      <DependentUpon>SupplementalBiennialReport_Codes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\WasteProfile\sub_ChemicalComponents.resx">
      <DependentUpon>sub_ChemicalComponents.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\WasteProfile\WasteProfile.resx">
      <DependentUpon>WasteProfile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\HW\WR.resx">
      <DependentUpon>WR.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ChecklistReport.resx">
      <DependentUpon>ChecklistReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ChecklistReport_SimpleTable.resx">
      <DependentUpon>ChecklistReport_SimpleTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ChecklistReport_AuditTable.resx">
      <DependentUpon>ChecklistReport_AuditTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalAuditReport_WithTier2_Sec41.resx">
      <DependentUpon>InternalAuditReport_WithTier2_Sec41.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\DetailsTable.resx">
      <DependentUpon>DetailsTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalComplianceDeficiencyForm_CFAY.resx">
      <DependentUpon>InternalComplianceDeficiencyForm_CFAY.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="evointernal.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <EmbeddedResource Include="Dal\Mapping\QA\TESpecies2Org.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\TESpeciesLink.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\InstallationProfile.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\TESpecies2SpecialArea.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\QA\InrmpProject.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\Inrmp.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\SummaryEmailQueue.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Core\SubTask.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Radiology\Workflow\Workflow.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Radiology\Workflow\WorkflowStep.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Radiology\Workflow\WorkflowTemplate.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Radiology\Workflow\WorkflowTemplateStep.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Radiology\Workflow\WorkflowStepLog.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Dao\Base\M3QueryOver.cs" />
    <Compile Include="Dao\Base\M3DetachedCriteria.cs" />
    <Compile Include="Dal\DeletedFilter.cs" />
    <Compile Include="Dal\RecordNotFoundException.cs" />
    <Compile Include="Dal\SchemaUpdater.cs" />
    <EmbeddedResource Include="Dal\Mapping\Core\DCAStatusHistory.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\ErrorGroup.hbm.xml" />
    <Compile Include="Dao\Base\ArithmeticOperatorProjection.cs" />
    <Compile Include="Dao\Base\OperatorProjection.cs" />
    <Compile Include="Dao\Base\PredicateBuilder.cs" />
    <Compile Include="Dao\Core\BLICommunicationDao.cs" />
    <Compile Include="Dao\Core\CustomChartDao.cs" />
    <Compile Include="Dao\Core\EmailTemplateDao.cs" />
    <Compile Include="Dao\Core\ErrorGroupDao.cs" />
    <Compile Include="Dao\DaoExtensions.cs" />
    <Compile Include="Dao\QA\AuditItemDao.cs" />
    <Compile Include="Dao\QA\InrmpDao.cs" />
    <Compile Include="Dao\QA\InstallationProfileDao.cs" />
    <Compile Include="Dao\QA\ImpactDao.cs" />
    <Compile Include="Dao\QA\SpeciesQuizDao.cs" />
    <Compile Include="Dao\QA\TESpecies2SpecialAreaDao.cs" />
    <Compile Include="Dao\QA\ListBuilderItemDao.cs" />
    <Compile Include="Dao\QA\TenantDao.cs" />
    <Compile Include="Dao\Radiology\Workflow\WorkflowStepDao.cs" />
    <Compile Include="Dao\Reporting\AggregatesDao.cs" />
    <Compile Include="Dao\Reporting\DCAStakeholderScoreDao.cs" />
    <Compile Include="Dao\Reporting\CustomTableDao.cs" />
    <Compile Include="Dao\SqlBuilder.cs" />
    <Compile Include="Domain\Base\CompositeKeyPersistentObject.cs" />
    <Compile Include="Domain\Base\NiftyDate.cs" />
    <Compile Include="Domain\Base\PersistentObjectBase.cs" />
    <Compile Include="Domain\Base\RenamedClassSerializationBinder.cs" />
    <Compile Include="Domain\Core\BLICommunication.cs" />
    <Compile Include="Domain\Core\SubTask.cs" />
    <Compile Include="Domain\Core\DCAStakeholderScore.cs" />
    <Compile Include="Domain\Core\CustomTableColumn.cs" />
    <Compile Include="Domain\Core\CustomTable.cs" />
    <Compile Include="Domain\Core\CustomChart.cs" />
    <Compile Include="Domain\Core\DCAStatusHistory.cs" />
    <Compile Include="Domain\Core\EmailTemplate.cs" />
    <Compile Include="Domain\Core\ErrorGroup.cs" />
    <Compile Include="Domain\QA\Inrmp.cs" />
    <Compile Include="Domain\QA\InrmpProject.cs" />
    <Compile Include="Domain\QA\SpeciesQuiz.cs" />
    <Compile Include="Domain\QA\TESpecies2SpecialArea.cs" />
    <Compile Include="Domain\QA\InstallationProfile.cs" />
    <Compile Include="Domain\QA\TESpeciesLink.cs" />
    <Compile Include="Domain\QA\ListBuilderItem.cs" />
    <Compile Include="Domain\QA\Tenant.cs" />
    <Compile Include="Domain\QA\IOverrideInactive.cs" />
    <Compile Include="Domain\QA\Object2AuditTemplate.cs" />
    <Compile Include="Domain\QA\Object2ChecklistTemplate.cs" />
    <Compile Include="Domain\QA\TESpecies2Org.cs" />
    <Compile Include="Domain\Quiz\Elements\Checkbox.cs" />
    <Compile Include="Domain\Quiz\Elements\LabelNoStyle.cs" />
    <Compile Include="Domain\Quiz\Elements\Output.cs" />
    <Compile Include="Domain\Quiz\Elements\DocumentLink.cs" />
    <Compile Include="Dao\Reporting\DataCallAssignmentDao.cs" />
    <Compile Include="Dao\QA\ChecklistTemplateDao.cs" />
    <Compile Include="Dao\Quiz\TemplateDao.cs" />
    <Compile Include="Domain\Core\DataCallComment.cs" />
    <Compile Include="Domain\Core\EnumMonth.cs" />
    <Compile Include="DrgErrorMailModule.cs" />
    <Compile Include="DynamicHelper.cs" />
    <Compile Include="DynamicPropertyDescriptor.cs" />
    <Compile Include="DynamicTypeDescriptorWrapper.cs" />
    <Compile Include="Domain\Radiology\Workflow\Workflow.cs" />
    <Compile Include="Domain\Radiology\Workflow\WorkflowStep.cs" />
    <Compile Include="Domain\Radiology\Workflow\WorkflowStepLog.cs" />
    <Compile Include="Domain\Radiology\Workflow\WorkflowTemplate.cs" />
    <Compile Include="Domain\Radiology\Workflow\WorkflowTemplateStep.cs" />
    <Compile Include="Emails\AbstractEmailTemplate.cs" />
    <Compile Include="Modules\AbstractModule.cs" />
    <Compile Include="Modules\Calendar.cs" />
    <Compile Include="Modules\Compliance.cs" />
    <Compile Include="Modules\Core.cs" />
    <Compile Include="Modules\IModule.cs" />
    <Compile Include="Modules\ModuleAttribute.cs" />
    <Compile Include="Modules\QA.cs" />
    <Compile Include="Reports\Contacts\DefaultEmployerAvery5160.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\DefaultEmployerAvery5160.designer.cs">
      <DependentUpon>DefaultEmployerAvery5160.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\IGroupedReport.cs" />
    <Compile Include="Charts\QA\ExternalDeficiencies.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\QA\ExternalDeficiencies.Designer.cs">
      <DependentUpon>ExternalDeficiencies.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\INeedsSession.cs" />
    <Compile Include="Reports\NoBufferAttribute.cs" />
    <Compile Include="Reports\QA\AuditItemChecklistComments_InspectionComment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditItemChecklistComments_InspectionComment.Designer.cs">
      <DependentUpon>AuditItemChecklistComments_InspectionComment.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_FindingsCountISOCategory.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_FindingsCountISOCategory.Designer.cs">
      <DependentUpon>InternalAuditReport_FindingsCountISOCategory.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_FindingsCountISO.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_FindingsCountISO.Designer.cs">
      <DependentUpon>InternalAuditReport_FindingsCountISO.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_FindingsCountMedia.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_FindingsCountMedia.Designer.cs">
      <DependentUpon>InternalAuditReport_FindingsCountMedia.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_MediaFindings.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport_MediaFindings.Designer.cs">
      <DependentUpon>InternalAuditReport_MediaFindings.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalAuditReport.Designer.cs">
      <DependentUpon>InternalAuditReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief2_Findings.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief2_Findings.Designer.cs">
      <DependentUpon>ExternalAuditFindingOutbrief2_Findings.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief_Audit.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief_Audit.Designer.cs">
      <DependentUpon>ExternalAuditFindingOutbrief_Audit.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingSummary.Designer.cs">
      <DependentUpon>ExternalAuditFindingSummary.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditComplianceChecklist.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditComplianceChecklist.Designer.cs">
      <DependentUpon>ExternalAuditComplianceChecklist.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditReport.Designer.cs">
      <DependentUpon>ExternalAuditReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Roles\AbstractRoleType.cs" />
    <Compile Include="Roles\ContactCompany.cs" />
    <Compile Include="Roles\ContactPerson.cs" />
    <Compile Include="Roles\IRoleType.cs" />
    <Compile Include="Roles\Organization.cs" />
    <Compile Include="Roles\ProfileBlocks\ActionProfileBlock.cs" />
    <Compile Include="Roles\ProfileBlocks\IProfileBlock.cs" />
    <Compile Include="Roles\ProfileBlocks\PartialProfileBlock.cs" />
    <Compile Include="Roles\Staff.cs" />
    <Compile Include="Roles\User.cs" />
    <Compile Include="ToStringableList.cs" />
    <EmbeddedResource Include="Dal\Mapping\QA\Tenant.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\CustomChart.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\QA\ListBuilderItem.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\EmailTemplate.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <None Include="Dal\Mapping\QA\Object2AuditTemplate.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\Object2ChecklistTemplate.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\DataCallComment.hbm.xml" />
    <Compile Include="Domain\Base\IHasEntity.cs" />
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Dao\Base\ExecutedDaoResult.cs" />
    <Compile Include="Dao\Base\Extensions.cs" />
    <Compile Include="Dao\Base\IDaoResult.cs" />
    <Compile Include="Dao\Compliance\MonitoringProfileDao.cs" />
    <Compile Include="Dao\Compliance\SampleDao.cs" />
    <Compile Include="Dao\Core\BookmarkDao.cs" />
    <Compile Include="Dao\Core\AssociationDao.cs" />
    <Compile Include="Dao\Core\ErrorDao.cs" />
    <Compile Include="Dao\Core\ChangeHistoryDao.cs" />
    <Compile Include="Dao\Reporting\DataCallDao.cs" />
    <Compile Include="Dao\Core\MonitoredSubstanceDao.cs" />
    <Compile Include="Dao\QA\BuiltEnvironmentDao.cs" />
    <Compile Include="Dao\QA\TESpeciesDao.cs" />
    <Compile Include="Dao\QA\MediaDao.cs" />
    <Compile Include="Dao\QA\PermitDao.cs" />
    <Compile Include="Dao\QA\ObjectiveDao.cs" />
    <Compile Include="Dao\QA\MediaStatisticsDao.cs" />
    <Compile Include="Dao\QA\SpecialAreaDao.cs" />
    <Compile Include="Dao\Quiz\QuizDao.cs" />
    <Compile Include="Domain\Base\IHasInactive.cs" />
    <Compile Include="Domain\Base\IHasNotes.cs" />
    <Compile Include="Domain\Compliance\MonitoringProfile.cs" />
    <Compile Include="Domain\Compliance\MonitoredComponent.cs" />
    <Compile Include="Domain\Compliance\MonitoredSubstance.cs" />
    <Compile Include="Domain\Compliance\Sample.cs" />
    <Compile Include="Domain\Compliance\SampleComponent.cs" />
    <Compile Include="Domain\Core\Bookmark.cs" />
    <Compile Include="Domain\Core\DataCallGroup.cs" />
    <Compile Include="Domain\Core\DataCallAssignment.cs" />
    <Compile Include="Domain\Core\DataCall.cs" />
    <Compile Include="Domain\Core\Communication2User.cs" />
    <Compile Include="Domain\Core\Error.cs" />
    <Compile Include="Domain\Help\Topic2Page.cs" />
    <Compile Include="Domain\Contacts\EntitySearchResult.cs" />
    <Compile Include="Domain\Core\OrganizationFilterAttribute.cs" />
    <Compile Include="Domain\Core\IShareable.cs" />
    <Compile Include="Domain\Core\MergeTemplate.cs" />
    <Compile Include="Domain\Core\ICalendarItem.cs" />
    <Compile Include="Domain\Core\DateAndStatusAlertRule.cs" />
    <Compile Include="Bll\Core\CustomFieldBll.cs" />
    <Compile Include="Bll\Core\ElmahSqlLog.cs" />
    <Compile Include="Bll\Core\IAlertRule.cs" />
    <Compile Include="Domain\Help\TutorialTopic.cs" />
    <Compile Include="Domain\QA\AuditGroup.cs" />
    <Compile Include="Domain\QA\BuiltEnvironment.cs" />
    <Compile Include="Domain\QA\TESpecies.cs" />
    <Compile Include="Domain\QA\SpecialArea.cs" />
    <Compile Include="Domain\QA\WaterSystemOperator.cs" />
    <Compile Include="Domain\QA\User2Media.cs" />
    <Compile Include="Domain\QA\MediaProfile.cs" />
    <Compile Include="Domain\QA\LocalPracticeType.cs" />
    <Compile Include="Domain\Quiz\Answer.cs" />
    <Compile Include="Domain\Quiz\Elements\Warehouse.cs" />
    <Compile Include="Domain\Quiz\Elements\IScorableQuestion.cs" />
    <Compile Include="Domain\Quiz\Elements\UserControl.cs" />
    <Compile Include="Domain\Quiz\Elements\LookupMultiSelect.cs" />
    <Compile Include="Domain\Quiz\Elements\Lookup.cs" />
    <Compile Include="Domain\Quiz\Elements\Table.cs" />
    <Compile Include="Domain\Quiz\QuizExtensions.cs" />
    <Compile Include="Domain\Quiz\View.cs" />
    <Compile Include="Domain\Quiz\ViewElement.cs" />
    <Compile Include="Domain\Quiz\Enums.cs" />
    <Compile Include="Domain\Quiz\Element.cs" />
    <Compile Include="Domain\Quiz\Elements\RichText.cs" />
    <Compile Include="Domain\Quiz\Elements\MultipleSelect.cs" />
    <Compile Include="Domain\Quiz\Elements\LongText.cs" />
    <Compile Include="Domain\Quiz\Elements\ShortText.cs" />
    <Compile Include="Domain\Quiz\Elements\Integer.cs" />
    <Compile Include="Domain\Quiz\Elements\Currency.cs" />
    <Compile Include="Domain\Quiz\Elements\ValidElementTypesAttribute.cs" />
    <Compile Include="Domain\Quiz\Option.cs" />
    <Compile Include="Domain\Quiz\Question.cs" />
    <Compile Include="Domain\Quiz\Template.cs" />
    <Compile Include="Domain\Quiz\Quiz.cs" />
    <Compile Include="Domain\Quiz\Elements\ColoredIndicator.cs" />
    <Compile Include="Domain\Quiz\Elements\CommentableQuestion.cs" />
    <Compile Include="Domain\Quiz\Elements\Phone.cs" />
    <Compile Include="Domain\Quiz\Elements\Email.cs" />
    <Compile Include="Domain\Quiz\Elements\Date.cs" />
    <Compile Include="Domain\Quiz\Elements\Document.cs" />
    <Compile Include="Domain\Quiz\Elements\IHasOptions.cs" />
    <Compile Include="Domain\Quiz\Elements\IHasElements.cs" />
    <Compile Include="Domain\Quiz\Elements\Label.cs" />
    <Compile Include="Domain\Quiz\Elements\MultipleChoice.cs" />
    <Compile Include="Domain\Quiz\Elements\Number.cs" />
    <Compile Include="Domain\Quiz\Elements\Percentage.cs" />
    <Compile Include="Domain\Quiz\Elements\RankedQuestion.cs" />
    <Compile Include="Domain\Quiz\Elements\RankedSection.cs" />
    <Compile Include="Domain\Quiz\Elements\Section.cs" />
    <Compile Include="Domain\Quiz\Elements\Text.cs" />
    <Compile Include="Domain\Quiz\Elements\YesNo.cs" />
    <Compile Include="Domain\Quiz\Elements\YesNoNa.cs" />
    <Compile Include="AssociationAttribute.cs" />
    <Compile Include="Reports\Contacts\Avery5160.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Avery5160.Designer.cs">
      <DependentUpon>Avery5160.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\CompanyAvery5160.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\CompanyAvery5160.Designer.cs">
      <DependentUpon>CompanyAvery5160.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditSummary.Designer.cs">
      <DependentUpon>AuditSummary.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditItemChecklistComments_ChecklistComment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditItemChecklistComments_ChecklistComment.Designer.cs">
      <DependentUpon>AuditItemChecklistComments_ChecklistComment.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditPoamReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditPoamReport.Designer.cs">
      <DependentUpon>AuditPoamReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditSummaryExpanded.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditSummaryExpanded.Designer.cs">
      <DependentUpon>AuditSummaryExpanded.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalFindingSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalFindingSummary.Designer.cs">
      <DependentUpon>InternalFindingSummary.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalEMSNonConformanceForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalEMSNonConformanceForm.Designer.cs">
      <DependentUpon>InternalEMSNonConformanceForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalNoteworthyForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalNoteworthyForm.Designer.cs">
      <DependentUpon>InternalNoteworthyForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\InternalComplianceDeficiencyForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\InternalComplianceDeficiencyForm.Designer.cs">
      <DependentUpon>InternalComplianceDeficiencyForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief2.Designer.cs">
      <DependentUpon>ExternalAuditFindingOutbrief2.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditFindingOutbrief.Designer.cs">
      <DependentUpon>ExternalAuditFindingOutbrief.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditItemChecklistComments.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditItemChecklistComments.Designer.cs">
      <DependentUpon>AuditItemChecklistComments.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditItemFindings.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditItemFindings.Designer.cs">
      <DependentUpon>AuditItemFindings.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\AuditItemFindings_Finding.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\AuditItemFindings_Finding.Designer.cs">
      <DependentUpon>AuditItemFindings_Finding.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditNoteworthyForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditNoteworthyForm.Designer.cs">
      <DependentUpon>ExternalAuditNoteworthyForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditNonConformanceForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditNonConformanceForm.Designer.cs">
      <DependentUpon>ExternalAuditNonConformanceForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalComplianceDeficiencyForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalComplianceDeficiencyForm.Designer.cs">
      <DependentUpon>ExternalComplianceDeficiencyForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditNonComplianceForm.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QA\ExternalAuditNonComplianceForm.Designer.cs">
      <DependentUpon>ExternalAuditNonComplianceForm.cs</DependentUpon>
    </Compile>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\DataSources\System.Data.DataSet.datasource" />
    <None Include="Properties\DataSources\System.Data.DataTable.datasource" />
    <Compile Include="Reports\Contacts\Person\Notes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Person\Notes.Designer.cs">
      <DependentUpon>Notes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\EntityGroups.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\EntityGroups.Designer.cs">
      <DependentUpon>EntityGroups.cs</DependentUpon>
    </Compile>
    <Compile Include="SerializableDictionary.cs" />
    <Compile Include="Bll\Core\NoteBll.cs" />
    <Compile Include="Bll\Core\EmailBll.cs" />
    <Compile Include="Bll\Core\TaskBll.cs" />
    <Compile Include="Bll\Core\UserSettingBll.cs" />
    <Compile Include="Bll\Contacts\AddressBll.cs" />
    <Compile Include="Bll\Contacts\CompanyBll.cs" />
    <Compile Include="Bll\Contacts\EmailAddressBll.cs" />
    <Compile Include="Bll\Contacts\EntityBll.cs" />
    <Compile Include="Bll\Contacts\PhoneBll.cs" />
    <Compile Include="Bll\Contacts\GroupBll.cs" />
    <Compile Include="Bll\Contacts\OrganizationBll.cs" />
    <Compile Include="Bll\Contacts\PersonBll.cs" />
    <Compile Include="Bll\QA\ObjectiveBll.cs" />
    <Compile Include="Bll\QA\FindingBll.cs" />
    <Compile Include="Bll\QA\SignificanceBll.cs" />
    <Compile Include="Bll\Help\TooltipBll.cs" />
    <Compile Include="Bll\QA\AuditBll.cs" />
    <Compile Include="Bll\Reports\AdvancedSearchBll.cs" />
    <Compile Include="Bll\Core\DistributionBll.cs" />
    <Compile Include="Bll\Reports\GridReportAttribute.cs" />
    <Compile Include="Bll\Reports\ReportBll.cs" />
    <Compile Include="Bll\Reports\ReportTemplateBll.cs" />
    <Compile Include="Bll\Reports\ReportAttribute.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Exists.cs" />
    <Compile Include="Bll\Reports\SearchOperators\In.cs" />
    <Compile Include="Bll\Reports\SearchOperators\StartsWith.cs" />
    <Compile Include="Bll\Reports\SearchOperators\EndsWith.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Contains.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Le.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Lt.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Ge.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Gt.cs" />
    <Compile Include="Bll\Reports\SearchOperators\Eq.cs" />
    <Compile Include="Bll\Reports\SearchPropertyType.cs" />
    <Compile Include="Bll\Reports\SearchProperty.cs" />
    <Compile Include="Bll\Reports\SearchClass.cs" />
    <Compile Include="Bll\Reports\SearchCriterion.cs" />
    <Compile Include="Bll\Reports\SearchOperator.cs" />
    <Compile Include="Bll\Security\IFunction.cs" />
    <Compile Include="Bll\Security\IMenuItem.cs" />
    <Compile Include="Bll\Security\IProfileTab.cs" />
    <Compile Include="Bll\Security\PermissionStash.cs" />
    <Compile Include="ClassCodes.cs" />
    <Compile Include="Client\Configuration\M3SettingsSection.cs" />
    <Compile Include="Client\ContactHelper.cs" />
    <Compile Include="Client\Emails\QA\FindingReviewNotification.cs" />
    <Compile Include="Client\Emails\QA\FindingReadyNotification.cs" />
    <Compile Include="Client\Emails\QA\FindingResolutionNotification.cs" />
    <Compile Include="Client\Emails\QA\FindingAssignmentNotification.cs" />
    <Compile Include="Client\Emails\QA\FindingNotification.cs" />
    <Compile Include="Client\Emails\QA\AuditTemplateNotification.cs" />
    <Compile Include="Client\Emails\QA\AuditNotification.cs" />
    <Compile Include="Client\Emails\QA\ObjectiveNotification.cs" />
    <Compile Include="Client\Emails\QA\PoamNotification.cs" />
    <Compile Include="Client\Emails\QA\MilestoneNotification.cs" />
    <Compile Include="Client\Validation\Validators\Time.cs" />
    <Compile Include="Dao\Core\AlertDao.cs" />
    <Compile Include="Dao\Core\CustomAlertRuleDao.cs" />
    <Compile Include="Dao\Core\LabelDao.cs" />
    <Compile Include="Dao\Core\DocumentDao.cs" />
    <Compile Include="Dao\Core\CustomContentDao.cs" />
    <Compile Include="Dao\QA\AssetDao.cs" />
    <Compile Include="Dao\QA\AspectDao.cs" />
    <Compile Include="Dao\QA\PoamDao.cs" />
    <Compile Include="Dao\QA\MilestoneDao.cs" />
    <Compile Include="Dao\QA\FindingDao.cs" />
    <Compile Include="Dao\QA\PracticeDao.cs" />
    <Compile Include="Dao\QA\LocationDao.cs" />
    <Compile Include="Dao\QA\InspectionDao.cs" />
    <Compile Include="Dao\QA\AuditDao.cs" />
    <Compile Include="Dao\QA\MetricDao.cs" />
    <Compile Include="Domain\Core\Alert.cs" />
    <Compile Include="Domain\Core\CustomAlertRule.cs" />
    <Compile Include="Domain\Core\Label.cs" />
    <Compile Include="Domain\Core\Document.cs" />
    <Compile Include="Domain\Core\CustomContent.cs" />
    <Compile Include="Domain\QA\ChecklistTemplate.cs" />
    <Compile Include="Domain\QA\Asset.cs" />
    <Compile Include="Domain\QA\Aspect.cs" />
    <Compile Include="Domain\Core\IHasFiles.cs" />
    <Compile Include="Domain\QA\Milestone.cs" />
    <Compile Include="Domain\QA\Objective.cs" />
    <Compile Include="Domain\QA\Poam.cs" />
    <Compile Include="Domain\QA\Finding.cs" />
    <Compile Include="Domain\QA\Impact.cs" />
    <Compile Include="Domain\QA\Permit.cs" />
    <Compile Include="Domain\QA\AuditItem.cs" />
    <Compile Include="Domain\QA\Inspection.cs" />
    <Compile Include="Domain\QA\AuditTemplate.cs" />
    <Compile Include="Domain\QA\Practice.cs" />
    <Compile Include="Domain\QA\Location.cs" />
    <Compile Include="Domain\QA\Audit.cs" />
    <Compile Include="Domain\Base\CommonBusinessObject.cs" />
    <Compile Include="Domain\QA\IHasLocation.cs" />
    <Compile Include="Domain\Base\IHasXml.cs" />
    <Compile Include="Domain\Base\XmlSettings.cs" />
    <Compile Include="Dao\Help\TutorialDao.cs" />
    <Compile Include="Dao\Help\TopicDao.cs" />
    <Compile Include="Dao\Help\TooltipDao.cs" />
    <Compile Include="Domain\Core\Association.cs" />
    <Compile Include="Domain\Core\Association2Module.cs" />
    <Compile Include="Domain\Core\Menu2Template.cs" />
    <Compile Include="Domain\Core\Object2Quiz.cs" />
    <Compile Include="Domain\Core\ObjectType2Template.cs" />
    <Compile Include="Domain\Help\Tutorial.cs" />
    <Compile Include="Domain\Help\Topic.cs" />
    <Compile Include="Domain\Help\Tooltip.cs" />
    <Compile Include="Domain\Core\IHasAssociation.cs" />
    <Compile Include="Domain\Core\IHasDefaultRole.cs" />
    <Compile Include="HasCustomFieldsAttribute.cs" />
    <Compile Include="Client\Validation\Validators\AbstractValidator.cs" />
    <Compile Include="Client\Validation\Validators\Currency.cs" />
    <Compile Include="Client\Validation\Validators\Date.cs" />
    <Compile Include="Client\Validation\Validators\Digits.cs" />
    <Compile Include="Client\Validation\Validators\Email.cs" />
    <Compile Include="Client\Validation\Validators\Number.cs" />
    <Compile Include="Client\Validation\Validators\Phone.cs" />
    <Compile Include="Client\Validation\Validators\Required.cs" />
    <Compile Include="Client\Validation\Validators\RegexValidator.cs" />
    <Compile Include="Client\Validation\Validators\SimpleValidator.cs" />
    <Compile Include="Client\Validation\Validators\Url.cs" />
    <Compile Include="Client\ViewPageExtensions.cs" />
    <Compile Include="Dao\Base\DaoResult.cs" />
    <Compile Include="Dao\Core\DistributionDao.cs" />
    <Compile Include="Dao\Core\UserSettingDao.cs" />
    <Compile Include="Dao\Base\GenericDao.cs" />
    <Compile Include="Dao\Base\GenericCriteriaBuilder.cs" />
    <Compile Include="Dao\Core\CommunicationDao.cs" />
    <Compile Include="Dao\Contacts\AddressDao.cs" />
    <Compile Include="Dao\Contacts\CompanyDao.cs" />
    <Compile Include="Dao\Reporting\AdvancedSearchDao.cs" />
    <Compile Include="Dao\Core\CustomFieldDao.cs" />
    <Compile Include="Dao\Core\TaskDao.cs" />
    <Compile Include="Dao\Core\ReferenceDao.cs" />
    <Compile Include="Dao\Core\FileDao.cs" />
    <Compile Include="Dao\Contacts\EntityGroupDao.cs" />
    <Compile Include="Dao\Contacts\GroupDao.cs" />
    <Compile Include="Dao\Contacts\PersonCompanyDao.cs" />
    <Compile Include="Dao\Core\NoteDao.cs" />
    <Compile Include="Dao\Contacts\EmailAddressDao.cs" />
    <Compile Include="Dao\Contacts\PhoneDao.cs" />
    <Compile Include="Dao\Contacts\EntityDao.cs" />
    <Compile Include="Dao\Core\PermissionDao.cs" />
    <Compile Include="Domain\Base\BusinessObject.cs" />
    <Compile Include="Domain\Core\Communication.cs" />
    <Compile Include="Domain\Core\AdvancedSearch.cs" />
    <Compile Include="Domain\Core\Object2File.cs" />
    <Compile Include="Domain\Core\Distribution.cs" />
    <Compile Include="Domain\Core\UserSetting.cs" />
    <Compile Include="Domain\Core\ReportTemplate.cs" />
    <Compile Include="Domain\Core\Task.cs" />
    <Compile Include="Domain\Core\Reference.cs" />
    <Compile Include="Domain\Core\ReferenceType.cs" />
    <Compile Include="Domain\Core\File.cs" />
    <Compile Include="Domain\Contacts\EntityGroup.cs" />
    <Compile Include="Domain\Contacts\Group.cs" />
    <Compile Include="Domain\Contacts\PersonCompany.cs" />
    <Compile Include="Domain\Core\Communication2Object.cs" />
    <Compile Include="Domain\Core\Note.cs" />
    <Compile Include="Dao\Contacts\PersonDao.cs" />
    <Compile Include="Bll\Core\GridSettingBll.cs" />
    <Compile Include="Bll\Security\SecurityBll.cs" />
    <Compile Include="Bll\Security\UserBll.cs" />
    <Compile Include="Client\M3Context.cs" />
    <Compile Include="Client\Messages.cs" />
    <Compile Include="Dao\Core\UserGroupDao.cs" />
    <Compile Include="Dao\Contacts\OrganizationDao.cs" />
    <Compile Include="Domain\Contacts\Company.cs" />
    <Compile Include="Domain\Contacts\Address.cs" />
    <Compile Include="Domain\Contacts\EmailAddress.cs" />
    <Compile Include="Domain\Contacts\Phone.cs" />
    <Compile Include="Domain\Core\Permission.cs" />
    <Compile Include="Domain\Core\LoginAttempt.cs" />
    <Compile Include="Domain\Contacts\Organization.cs" />
    <Compile Include="Domain\Contacts\Entity.cs" />
    <Compile Include="Domain\Base\DeletableBusinessObject.cs" />
    <Compile Include="Bll\Security\PermissionManager.cs" />
    <Compile Include="Domain\Contacts\Person.cs" />
    <Compile Include="Domain\Core\User.cs" />
    <Compile Include="Domain\Core\UserGroup.cs" />
    <Compile Include="Dao\Core\UserDao.cs" />
    <Compile Include="Domain\Contacts\IHasOrganization.cs" />
    <Compile Include="Domain\Base\ModifiableBusinessObject.cs" />
    <Compile Include="Domain\Base\PersistentObject.cs" />
    <Compile Include="Emails\CookieAwareWebClient.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Emails\IEmailTemplate.cs" />
    <Compile Include="Emails\PageEmailTemplate.cs" />
    <Compile Include="Extensions.cs" />
    <Compile Include="Messages\Message.cs" />
    <Compile Include="Messages\MessageManager.cs" />
    <Compile Include="NameIdPair.cs" />
    <Compile Include="PeriodicService\AbstractPeriodicTask.cs" />
    <Compile Include="PeriodicService\AlertRunner.cs" />
    <Compile Include="ProjectBase\Cache\AppCache.cs" />
    <Compile Include="ProjectBase\Cache\CacheService.cs" />
    <Compile Include="ProjectBase\Cache\HashtableCache.cs" />
    <Compile Include="ProjectBase\Cache\HttpCacheAdapter.cs" />
    <Compile Include="ProjectBase\Cache\ICache.cs" />
    <Compile Include="Client\ConcreteTypeAttribute.cs" />
    <Compile Include="Dao\Base\AbstractDao.cs" />
    <Compile Include="Reports\Admin\Security\User.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Admin\Security\User.Designer.cs">
      <DependentUpon>User.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\Note.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\Note.Designer.cs">
      <DependentUpon>Note.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Extensions.cs" />
    <Compile Include="Reports\IFilterableReport.cs" />
    <Compile Include="Security\FunctionKeys.cs" />
    <Compile Include="Security\NotAuthorizedException.cs" />
    <Compile Include="Security\GrantAllPermissionManager.cs" />
    <Compile Include="Security\IPermissionManager.cs" />
    <Compile Include="Client\ActionLink.cs" />
    <Compile Include="Client\M3MultiSelectList.cs" />
    <Compile Include="Client\M3SelectList.cs" />
    <Compile Include="Client\CacheFilter.cs" />
    <Compile Include="Client\ControllerFactory.cs" />
    <Compile Include="Domain\Core\ChangeHistory.cs" />
    <Compile Include="Domain\Core\ChangeHistoryDetail.cs" />
    <Compile Include="Domain\Base\IAuditable.cs" />
    <Compile Include="Dal\AuditInterceptor.cs" />
    <Compile Include="Dal\Wrappers\M3Session.cs" />
    <Compile Include="Client\DownloadResult.cs" />
    <Compile Include="Client\JsonNoCacheResult.cs" />
    <Compile Include="Client\NamespacedViewEngine.cs" />
    <Compile Include="Client\NoCompressFilter.cs" />
    <Compile Include="Client\NullModelBinder.cs" />
    <Compile Include="Client\RequiresPermissionAttribute.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Reports\Admin\Security\User2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Admin\Security\User2.Designer.cs">
      <DependentUpon>User2.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Admin\Shared\UserGroups.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Admin\Shared\UserGroups.Designer.cs">
      <DependentUpon>UserGroups.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Directory.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Directory.Designer.cs">
      <DependentUpon>Directory.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\Keywords.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\Keywords.Designer.cs">
      <DependentUpon>Keywords.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\CompanyTypes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\CompanyTypes.Designer.cs">
      <DependentUpon>CompanyTypes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\ContactTypes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\ContactTypes.Designer.cs">
      <DependentUpon>ContactTypes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\Employees.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\Employees.Designer.cs">
      <DependentUpon>Employees.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\IndustryCodes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\IndustryCodes.Designer.cs">
      <DependentUpon>IndustryCodes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\GroupProfile.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Contacts\Shared\GroupProfile.Designer.cs">
      <DependentUpon>GroupProfile.cs</DependentUpon>
    </Compile>
    <Compile Include="Client\Validation\ValidationSet.cs" />
    <Compile Include="Client\Validation\ValidationSetAttribute.cs" />
    <Compile Include="LazyDictionary.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{3259AA49-8AA1-44D3-9025-A0B520596A8C}" />
    <Service Include="{82A7F48D-3B50-4B1E-B82E-3ADA8210C358}" />
    <Service Include="{C8F2D6AC-F9F4-4E40-A399-22F9A9A5CBD2}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Reports\Contacts\Avery5160.resx">
      <DependentUpon>Avery5160.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\CompanyAvery5160.resx">
      <DependentUpon>CompanyAvery5160.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\DefaultEmployerAvery5160.resx">
      <DependentUpon>DefaultEmployerAvery5160.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\Person\Notes.resx">
      <DependentUpon>Notes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\Shared\EntityGroups.resx">
      <DependentUpon>EntityGroups.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Core\User.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <None Include="Dal\Mapping\Core\DbVersion.hbm.xml" />
    <EmbeddedResource Include="Charts\QA\ExternalDeficiencies.resx">
      <DependentUpon>ExternalDeficiencies.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditItemChecklistComments_InspectionComment.resx">
      <DependentUpon>AuditItemChecklistComments_InspectionComment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditSummary.resx">
      <DependentUpon>AuditSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditItemChecklistComments_ChecklistComment.resx">
      <DependentUpon>AuditItemChecklistComments_ChecklistComment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditPoamReport.resx">
      <DependentUpon>AuditPoamReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditSummaryExpanded.resx">
      <DependentUpon>AuditSummaryExpanded.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalAuditReport_FindingsCountISOCategory.resx">
      <DependentUpon>InternalAuditReport_FindingsCountISOCategory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalAuditReport_FindingsCountISO.resx">
      <DependentUpon>InternalAuditReport_FindingsCountISO.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalAuditReport_FindingsCountMedia.resx">
      <DependentUpon>InternalAuditReport_FindingsCountMedia.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalAuditReport_MediaFindings.resx">
      <DependentUpon>InternalAuditReport_MediaFindings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalAuditReport.resx">
      <DependentUpon>InternalAuditReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditFindingOutbrief2_Findings.resx">
      <DependentUpon>ExternalAuditFindingOutbrief2_Findings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditFindingOutbrief_Audit.resx">
      <DependentUpon>ExternalAuditFindingOutbrief_Audit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditFindingSummary.resx">
      <DependentUpon>ExternalAuditFindingSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditComplianceChecklist.resx">
      <DependentUpon>ExternalAuditComplianceChecklist.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditReport.resx">
      <DependentUpon>ExternalAuditReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalFindingSummary.resx">
      <DependentUpon>InternalFindingSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalEMSNonConformanceForm.resx">
      <DependentUpon>InternalEMSNonConformanceForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalNoteworthyForm.resx">
      <DependentUpon>InternalNoteworthyForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\InternalComplianceDeficiencyForm.resx">
      <DependentUpon>InternalComplianceDeficiencyForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditFindingOutbrief2.resx">
      <DependentUpon>ExternalAuditFindingOutbrief2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditFindingOutbrief.resx">
      <DependentUpon>ExternalAuditFindingOutbrief.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditItemChecklistComments.resx">
      <DependentUpon>AuditItemChecklistComments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditItemFindings.resx">
      <DependentUpon>AuditItemFindings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\AuditItemFindings_Finding.resx">
      <DependentUpon>AuditItemFindings_Finding.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditNoteworthyForm.resx">
      <DependentUpon>ExternalAuditNoteworthyForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditNonConformanceForm.resx">
      <DependentUpon>ExternalAuditNonConformanceForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalComplianceDeficiencyForm.resx">
      <DependentUpon>ExternalComplianceDeficiencyForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QA\ExternalAuditNonComplianceForm.resx">
      <DependentUpon>ExternalAuditNonComplianceForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\Entity.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\LoginAttempt.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Dal\Mapping\Core\GridSetting.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Permission.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\UserGroup.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\Phone.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\Address.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\EmailAddress.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\PersonCompany.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\Group.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\EntityGroup.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\File.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\AdvancedSearch.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\ChangeHistory.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\ChangeHistoryDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\UserSetting.hbm.xml" />
    <EmbeddedResource Include="Reports\Contacts\Shared\GroupProfile.resx">
      <DependentUpon>GroupProfile.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Reports\Admin\Security\User2.resx">
      <DependentUpon>User2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Admin\Shared\UserGroups.resx">
      <DependentUpon>UserGroups.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Reports\Contacts\Directory.resx">
      <DependentUpon>Directory.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Distribution.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\Shared\IndustryCodes.resx">
      <DependentUpon>IndustryCodes.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Reports\Contacts\Shared\Keywords.resx">
      <DependentUpon>Keywords.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\Shared\CompanyTypes.resx">
      <DependentUpon>CompanyTypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\Shared\ContactTypes.resx">
      <DependentUpon>ContactTypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Contacts\Shared\Employees.resx">
      <DependentUpon>Employees.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Object2File.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Association.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Association2Module.hbm.xml" />
    <EmbeddedResource Include="Reports\Admin\Security\User.resx">
      <DependentUpon>User.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Help\Tooltip.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Help\Topic.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Help\Tutorial.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\ObjectType2Template.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Object2Quiz.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Reports\Contacts\Shared\Note.resx">
      <DependentUpon>Note.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\Person.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Contacts\Company.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\ChecklistTemplate.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Audit.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Location.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Asset.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Practice.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\AuditTemplate.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Inspection.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\AuditItem.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Aspect.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Permit.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Milestone.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Impact.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Objective.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\CustomContent.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Document.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Label.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Poam.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Filters.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Reports\Contacts\Company\" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Error.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\MediaProfile.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\User2Media.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\QA\WaterSystemOperator.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Quiz\Element.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Quiz\Option.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Quiz\Template.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Quiz\Quiz.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Core\Communication2User.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\Bookmark.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\LocalPracticeType.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Help\Topic2Page.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Contacts\EntitySearchResult.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Help\TutorialTopic.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Core\MergeTemplate.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\Compliance\MonitoringProfile.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Quiz\Answer.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\DataCall.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\DataCallAssignment.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\DataCallGroup.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Quiz\View.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Quiz\ViewElement.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\AuditGroup.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\TESpecies.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\QA\BuiltEnvironment.hbm.xml" />
    <EmbeddedResource Include="Dal\Mapping\QA\SpecialArea.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\UserLogin.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\ReportTemplate2Control.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Dal\Mapping\Core\ReportTemplateMapping.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\Object2Document.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\RenderedReport.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\Chemical.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\CLINSuffix.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\StorageLocation.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\WasteRecordRate.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\WasteRecordCLIN.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\RateCode.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\LSN.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\ShippingHazard.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\ReviewDate.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\WasteStream.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\Facility.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\ManifestContact.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\ContainerPreset.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\Customer2Person.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\ImportFile.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\ImportDetailPreview.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\RequestedDocument.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\DocumentResponse.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\DocumentLevel.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\TrendChart.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\TrendChartSeries.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\Object2AuditItem.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\Core\LocalizedTerm.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\SimpleAuditPoam.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\ChecklistImportDetail.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\ChecklistImport.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\HW\VPIC.hbm.xml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Dal\Mapping\QA\ChecklistTemplateVersion.hbm.xml">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bll\HW\DAAS\Models\readme.txt" />
    <Content Include="Migrations\Sql\Release-2025-06.sql" />
    <Content Include="Migrations\Sql\Release-2025-05.sql" />
    <Content Include="Migrations\Sql\Release-2025-03.sql" />
    <Content Include="Migrations\Sql\Release-2025-02.sql" />
    <Content Include="Migrations\Sql\Release-2024-12.sql" />
    <Content Include="Migrations\Sql\Release-2024-11.sql" />
    <Content Include="Migrations\Sql\Release-2024-08.sql" />
    <Content Include="Migrations\Sql\Release-2024-06.sql" />
    <Content Include="Migrations\Sql\Release-2024-04.sql" />
    <Content Include="Migrations\Sql\Release-2023-12.sql" />
    <Content Include="Migrations\Sql\Release-2023-10.sql" />
    <Content Include="Migrations\Sql\Release-2023-09.sql" />
    <Content Include="Migrations\Sql\Release-2023-07-1.sql" />
    <Content Include="Migrations\Sql\Release-2023-07.sql" />
    <Content Include="Migrations\Sql\Release-2023-07-hangfire.sql" />
    <Content Include="Migrations\Sql\Release-2023-06.sql" />
    <Content Include="Migrations\Sql\Release-2023-04.sql" />
    <Content Include="Migrations\Sql\Release-2023-03.sql" />
    <Content Include="Migrations\Sql\Release-2023-02.sql" />
    <Content Include="Migrations\Sql\Release-2022-11.sql" />
    <Content Include="Migrations\Sql\Release-2022-09.sql" />
    <Content Include="Migrations\Sql\Release-2022-07.sql" />
    <Content Include="Migrations\Sql\Release-2022-06.sql" />
    <Content Include="Migrations\Sql\Release-2021-12.sql" />
    <Content Include="Migrations\Sql\Release-2021-11.sql" />
    <Content Include="Migrations\Sql\Release-2021-10.sql" />
    <Content Include="Migrations\Sql\Release-2021-08.sql" />
    <Content Include="Migrations\Sql\Release-2021-06.sql" />
    <Content Include="Migrations\Sql\Release-2021-05.sql" />
    <Content Include="Migrations\Sql\Release-2021-04.sql" />
    <Content Include="Migrations\Sql\Release-2021-03.sql" />
    <Content Include="Migrations\Sql\Release-2021-01.sql" />
    <Content Include="Migrations\Sql\Release-2020-12.sql" />
    <Content Include="Migrations\Sql\Release-2020-08.sql" />
    <Content Include="Migrations\Sql\Release-2020-07-1.sql" />
    <Content Include="Migrations\Sql\Release-2020-07.sql" />
    <Content Include="Migrations\Sql\Release-2020-06.sql" />
    <Content Include="Migrations\Sql\Release-2020-05.sql" />
    <Content Include="Migrations\Sql\Release-2020-04.sql" />
    <Content Include="Migrations\Sql\Release-2020-03.sql" />
    <Content Include="Migrations\Sql\readme.txt" />
    <Content Include="Migrations\Sql\Release-2020-02.sql" />
    <Content Include="Migrations\Sql\Release-2020-01.sql" />
    <Content Include="Migrations\Sql\Release-2019-01.sql" />
    <Content Include="Migrations\Sql\Release-2023-08.sql" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\Build\CommonProject.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
    <Copy SourceFiles="App.config" DestinationFiles="$(OutputPath)$(AssemblyName).dll.config" />
  </Target>
  <Import Project="..\packages\FluentMigrator.Runner.SqlServerCe.3.2.1\build\netstandard2.0\FluentMigrator.Runner.SqlServerCe.targets" Condition="Exists('..\packages\FluentMigrator.Runner.SqlServerCe.3.2.1\build\netstandard2.0\FluentMigrator.Runner.SqlServerCe.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\FluentMigrator.Runner.SqlServerCe.3.2.1\build\netstandard2.0\FluentMigrator.Runner.SqlServerCe.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\FluentMigrator.Runner.SqlServerCe.3.2.1\build\netstandard2.0\FluentMigrator.Runner.SqlServerCe.targets'))" />
  </Target>
</Project>