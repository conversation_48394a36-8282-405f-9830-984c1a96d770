using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Drg.M3
{
    public class LazyDictionary<TKey, TValue> 
    {
        Dictionary<TKey, TValue> internalDict = new Dictionary<TKey, TValue>();
        Func<TKey,TValue> loader;
        public LazyDictionary(Func<TKey,TValue> loader)
        {
            this.loader = loader;
        }
        public TValue this[TKey key]
        {
            get
            {
                if (internalDict.ContainsKey(key))
                    return internalDict[key];
                var val = loader.Invoke(key);
                if (!object.Equals(val, default(TValue)))
                    internalDict[key] = val;
                return val;
            }
        }
    }
}
