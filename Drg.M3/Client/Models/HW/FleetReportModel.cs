using System;

namespace Drg.M3.Client.Models.HW
{
    public class FleetReportModel
    {
        public string WasteNumber { get; set; }
        public int WasteRecordId { get; set; }
        public string OrganizationUIC { get; set; }
        public string FEC { get; set; }
        public DateTime? Billed { get; set; }
        public DateTime PickupDate { get; set; }
        public string Ship { get; set; }
        public string Nomenclature { get; set; }
        public string WasteName { get; set; }
        public bool Unopened { get; set; }
        public string RateType { get; set; }
        public decimal WeightLbs { get; set; }
        public decimal? Rate { get; set; }
        public decimal? BillAmount { get; set; }
        public string RateQuantity { get; set; }
        public string RateRate { get; set; }
        public string RateTotal { get; set; }
        public string RateDesc { get; set; }
    }
}