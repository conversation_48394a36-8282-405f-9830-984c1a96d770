using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Collections.Specialized;

namespace Drg.M3.Client.Validation.Validators
{
    public abstract class SimpleValidator : AbstractValidator
    {
        public string ClassName { get; set; }
        public SimpleValidator(string element, string className)
            : base(element)
        {
            this.ClassName = className;
        }
    }
}
