using System;
using System.Collections.Specialized;

namespace Drg.M3.Client.Validation.Validators
{
	public class Time : SimpleValidator
	{
        public Time(string element) : base(element, "time") { }

        public override string Validate(NameValueCollection values)
        {
            var value = values.Get(Element);
            if (string.IsNullOrEmpty(value)) return null;
            DateTime dt;
            if (DateTime.TryParse(value, out dt)) return null;
            else return "{" + Element + "}: Please enter a valid time.";
        }
	}
}
