using System;
using System.Text;
using Drg.M3.Emails;
using Drg.M3.Domain.QA;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Client.Emails.QA
{
    [Modules.ModuleAttribute(typeof(Modules.QA))]
    public class AuditNotification : AbstractEmailTemplate<Audit>
    {
        public AuditNotification(IM3Session session) : base(session)
        {
            UnformattedBody = "{audit}: \r\n\r\n"
                + "You have been assigned to this Audit. Please go to {url} to view the audit.\r\n\r\n"
                + "Internal/External:\t\t{internalExternal}\r\n"
                + "Date summary:\t\t\t{date}\r\n"
                + "Organization:\t\t\t{organization}\r\n"
                + "Author:\t\t\t\t{author}\r\n"
                + "Created:\t\t\t\t{createdDate}\r\n\r\n"
                + "Templates:\tMedia\tAuditor\tPOC\tEvaluation Type{templates}";
            UnformattedSubject = "Audit Submitted";
        }

        #region IEmailTemplate<Audit> Members

        public override string Subject(Audit model)
        {
            return UnformattedSubject;
        }

        public override string Body(Audit model)
        {
            StringBuilder templates = new StringBuilder();

            foreach (var template in model.AuditTemplates)
                templates.Append(Environment.NewLine + " - " + template.Template.Name + ":\t" + template.Media + "\t" + template.Auditor.ToString() + "\t" + template.Poc + "\t" + template.EvaluationType);

            string date = model.StartDate.ToShortDateString();
            if (model.EndDate != null)
                date += " - " + model.EndDate.ToShortDateString();
            if (model.IsRecurring && model.Settings.RecurrenceSetting != null)
                date += " (" + model.Settings.RecurrenceSetting.Name + ")";

            return UnformattedBody
                .Replace("{audit}", model.Name)
                .Replace("{url}", M3Context.Current.ResolveAbsoluteUrl("~/QA/Audit/Profile/" + model.Id))
                .Replace("{internalExternal}", model.InternalExternal)
                .Replace("{date}", date)
                .Replace("{organization}", model.Organization.ToString())
                .Replace("{author}", model.CreatedBy.Person.FullName)
                .Replace("{createdDate}", model.DC.ToShortDateString())
                .Replace("{templates}", templates.ToString());
        }

        #endregion
    }
}
