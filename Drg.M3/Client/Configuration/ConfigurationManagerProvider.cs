using System.Configuration;

namespace Drg.M3.Client.Configuration
{
    public class ConfigurationManagerProvider
    {
        public static ConfigurationManagerProvider Instance
        {
            get;
            set;
        } = new ConfigurationManagerProvider();

        public virtual T GetSection<T>(string sectionName) where T : ConfigurationSection
        {
            return System.Configuration.ConfigurationManager.GetSection(sectionName) as T;
        }
    }
}
