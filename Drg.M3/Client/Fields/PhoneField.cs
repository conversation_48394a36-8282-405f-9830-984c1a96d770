using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace Drg.M3.Client.Fields
{
    public class PhoneField : AbstractField
    {
        public PhoneField(string name, string caption, bool required) : base(name, caption, required) { }

        public override IHtmlString Render(HtmlHelper context, bool softRequired)
        {
            if (Required || softRequired)
                return context.PhoneField(Name, Caption, null, HtmlAttributes, true);
            return context.PhoneField(Name, Caption);
        }
    }
}
