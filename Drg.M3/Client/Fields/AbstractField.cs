using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Mvc;
using System.Web.Routing;
using Drg.M3.Domain;
using System.Web;

namespace Drg.M3.Client.Fields
{
    public abstract class AbstractField : IHtmlField
    {
        public AbstractField(string name, string caption, bool required)
        {
            this.Name = name;
            this.Caption = caption;
            this.Required = required;
            this.HtmlAttributes = null;
        }

        public string Name { get; set; }
        public string Caption { get; set; }
        public bool Required { get; set; }
        public object HtmlAttributes { get; set; }

        public abstract IHtmlString Render(HtmlHelper context, bool softRequired);
        public virtual IHtmlString Render(HtmlHelper context, bool softRequired, Organization organization)
        {
            // Default is to ignore org
            return Render(context, softRequired);
        }
    }
}
