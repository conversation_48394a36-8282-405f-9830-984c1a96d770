using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Hosting;
using System.Web.Mvc;

namespace Drg.M3.Client
{
    public static class UrlHelperExtensions
    {
        static ConcurrentDictionary<string, string> _cacheBusts = new ConcurrentDictionary<string, string>();
        public static string CacheBust(string url, string virtualPath = null)
        {
            //Can't cachebust if it has a querystring
            if ((virtualPath ?? url).Contains("?"))
                return url;

            var v = _cacheBusts.GetOrAdd(virtualPath ?? url, path =>
            {
                var actualPath = HostingEnvironment.MapPath(path);

                if (!File.Exists(actualPath))
                    return null;

                using (FileStream fs = File.OpenRead(actualPath))
                {
                    byte[] hash = new SHA256CryptoServiceProvider().ComputeHash(fs);
                    return HttpServerUtility.UrlTokenEncode(hash);
                }
            });

            return url + (url.Contains("?") ? "&" : "?") + "v=" + v;
        }
        public static string CacheBust(this UrlHelper _this, string url, string virtualPath = null)
        {
            return CacheBust(url, virtualPath);
        }
    }
}
