using System;
using System.Linq;
using Drg.M3.Domain;
using Drg.M3.Dao;
using Drg.M3.Client;
using Drg.M3.Dal;
using Drg.M3.Dao.QA;
using Dapper;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Bll
{
    public class OrganizationBll : AbstractBll
    {
        public OrganizationBll(IM3Session session) : base(session) { }

        public Company CreateOrgCompany(Organization org)
        {
            var c = new Company();

            org.Company = c;
            c.IsOrganization = true;
            c.Name = org.Name;
            c.Organization = org;

            new CompanyDao(Session).Save(c);
            return c;
        }

        public void SetParent(Organization org, Organization parent)
        {
            if (!parent.IsOutside(org)) throw new InvalidOperationException("A circular reference was attempted");

            if (org.IsTransient())
            {
                org.Currency = parent.Currency;
                org.Settings.CultureName = parent.Settings.CultureName;
                org.Settings.UICultureName = parent.Settings.UICultureName;
            }

            org.ParentOrganization = parent;
            org.Key = parent.Key + ".?";

            new OrganizationDao(Session).Save(org);

            SetKeyRecursive(org);
        }
        public void SetKeyRecursive(Organization org)
        {
            org.Key = org.ParentOrganization.Key + "." + org.Id;
            new OrganizationDao(Session).Save(org);

            Session.Connection.Execute("update COR_Entity set w_OrganizationKey=@key where OrganizationId=@id",
                new { org.Key, org.Id }, Session.GetDbTransaction());

            foreach (var child in org.Organizations)
                SetKeyRecursive(child);
        }

        public void DeleteRecursive(Organization org)
        {
            foreach (var user in new UserDao(Session).GetCurrentlyViewingAt(org))
            {
                user.CurrentOrganization = null;
                Session.Save(user);
            }

            if (org.DD == null)
            {
                org.DD = NiftyDate.Now;
                org.DeletedBy = M3Context.Current.User;
            }
            org.Key = string.Empty;
            Session.Save(org);

            foreach (var user in new UserDao(Session).GetAll(org, false))
            {
                user.DD = NiftyDate.Now;
                user.DeletedBy = M3Context.Current.User;
                Session.Save(user);
            }

            using (DeletedFilter.Disable(Session))
            {
                var children = new OrganizationDao(Session).GetChildren(org);
                foreach (var child in children)
                    DeleteRecursive(child);
            }
        }

        /// <summary>
        /// Determines whether an organization-level has Regulatory Inspections in a given FY.
        /// </summary>
        public bool HasRegulatoryInspections(Organization org, int fiscalYear, bool includeDraft, bool recursive = true)
        {
            if (org == null)
            {
                return false;
            }

            var auditType = new ReferenceDao(Session).FindActive(ReferenceType.AuditInspectionType, org, "Regulatory Performed Inspection");
            var orgs = new AuditDao(Session).GetAll(org: org,
                                                    recursive: recursive, 
                                                    search: string.Empty, 
                                                    isInactive: null, 
                                                    isCurrent: null, 
                                                    auditType: auditType, 
                                                    fy: fiscalYear, 
                                                    isDraft: (includeDraft ? (bool?)null : false))
                .Execute(Session).Select(s => s.Organization).ToList();
            return orgs.Contains(org) || (recursive && orgs.Any(a => a.Key.StartsWith(org.Key)));
        }

        public void UpdateRegions()
        {
            Session.Connection.Execute(@"
update o set RegionId=
	(select top 1 p.Id from (select value from string_split(o.KeyChain, '.')) ids join CON_Organization p on ids.value=p.Id join COR_Reference r on p.OrganizationTypeID=r.Id where r.Name='Region')
from
	CON_Organization o
", transaction: Session.GetDbTransaction());
        }

        public Organization FindRegion(Organization org)
        {
            do
            {
                if (org.OrganizationType?.Name == "Region")
                    return org;

                org = org.ParentOrganization;
            }
            while (org != null);

            return null;
        }
    }
}