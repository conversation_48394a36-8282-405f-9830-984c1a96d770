using Drg.M3.Domain.Compliance;
using Drg.M3.Domain;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Bll.Compliance
{
    public class MonitoringBll : AbstractBll
    {
        public MonitoringBll(IM3Session session) : base(session) { }

        public MonitoredSubstance Copy(MonitoredSubstance src, Organization org)
        {
            return Copy(src, org, "Copy of " + src.Name);
        }
        public MonitoredSubstance Copy(MonitoredSubstance src, Organization org, string name)
        {
            var copy = new MonitoredSubstance(name, org);
            copy.Description = src.Description;
            foreach (var cmpSrc in src.Components)
            {
                var cmpCopy = new MonitoredComponent(cmpSrc.Name, cmpSrc.Unit, copy);
                copy.Components.Add(cmpCopy);
                Session.Save(cmpCopy);
            }
            Session.Save(copy);
            return copy;
        }
    }
}
