using System;
using System.Collections.Generic;
using System.Linq;

using Drg.M3.Domain;
using Drg.M3.Dao;
using System.Security;
using Drg.M3.Client;
using Drg.M3.Dal.Wrappers;


namespace Drg.M3.Bll
{
    public class NoteBll : AbstractBll
    {
        public NoteBll(IM3Session session) : base(session) { }

        public void ValidateOperation(Note note, AccessType accessType)
        {
            /*A user has permission if:
             * 1. He has permission to do this operation on the record type level AND
             * (
             *    2. He is logged on as the creator of the note OR
             *    3. He is logged on as the author of the note OR
             *    4. He has permission in general to do this operation on someone else's notes
             *  )
             */

            if (M3Context.Current.Permissions.HasPermission(typeof(Note), accessType) && (
                    note.CreatedBy == M3Context.Current.User
                    || (note.Author != null && note.Author.Id == M3Context.Current.User.Id)
                    || M3Context.Current.Permissions.HasPermission("OtherPeoplesNotes", accessType)
                ))
            {
                //this operation is allowed
            }
            else
            {
                throw new SecurityException("You do not have permission to " + accessType.ToString().ToLower() + " this note");
            }
        }

        public Note CreateNote(string subject, string message, int? authorUserId, int? noteTypeId, PersistentObject obj)
        {
            return CreateNote(subject, message, authorUserId, noteTypeId, new[] { obj });
        }

        public Note CreateNote(string subject, string message, int? authorUserId, int? noteTypeId, IEnumerable<PersistentObject> objects)
        {
            if (!M3Context.Current.Permissions.HasPermission(typeof(Note), AccessType.Add))
                throw new SecurityException("You do not have permission to add notes");

            var author = Session.Get<User>(authorUserId);

            var org = objects.OfType<IHasOrganization>().Where(o => o.Organization != null)
                .Select(o => o.Organization).FirstOrDefault();

            if (org == null && author != null) org = author.Organization;

            var note = new Note(org) { Subject = subject, Message = message };

            if (authorUserId.HasValue) note.Author = author;
            if (noteTypeId.HasValue) note.NoteType = new ReferenceDao(Session).Get(noteTypeId.Value);

            new NoteDao(Session).Save(note);

            foreach (var obj in objects)
            {
                if (obj.Id == 0) throw new ArgumentException("Linked objects must be saved prior to creating a note");
                note.Communication2Objects.Add(new Communication2Object() { Communication = note, RelatedObject = obj });

                //http://gemini.datarg.net/workspace/67/item/61394
                if (obj is Domain.QA.Finding f)
                    f.w_LastActionDate = NiftyDate.Now;
                else if (obj is Domain.QA.Poam p)
                    p.DM = NiftyDate.Now;
            }
            return note;
        }

        public void UpdateNote(Note note, string subject, string body, int authorUserId, int? noteTypeId)
        {
            ValidateOperation(note, AccessType.Edit);

            note.Subject = subject;
            note.Message = body;
            note.Author = new UserDao(Session).Get(authorUserId);
            note.NoteType = new ReferenceDao(Session).Get(noteTypeId.GetValueOrDefault());
            new NoteDao(Session).Save(note);
        }

        public void DeleteNote(Note note)
        {
            ValidateOperation(note, AccessType.Delete);

            note.DD = NiftyDate.Now;
            note.DeletedBy = M3Context.Current.User;
            new NoteDao(Session).Save(note);
        }
    }
}