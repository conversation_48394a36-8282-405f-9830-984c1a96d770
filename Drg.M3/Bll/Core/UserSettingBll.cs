using System.Collections.Generic;
using Drg.M3.Domain;
using Drg.M3.Dao;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Bll.Common
{
    public class UserSettingBll : AbstractBll
    {
        public UserSettingBll(IM3Session session) : base(session) { }


        public UserSetting Set(SettingType type, User user, string className, string value)
        {
            return Set(type, user, className, null, value);
        }
        public UserSetting Set(SettingType type, User user, string className, string objectName, string value)
        {
            var setting = new UserSettingDao(Session).Get(type, user, className, objectName);
            if (setting == null)
                setting = new UserSetting() { SettingType = type, User = user, ClassName = className, ObjectName = objectName };
            setting.Value = value;
            new UserSettingDao(Session).Save(setting);
            return setting;
        }
        public List<DashboardTable> GenerateDefaults()
        {
            List<DashboardTable> result = new List<DashboardTable>();
            result.Add(new DashboardTable("personalInfo", "Personal Info", true, 1));
            result.Add(new DashboardTable("messages", "My Messages", true, 2));
            result.Add(new DashboardTable("tasks", "My Tasks", true, 3));
            result.Add(new DashboardTable("audits", "My Audits/Inspections", true, 4));
            result.Add(new DashboardTable("checklists", "My Checklists", true, 5));
            result.Add(new DashboardTable("findings", "My Findings", true, 6));
            result.Add(new DashboardTable("poams", "My POAMs", true, 7));
            result.Add(new DashboardTable("milestones", "My Milestones", true, 8));
            result.Add(new DashboardTable("dataCalls", "My Data Calls", true, 9));

            return result;
        }
        public Dictionary<string, object> DefaultDashboardFilters(User user)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();

            // tasks;
            result.Add("tasks_daysuntildue", 30);
            result.Add("tasks_daysoverdue", null);
            result.Add("tasks_completed", false);
            //result.Add("tasks_assignedusers", -1);

            // audits/inspections
            result.Add("audits_drafts", true);
            result.Add("audits_closed", false);

            // checklists
            result.Add("checklists_daysuntildue", 30);
            result.Add("checklists_daysoverdue", null);
            result.Add("checklists_completed", false);

            // findings
            result.Add("findings_status", -1);
            result.Add("findings_drafts", true);
            result.Add("findings_closed", true);
            result.Add("findings_inactive", false);

            // poams
            result.Add("poams_progressmin", 0);
            result.Add("poams_progressmax", 100);
            result.Add("poams_daysuntildue", null);

            // milestones
            result.Add("milestones_progressmin", 0);
            result.Add("milestones_progressmax", 100);
            result.Add("milestones_daysuntildue", null);

            // data calls
            result.Add("datacalls_submitted", true);
            result.Add("datacalls_closed", false);

            return result;
        }
    }
}
