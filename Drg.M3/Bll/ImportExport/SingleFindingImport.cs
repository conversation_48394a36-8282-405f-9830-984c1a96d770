using Drg.M3.Bll.HW;
using Drg.M3.Bll.ImportExport;
using Drg.M3.Bll.QA;
using Drg.M3.Dal;
using Drg.M3.Dao;
using Drg.M3.Dao.QA;
using Drg.M3.Domain;
using Drg.M3.Domain.QA;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace Drg.M3.Bll.ImportExport
{
    public class SingleFindingImport : M3AbstractImport<Finding>
    {
        #region Import
        public override bool ByCellReference => true;
        public override bool CanUpdate => false;

        Organization org;
        Audit Audit;

        public SingleFindingImport(IM3Context context, Audit audit = null) : base(context)
        {
            org = audit?.Organization ?? context.CurrentOrganization;
            Audit = audit;

            //Settings
            Prop(x => x.IsObservation ? "Observation Number" : "Finding Number", "A3").SetValue(e => e.Record.IsObservation = e.Value == "Observation Number");

            //General Info
            Prop(x => x.FindingNumber, "B3");
            Prop(x => x.Media.FirstOrDefault().Name, "B4")
                .Required()
                .SetValue(e => e.Record.Media.Add(GetReference(ReferenceType.Media, e.Value, org)));

            Prop(x => x.Media.Skip(1).FirstOrDefault().Name, "B5").SetValue(e => { if (!string.IsNullOrEmpty(e.Value)) { e.Record.Media.Add(GetReference(ReferenceType.Media, e.Value, org)); } });
            Prop(x => x.Media.Skip(2).FirstOrDefault().Name, "B6").SetValue(e => { if (!string.IsNullOrEmpty(e.Value)) { e.Record.Media.Add(GetReference(ReferenceType.Media, e.Value, org)); } });
            Prop(x => x.Observed, "B7").Required();
            Prop(x => x.Area, "B8");
            Prop(x => x.POC, "B9");
            Prop(x => x.LatLong, "B10").Validate(@"^-?[0-9]+(\.[0-9]+)*,\s*-?[0-9]+(\.[0-9]+)*(;\s*-?[0-9]+(\.[0-9]+)*,\s*-?[0-9]+(\.[0-9]+)*)*$");

            //Finding Details
            Prop(x => x.FindingType.Name, "B13").RequiredIf(x => !x.IsObservation).IgnoreIf(x => x.IsObservation).SetValue(e => e.Record.FindingType = GetReference(ReferenceType.FindingType, e.Value, org));
            Prop(x => x.Category.Name, "B14").Required().SetValue(e =>
            {
                var rt = e.Record.IsObservation ? ReferenceType.ObservationCategory : e.Record.FindingType?.Name == "Compliance" ? ReferenceType.FindingCategoryCompliance : ReferenceType.FindingCategory;
                e.Record.Category = GetReference(rt, e.Value, org);
            });
            Prop(x => x.ClassTier.Name, "B15").IgnoreIf(f => f.IsObservation).SetValue(e => e.Record.ClassTier = GetReference(ReferenceType.FindingClassTier, e.Value, org));
            Prop(x => x.Tier.Name, "B16").IgnoreIf(f => f.IsObservation).SetValue(e => e.Record.Tier = GetReference(ReferenceType.FindingTier, e.Value, org));
            Prop(x => x.Subject, "B17").Required();
            Prop(x => x.PreliminaryRootCauseCode.Name, "B18").RequiredIf(x => !x.IsObservation).IgnoreIf(f => f.IsObservation).SetValue(e => e.Record.PreliminaryRootCauseCode = GetReference(ReferenceType.RootCauseCode, e.Value, org));
            Prop(x => x.Description, "B19").Required();
            Prop(x => x.OtherComments, "B20");

            //Citation
            Prop(x => x.FindingReference.Name, "B23").RequiredIf(x => !x.IsObservation).IgnoreIf(f => f.IsObservation).SetValue(e => e.Record.FindingReference = GetReference(ReferenceType.FindingReference, e.Value, org));
            Prop(x => x.Iso14001.Name, "B24").RequiredIf(f => f.FindingType?.Name == "EMS").SetValue(e => e.Record.Iso14001 = GetReference(ReferenceType.Iso14001, e.Value, org));
            Prop(x => x.ChecklistName, "B25").IgnoreIf(f => f.IsObservation);
            Prop(x => x.ChecklistItemId, "B26").IgnoreIf(f => f.IsObservation);
            Prop(x => x.CitationCode, "B27").RequiredIf(x => !x.IsObservation).IgnoreIf(f => f.IsObservation);
            Prop(x => x.Requirement, "B28").RequiredIf(f => !f.IsObservation);

            //Risk Assessment
            bool isNDW = org.Id == 4 || org.Key.Contains(".4.");
            Prop(x => x.NDWRiskClass.ToString(), "B31").IgnoreIf(f => !isNDW).SetValue(e => e.Record.NDWRiskClass = GetFromDictionary(_ndwRiskClass, e.Value));
            Prop(x => x.RiskSeverityMission.ToString(), "B32").SetValue(e => e.Record.RiskSeverityMission = GetFromDictionary(_missionRiskSeverity, e.Value));
            Prop(x => x.RiskSeverityCompliance.ToString(), "B33").SetValue(e => e.Record.RiskSeverityCompliance = GetFromDictionary(_complianceRiskSeverity, e.Value));
            Prop(x => x.RiskSeverityExternal.ToString(), "B34").SetValue(e => e.Record.RiskSeverityExternal = GetFromDictionary(_externalRiskSeverity, e.Value));
            Prop(x => x.RiskProbability.ToString(), "B35").SetValue(e => e.Record.RiskProbability = GetFromDictionary(_riskProbability, e.Value));
            Prop(x => x.Risk.ToString(), "B36").SetValue(e => e.Record.Risk = GetFromDictionary(_risk, e.Value));
            Prop(x => x.RiskJustification, "B37");

            //Other
            Prop(x => x.IsRepeat, "B40");
            Prop(x => x.ReviewStatusComments, "B41");
        }


        Dictionary<string, NDWRiskClass?> _ndwRiskClass;
        Dictionary<string, RiskSeverity?> _missionRiskSeverity;
        Dictionary<string, RiskSeverity?> _complianceRiskSeverity;
        Dictionary<string, RiskSeverity?> _externalRiskSeverity;
        Dictionary<string, RiskProbability?> _riskProbability;
        Dictionary<string, Risk?> _risk;

        public override void BeforeImport(IEnumerable<ImportValueDictionary<Finding>> rows)
        {
            bool isNDW = org.Id == 4 || org.Key.Contains(".4.");

            if (isNDW)
                _ndwRiskClass = typeof(NDWRiskClass).EnumToEnumerable().ToDictionary(x => x.Item2, x => (NDWRiskClass?)x.Item1);

            _missionRiskSeverity = typeof(RiskSeverity).EnumToEnumerable(customKey: isNDW ? "NDW.Mission" : null).ToDictionary(x => x.Item2, x => (RiskSeverity?)x.Item1);
            _complianceRiskSeverity = typeof(RiskSeverity).EnumToEnumerable(customKey: isNDW ? "NDW.Compliance" : null).ToDictionary(x => x.Item2, x => (RiskSeverity?)x.Item1);
            _externalRiskSeverity = typeof(RiskSeverity).EnumToEnumerable(customKey: isNDW ? "NDW.External" : null).ToDictionary(x => x.Item2, x => (RiskSeverity?)x.Item1);
            _riskProbability = typeof(RiskProbability).EnumToEnumerable(customKey: isNDW ? "NDW" : null).ToDictionary(x => x.Item2, x => (RiskProbability?)x.Item1);
            _risk = typeof(Risk).EnumToEnumerable(customKey: isNDW ? "NDW" : null).ToDictionary(x => x.Item2, x => (Risk?)x.Item1);
        }

        public override Finding CreateRecord(ImportValueDictionary<Finding> values)
        {
            var f = new Finding("", Context.CurrentOrganization)
            {
                Auditor = Context.CurrentUser
            };

            if (Audit != null)
            {
                f.Audit = f.RelatedAudit = Audit;
                new FindingBll(Context.Session).CopyToFinding(f, Audit);
            }

            return f;
        }

        public override void ProcessRecord(Finding f, ImportValueDictionary<Finding> values, bool isNew)
        {
            base.ProcessRecord(f, values, isNew);

            if (string.IsNullOrEmpty(f.FindingNumber))
                f.FindingNumber = new FindingBll(Context.Session).BuildFindingNumber(f);

            if (f.CitationCode == null && f.Iso14001 != null)
                f.CitationCode = f.Iso14001.ToString();

            new FindingDao(Context.Session).Save(f);
        }

        public override void ValidateRecord(Finding f)
        {
            var dupFinding = new FindingDao(Context.Session).GetByFindingNumber(f.FindingNumber);
            if (dupFinding != null && dupFinding != f)
                throw new Exception($"The finding or observation number {f.FindingNumber} already exists in the system. Please select a different number.");
        }
        #endregion

        #region Export
        public void PrepareExcelForm(ExcelPackage file, bool isObservation)
        {
            nextLookupColumn = 'B';

            var sheet = file.Workbook.Worksheets.First();
            var listsSheet = file.Workbook.Worksheets.Skip(1).First();

            sheet.Cells["B2"].Value = org.Name;

            var medias = new ReferenceDao(Context.Session).GetAll(ReferenceType.Media, org, false).Select(x => x.Name);
            for (int row = 4; row <= 6; row++)
                AddListValidation(sheet, listsSheet, row, medias);

            if (isObservation)
            {
                //hide some rows that are not used for observations
                var hiddenRows = new[] { 13, 15, 16, 18, 23, 25, 26, 27 };
                foreach (int row in hiddenRows)
                    sheet.Row(row).Hidden = true;

                sheet.Cells["A3"].Value = "Observation Number";
                sheet.Cells["A12"].Value = "Observation Details";
                sheet.Cells["A14"].Value = "Observation Category*";

                AddListValidation(sheet, listsSheet, 14, ReferenceType.ObservationCategory);
            }
            else
            {
                AddListValidation(sheet, listsSheet, 13, ReferenceType.FindingType);
                AddListValidation(sheet, listsSheet, 15, ReferenceType.FindingClassTier);
                AddListValidation(sheet, listsSheet, 16, ReferenceType.FindingTier);
                AddListValidation(sheet, listsSheet, 18, new ReferenceDao(Context.Session).GetAll(ReferenceType.RootCauseCode, org, false).Where(x => x.Parent != null).Select(x => x.Name));
                AddListValidation(sheet, listsSheet, 23, ReferenceType.FindingReference);
            }

            AddListValidation(sheet, listsSheet, 24, ReferenceType.Iso14001);

            {
                bool isNDW = org.Id == 4 || org.Key.Contains(".4.");
                if (isNDW)
                {
                    AddListValidation(sheet, listsSheet, 31, typeof(NDWRiskClass).EnumToEnumerable());
                    sheet.Cells["A32"].Value = "NDW Mission Impact";
                    sheet.Cells["A33"].Value = "NDW Environmental Severity";
                    sheet.Cells["A34"].Value = "NDW Stakeholder Concerns";
                    sheet.Cells["A35"].Value = "NDW Probability/Likelihood";
                    sheet.Cells["A36"].Value = "NDW Risk Classification";
                }
                else
                {
                    sheet.Row(3).Hidden = true;
                    sheet.Row(31).Hidden = true;
                }

                AddListValidation(sheet, listsSheet, 32, typeof(RiskSeverity).EnumToEnumerable(customKey: isNDW ? "NDW.Mission" : null));
                AddListValidation(sheet, listsSheet, 33, typeof(RiskSeverity).EnumToEnumerable(customKey: isNDW ? "NDW.Compliance" : null));
                AddListValidation(sheet, listsSheet, 34, typeof(RiskSeverity).EnumToEnumerable(customKey: isNDW ? "NDW.External" : null));
                AddListValidation(sheet, listsSheet, 35, typeof(RiskProbability).EnumToEnumerable(customKey: isNDW ? "NDW" : null));
                AddListValidation(sheet, listsSheet, 36, typeof(Risk).EnumToEnumerable(customKey: isNDW ? "NDW" : null));
            }

            ExportBll.ProtectAll(file, allowSelection: false);
        }

        void AddListValidation(ExcelWorksheet sheet, ExcelWorksheet lists, int row, IEnumerable<NameIdPair> values)
        {
            AddListValidation(sheet, lists, row, values.Select(x => x.Name));
        }

        void AddListValidation(ExcelWorksheet sheet, ExcelWorksheet lists, int row, ReferenceType refType)
        {
            var values = new ReferenceDao(Context.Session).GetAll(refType, org, false).Select(x => x.Name);
            AddListValidation(sheet, lists, row, values);
        }

        int nextLookupColumn = 'B';
        void AddListValidation(ExcelWorksheet sheet, ExcelWorksheet lists, int row, IEnumerable<string> values)
        {
            var validationList = sheet.Cells[$"B{row}"].DataValidation.AddListDataValidation();

            char col = (char)nextLookupColumn;
            var list = values.ToList();
            for (int i = 1; i <= list.Count; i++)
            {
                lists.Cells[$"{col}{i}"].Value = list[i - 1];
            }
            
            validationList.Formula.ExcelFormula = $"={lists.Name}!${col}$1:${col}${list.Count}";
            validationList.ShowErrorMessage = true;

            nextLookupColumn++;
        }
        #endregion
    }
}
