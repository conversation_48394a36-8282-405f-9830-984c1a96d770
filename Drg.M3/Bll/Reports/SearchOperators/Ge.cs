using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using NHibernate.Criterion;

namespace Drg.M3.Bll.Reports.SearchOperators
{
    public class Ge : Eq
    {
        public override string Name { get { return "greater or equal"; } }
        public override ICriterion GetRestriction(IProjection projection, object value)
        {
            return Restrictions.Ge(projection, value);
        }
        public override ICriterion GetPropertyRestriction(string propertyName, string property2)
        {
            return Restrictions.GeProperty(propertyName, property2);
        }

        public override string GetOperator()
        {
            return ">=";
        }
    }
}
