using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using NHibernate.Criterion;
using NHibernate;
using System.Reflection;
using Drg.M3.Domain;
using Drg.M3.Bll.Reports.SearchOperators;

namespace Drg.M3.Bll.Reports
{
    public abstract class SearchOperator
    {

        /// <summary>
        /// Conditionally converts a DateTime to an NiftyDate based on the propType supplied
        /// </summary>
        protected object DateProperty(DateTime input, Type propType)
        {
            if (propType == typeof(NiftyDate))
                return NiftyDate.FromCurrentZone(input);
            else
                return input;
        }

        IEnumerable<object> GetMatchingEnumValues(string value, Type enumType, MatchMode matchMode)
        {
            value = value.Trim();

            var names = Enum.GetNames(enumType).ToList();
            var values = Enum.GetValues(enumType).Cast<int>().ToList();

            var allItems = enumType.EnumToEnumerable().ToList();
            IEnumerable<int> matchingItems = null;
            if (matchMode == MatchMode.Exact)
                matchingItems = allItems.Where(o => o.Name.Equals(value, StringComparison.CurrentCultureIgnoreCase)).Select(o => o.Id);
            else if (matchMode == MatchMode.Start)
                matchingItems = allItems.Where(o => o.Name.StartsWith(value, StringComparison.CurrentCultureIgnoreCase)).Select(o => o.Id);
            else if (matchMode == MatchMode.End)
                matchingItems = allItems.Where(o => o.Name.EndsWith(value, StringComparison.CurrentCultureIgnoreCase)).Select(o => o.Id);
            else if (matchMode == MatchMode.Anywhere)
                matchingItems = allItems.Where(o => o.Name.ToLower().Contains(value.ToLower())).Select(o => o.Id);

            return matchingItems.Select(i => Enum.ToObject(enumType, i));
        }
        
        public ICriterion BuildEnumCriterion(string propertyName, string value, Type enumType, MatchMode matchMode)
        {
            var dj = new Disjunction();
            foreach (var item in GetMatchingEnumValues(value, enumType, matchMode))
                dj.Add(Expression.Eq(propertyName, item));

            return dj;
        }

        public string BuildEnumLinq(string field, string value, Type fieldType, MatchMode matchMode)
        {
            var dj = new List<string>();
            foreach (var item in GetMatchingEnumValues(value, fieldType, matchMode))
                dj.Add(field + " == \"" + item + "\"");

            if (dj.Any())
                return "(" + string.Join(" || ", dj.ToArray()) + ")";
            else
                return "false";
        }

        public int Id { get; set; }
        public virtual bool HasValue { get { return true; } }
        public abstract string Name { get; }

        /// <summary>
        /// Parses collections into ".Any(...)"
        /// </summary>
        public string WrapLinqRecursive(Type type, IEnumerable<string> fieldParts, IList<PropertyInfo> breadcrumbs, string value, bool not, Type fieldType, string field = null, int aliasCounter = 0)
        {
            /* Examples:
             *  Collection1.Property1 => Collection1.Any(x0 => x0.Property1...)
             *  Collection1.Property2.Property1 => Collection1.Any(x0 => x0.Property2.Property1...)
             *  Property2.Collection1.Property1 => Property2.Collection1.Any(x0 => x0.Property1...)
             *  Collection1.Collection2.Property1 => Collection1.Any(x0 => x0.Collection2.Any(x1 => x1.Property1...))
             */

            var first = fieldParts.First();
            field = (field == null ? "" : field + ".") + first;

            if (fieldParts.Count() == 1)
            {
                return this.GetSimpleLinq(field, value, not, fieldType, breadcrumbs);
            }
            else
            {
                var pi = type.GetProperty(first);

                type = pi.PropertyType;
                string alias = field;
                string format = "{0}";

                if (pi.PropertyType.IsGenericType && typeof(ISet<>).IsAssignableFrom(pi.PropertyType.GetGenericTypeDefinition()))
                {
                    alias = null;
                    format = (not && this is IsNull && fieldParts.Last() == "Id" ? "!" : "") + $"{field}.Any({{0}})";
                    type = pi.PropertyType.GetGenericArguments()[0];
                }

                breadcrumbs.Add(pi);
                var result = string.Format(format, WrapLinqRecursive(type, fieldParts.Skip(1), breadcrumbs, value, not, fieldType, alias, aliasCounter));
                return result;
            }
        }

        public string GetDynamicLinq(Type rootType, string field, string value, bool not, Type fieldType)
        {
            return WrapLinqRecursive(rootType, field.Split('.'), new List<PropertyInfo>(), value, not, fieldType);
        }

        public abstract string GetSimpleLinq(string field, string value, bool not, Type fieldType, IEnumerable<PropertyInfo> breadcrumbs);
        
        public abstract ICriterion GetCriterion(string propertyPath, string property, string value, Type searchClass, bool not, bool valueIsProperty, Type propertyType);

        public virtual ICriterion GetJoinCriterion(string propertyPath, string property, string value, Type searchClass, bool not, bool valueIsProperty, Type propertyType)
        {
            return null;
        }

        public ICriterion GetCriterion(string propertyPath, string property, string value, Type searchClass, bool not, Type type)
        {
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>) && type.GetGenericArguments()[0].IsEnum)
                type = type.GetGenericArguments()[0];

            if (value.StartsWith("{") && value.EndsWith("}"))
            {
                var prop2 = value.Substring(1, value.Length - 2);

                if (AdvancedSearchBll.GetQueryableProperties(searchClass).Any(p => p.Name == prop2))
                {
                    return GetCriterion(propertyPath, property, prop2, searchClass, not, true, type);
                }
            }

            return GetCriterion(propertyPath, property, value, searchClass, not, false, type);
        }

        public ICriterion GetJoinCriterion(string propertyPath, string property, string value, Type searchClass, bool not, Type type)
        {
            if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>) && type.GetGenericArguments()[0].IsEnum)
                type = type.GetGenericArguments()[0];

            if (value.StartsWith("{") && value.EndsWith("}"))
            {
                var prop2 = value.Substring(1, value.Length - 2);

                if (AdvancedSearchBll.GetQueryableProperties(searchClass).Any(p => p.Name == prop2))
                {
                    return GetJoinCriterion(propertyPath, property, prop2, searchClass, not, true, type);
                }
            }

            return GetJoinCriterion(propertyPath, property, value, searchClass, not, false, type);
        }
    }
}
