//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using NHibernate.Criterion;

//namespace Drg.M3.Bll.Reports
//{
//    public class SearchClass
//    {
//        public string Name { get; set; }
//        public string ClassName { get; set; }
//        public string NavigateUrl { get; set; }
//        public Dictionary<string, SearchProperty> Properties { get; set; }

//        public SearchClass(string name, string navigateUrl, Dictionary<string, SearchProperty> properties) : this(name, navigateUrl, name, properties) { }
//        public SearchClass(string name, string navigateUrl, string className, Dictionary<string, SearchProperty> properties)
//        {
//            this.Name = name;
//            this.ClassName = className;
//            this.Properties = properties;
//            this.NavigateUrl = navigateUrl;
//        }
//    }
//}
