using System;
using System.Collections.Generic;
using System.Linq;
using Drg.M3.Domain;
using Drg.M3.Domain.QA;
using Drg.M3.Dao.QA;
using Drg.M3.Client.Emails.QA;
using NHibernate;
using FlexCel.Core;
using FlexCel.XlsAdapter;
using Drg.M3.Dao;
using Drg.M3.Client;
using System.IO;
using System.Drawing;
using Drg.Core;
using System.Web.Mvc;
using OfficeOpenXml;
using Drg.M3.Client.Models.HW;
using Dapper;
using OfficeOpenXml.Style;
using Drg.M3.Bll.Reports;
using Drg.M3.Domain.Compliance;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Bll.QA
{
    public class FindingBll : AbstractBll
    {
        public FindingBll(IM3Session session) : base(session) { }

        string BuildIndependentFindingNumber(Organization o, int year, bool isObservation)
        {
            //Finding f = new FindingDao(Session).GetLast(year, o);
            //int id = f == null ? 1 : (int.Parse(f.FindingNumber.Split('-').Last()) + 1);
            //return o.Id.ToString() + "-" + year + (isObservation ? "O" : "F") + "-" + id;

            string findingNumber = null;
            int count = 1;
            do
            {
                findingNumber = o.Id.ToString() + "-" + year + (isObservation ? "O" : "F") + "-" + count;
                count++;
            }
            while (new FindingDao(Session).FindingNumberExists(findingNumber));

            return findingNumber;
        }
        string BuildExternalFindingNumber(Finding f)
        {
            var uic = f.Organization.UIC ?? "N00000";
            var scope = f.IsExternal ? "E" : "I";
            var mediaObj = f.Media.FirstOrDefault();
            var mediaStr = mediaObj == null ? "EM" : mediaObj.Abbreviation;
            var cat = f.Category == null ? "NA" : f.Category.Abbreviation;
            var year = (f.Observed ?? f.DateOpened).InCurrentZone().Year.ToString();
            //var count = new FindingDao(Session).GetInitializedFindingsByMediaAndCategory(mediaObj, f.Category, f.Organization, f.DateOpened).ExecuteCount() + 1;
            var template = "{0}-{1}-{2}-{3}-{4}-{5}";

            string findingNumber = null;
            int count = 1;
            do
            {
                string strCount = count.ToString().PadLeft(2, '0');
                findingNumber = string.Format(template, uic, scope, mediaStr, cat, strCount, year);
                count++;
            }
            while (new FindingDao(Session).FindingNumberExists(findingNumber));

            return findingNumber;
        }
        
        public string BuildFindingNumber(Finding f)
        {
            if (f.CarryoverFrom != null)
                return f.CarryoverFrom.FindingNumber + " (K " + DateTime.Now.Year + ")";

            if (f.RelatedAudit == null) 
            {
                return BuildIndependentFindingNumber(f.Organization, (f.Observed ?? f.DateOpened ?? f.DC).InCurrentZone().Year, f.IsObservation);
            }
            else
            {
                return BuildExternalFindingNumber(f);
            }
        }

        public void SendReviewedNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.Inspection != null && finding.Inspection.AuditItem != null && finding.Inspection.AuditItem.Audit.LeadPoc != null)
                bll.SendNotification<Finding>(finding.Inspection.AuditItem.Audit.LeadPoc, new FindingReviewNotification(Session), finding);
            //else if (finding.Inspection == null && finding.ChecklistPoc != null)
            //    bll.SendNotification<Finding>(finding.ChecklistPoc, new FindingReviewNotification(), finding);
        }
        public void SendReadyForReviewNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.Inspection != null && finding.Inspection.AuditItem != null && finding.Inspection.AuditItem.Audit.LeadAuditor != null)
                bll.SendNotification<Finding>(finding.Inspection.AuditItem.Audit.LeadAuditor, new FindingReadyNotification(Session), finding);
            else if (finding.Inspection == null && finding.Auditor != null)
                bll.SendNotification<Finding>(finding.Auditor, new FindingReadyNotification(Session), finding);
        }
        public void SendResolvedNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.Inspection != null && finding.Inspection.AuditItem != null && finding.Inspection.AuditItem.Audit.LeadPoc != null)
                bll.SendNotification<Finding>(finding.Inspection.AuditItem.Audit.LeadPoc, new FindingResolutionNotification(Session), finding);
            //else if (finding.Inspection == null && finding.ChecklistPoc != null)
            //    bll.SendNotification<Finding>(finding.ChecklistPoc, new FindingResolutionNotification(), finding);
        }
        public void SendSubmittedNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.Inspection != null && finding.Inspection.AuditItem != null && finding.Inspection.AuditItem.Audit.LeadPoc != null)
                bll.SendNotification<Finding>(finding.Inspection.AuditItem.Audit.LeadPoc, new FindingNotification(Session), finding);
            //else if (finding.Inspection == null && finding.ChecklistPoc != null)
            //    bll.SendNotification<Finding>(finding.ChecklistPoc, new FindingNotification(), finding);
        }
        public void SendAssignmentNotifications(Finding finding, string[] additionalEmails = null)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.AssignedPoc != null || additionalEmails != null)
                bll.SendNotification<Finding>(finding.AssignedPoc, new FindingAssignmentNotification(Session), finding, additionalEmails);
        }
        public void SendTenantAssignedNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.ResponsibleParty != null)
            {
                List<User> Pocs = finding.ResponsibleParty.Distribution.Users.ToList();
                if (Pocs.Count > 0)
                    bll.SendNotification<Finding>(Pocs, new FindingTenantPOCsAssignmentNotification(Session), finding);
            }
        }
        public void SendTenantFinalizedNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.ResponsibleParty != null)
            {
                List<User> Pocs = finding.ResponsibleParty.Distribution.Users.ToList();
                if (Pocs.Count > 0)
                    bll.SendNotification<Finding>(Pocs, new FindingTenantPOCsNotification(Session), finding);
            }
        }
        public void SendUpdatedNotifications(Finding finding)
        {
            CommunicationBll bll = new CommunicationBll(Session, finding.Association);

            if (finding.EmailDistribution != null)
                bll.SendNotification<Finding>(finding.EmailDistribution, new FindingUpdatedNotification(Session), finding);

            if (finding.ReviewGroup != null)
                bll.SendNotification<Finding>(finding.ReviewGroup.Members, new FindingUpdatedNotification(Session), finding);
        }

        public void PoamReport(IEnumerable<Finding> findings, bool mergePoamRows = false, bool gis = false)
        {
            Session.FlushMode = FlushMode.Never;

            var xls = new XlsFile();

            if (gis)
            {
                xls.NewFile(3);
                
                PoamReport_PopulateSheet(findings, true, xls, 1, "Findings");
                PoamReport_PopulateGISData(findings, xls, 2, "GIS Data");
                PoamReport_PopulateSheet(findings, true, xls, 3, "Findings and GIS Data", gis = true);
            }
            else
            {
                xls.NewFile(1);
                PoamReport_PopulateSheet(findings, mergePoamRows, xls);
            }

            var Response = System.Web.HttpContext.Current.Response;
            var Request = System.Web.HttpContext.Current.Request;

            // output to stream
            using (MemoryStream output = new MemoryStream())
            {
                xls.Save(output, TFileFormats.Xlsx);
                byte[] data = output.ToArray();
                Response.AddHeader("Content-Length", data.Length.ToString());
                Response.BinaryWrite(data);
            }
            Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            // set attachment and filename
            if (Request.UrlReferrer == null)
                Response.AddHeader("Content-Disposition", "filename=POAM Report.xlsx");
            else
                Response.AddHeader("Content-Disposition", "attachment;filename=\"POAM Report.xlsx\"");

        }

        private void PoamReport_PopulateGISData(IEnumerable<Finding> findings, XlsFile xls, int sheet = 1, string sheetName = null)
        {
            xls.ActiveSheet = sheet;
            if (sheetName != null)
                xls.SheetName = sheetName;

            var captions = new[] { "Finding #", "Latitude", "Longitude" };

            for (int col = 0; col < captions.Length; col++)
            {
                xls.SetCellValue(1, col + 1, captions[col]);
            }

            int curRow = 2;
            foreach (var finding in findings)
            {
                var coords = finding.GetGeoCoords();
                foreach (var coord in coords)
                {
                    xls.SetCellValue(curRow, 1, finding.FindingNumber);
                    xls.SetCellValue(curRow, 2, coord.Latitude);
                    xls.SetCellValue(curRow, 3, coord.Longitude);

                    curRow++;
                }
            }
        }

        private void PoamReport_PopulateSheet(IEnumerable<Finding> findings, bool mergePoamRows, XlsFile xls, int sheet = 1, string sheetName = null, bool gis = false)
        {
            xls.ActiveSheet = sheet;
            if (sheetName != null)
                xls.SheetName = sheetName;

            var captions = new List<string>() { "Organization", "Finding Number", "Subject", "Finding Status", "Associated Findings", "Finding Description", "Related Media(s)", "Finding Type", "Finding Category", "Installation Assigned Root Cause Code", "Finding POC", "Plan of Action Synopsis", "Responsible Party" };
            if (!gis)
                captions.AddRange(new[] { "Milestone #", "Milestone/Plan of Action", "Responsiblity (Primary POC)", "Reporting (Env. POC)", "Date Added", "Estimated Date of Completion", "Actual Date of Completion", "Current Status", "Date Last Revised", "Notes" });
            else
                captions.AddRange(new[] { "Latitude", "Longitude" });

            var rotatedHeader = new List<bool>() { true, true, true, true, true, false, true, true, true, true, true, false, false };
            if (!gis)
                rotatedHeader.AddRange(new[] { true, false, false, false, true, true, true, true, true, false });
            else
                rotatedHeader.AddRange(new[] { false, false });

            var colWidths = new List<int>() { 59, 300, 300, 200, 75, 327, 100, 72, 64, 75, 51, 335, 200 };
            if (!gis)
                colWidths.AddRange(new[] { 45, 320, 113, 112, 81, 85, 93, 79, 67, 295 });
            else
                colWidths.AddRange(new[] { 45, 45 });

            int numRows = gis ? findings.Sum(x => Math.Max(x.GetGeoCoords().Count, 1))
                : findings.Sum(x => x.Poam == null || x.Poam.Milestones.Count <= 1 ? 1 : x.Poam.Milestones.Count);

            var allMilestones = findings.Where(x => x.Poam != null).SelectMany(x => x.Poam.Milestones).ToList();

            Dictionary<int, IEnumerable<string>> milestoneNotes = new Dictionary<int, IEnumerable<string>>();
            if (!gis)
            {
                foreach (var part in allMilestones.Partition(2000))
                {
                    var ids = part.Select(x => x.Id).ToArray();
                    var notes =
                        Session.Query<Communication2Object>().Where(x => x.ClassName == typeof(Milestone).FullName && ids.Contains(x.ObjectId))
                        .Select(x => new { x.ObjectId, x.Communication.Message, x.Communication.DC }).ToList()
                        .GroupBy(x => x.ObjectId).ToDictionary(x => x.Key, x => x.OrderByDescending(y => y.DC).Select(y => y.DC?.ToShortDateString() + " " + y.Message));

                    milestoneNotes.AddAll(notes);
                }
            }

            //Define formats
            var wrapF = xls.GetDefaultFormat;
            wrapF.WrapText = true;
            int wrapXF = xls.AddFormat(wrapF);
            if (numRows > 0)
                xls.SetCellFormat(1, 1, numRows, 22, wrapXF);

            var f = xls.GetDefaultFormat;
            f.Font.Style = TFlxFontStyles.Bold;
            int headerXF = xls.AddFormat(f);
            f.Rotation = 90;
            int headerRotatedXF = xls.AddFormat(f);
            f.Font.Style = TFlxFontStyles.None;
            int rotatedXF = xls.AddFormat(f);

            f = wrapF;
            f.FillPattern.Pattern = TFlxPatternStyle.Solid;
            f.FillPattern = new TFlxFillPattern { Pattern = TFlxPatternStyle.Solid, FgColor = Color.Red };
            int redXF = xls.AddFormat(f);
            f.FillPattern = new TFlxFillPattern { Pattern = TFlxPatternStyle.Solid, FgColor = Color.Yellow };
            int yellowXF = xls.AddFormat(f);
            f.FillPattern = new TFlxFillPattern { Pattern = TFlxPatternStyle.Solid, FgColor = Color.Green };
            int greenXF = xls.AddFormat(f);

            for (int col = 0; col < captions.Count; col++)
            {
                xls.SetCellValue(1, col + 1, captions[col]);
                xls.SetCellFormat(1, col + 1, rotatedHeader[col] ? headerRotatedXF : headerXF);
                xls.SetColWidth(col + 1, colWidths[col] * 36);
            }

            int curRow = 2;
            foreach (var finding in findings)
            {
                var coords = finding.GetGeoCoords();
                int numChildRows = gis ? coords.Count : finding.Poam == null ? 0 : finding.Poam.Milestones.Count;

                if (mergePoamRows || numChildRows == 0)
                {
                    if (numChildRows > 1)
                    {
                        for (int col = 1; col <= 12; col++)
                            xls.MergeCells(curRow, col, curRow + numChildRows - 1, col);
                    }

                    xls.SetCellValue(curRow, 1, finding.Organization.ToString());
                    xls.SetCellValue(curRow, 2, finding.FindingNumber);
                    xls.SetCellValue(curRow, 3, finding.Subject);
                    xls.SetCellValue(curRow, 4, finding.Status.ToString());
                    xls.SetCellValue(curRow, 5, finding.RelatedFindings.ConcatToString());
                    xls.SetCellValue(curRow, 6, finding.Description);
                    xls.SetCellValue(curRow, 7, finding.Media.ConcatToString());
                    if (finding.FindingType != null)
                        xls.SetCellValue(curRow, 8, finding.FindingType.Name);
                    if (finding.Category != null)
                        xls.SetCellValue(curRow, 9, finding.Category.Name);
                    if (finding.RootCauseCode != null)
                        xls.SetCellValue(curRow, 10, finding.RootCauseCode);
                    if (finding.AssignedPoc != null)
                        xls.SetCellValue(curRow, 11, finding.AssignedPoc.Name);
                    if (finding.Poam != null)
                        xls.SetCellValue(curRow, 12, finding.Poam.Synopsis);
                    if (finding.ResponsibleParty != null)
                        xls.SetCellValue(curRow, 13, finding.ResponsibleParty.Name);
                }

                if (gis)
                {
                    for (int i = 0; i < numChildRows; i++)
                    {
                        var childRow = curRow + i;
                        var coord = coords[i];
                        xls.SetCellValue(childRow, 14, coord.Latitude);
                        xls.SetCellValue(childRow, 15, coord.Longitude);
                    }
                }
                else if (finding.Poam != null) 
                {
                    var milestonesList = finding.Poam.Milestones.ToList();

                    for (int i = 0; i < numChildRows; i++)
                    {
                        int mRow = curRow + i;

                        if (!mergePoamRows)
                        {
                            xls.SetCellValue(mRow, 1, finding.Organization.ToString());
                            xls.SetCellValue(mRow, 2, finding.FindingNumber);
                            xls.SetCellValue(mRow, 3, finding.Subject);
                            xls.SetCellValue(mRow, 4, finding.Status.ToString());
                            xls.SetCellValue(mRow, 5, finding.RelatedFindings.ConcatToString());
                            xls.SetCellValue(mRow, 6, finding.Description);
                            xls.SetCellValue(curRow, 7, finding.Media.ConcatToString());
                            if (finding.FindingType != null)
                                xls.SetCellValue(mRow, 8, finding.FindingType.Name);
                            if (finding.Category != null)
                                xls.SetCellValue(mRow, 9, finding.Category.Name);
                            if (finding.RootCauseCode != null)
                                xls.SetCellValue(mRow, 10, finding.RootCauseCode);
                            if (finding.AssignedPoc != null)
                                xls.SetCellValue(mRow, 11, finding.AssignedPoc.Name);
                            if (finding.Poam != null)
                                xls.SetCellValue(mRow, 12, finding.Poam.Synopsis);
                            if (finding.ResponsibleParty != null)
                                xls.SetCellValue(mRow, 13, finding.ResponsibleParty.Name);
                        }

                        var milestone = milestonesList[i];

                        xls.SetCellValue(mRow, 14, i + 1);
                        xls.SetCellValue(mRow, 15, milestone.Name + " " + milestone.Synopsis);
                        if (milestone.Poc != null)
                            xls.SetCellValue(mRow, 16, milestone.Poc.Name);
                        if (finding.Poam.Manager != null)
                            xls.SetCellValue(mRow, 17, finding.Poam.Manager);
                        if (milestone.DC != null)
                            xls.SetCellValue(mRow, 18, milestone.DC.ToShortDateString());
                        if (milestone.DueDate != null)
                            xls.SetCellValue(mRow, 19, milestone.DueDate.ToShortDateString());
                        if (milestone.Completed != null)
                            xls.SetCellValue(mRow, 20, milestone.Completed.ToShortDateString());

                        //string status = milestone.Completed != null ? "Complete"
                        //    : milestone.DueDate < NiftyDate.Now ? "Overdue"
                        //    : "In Progress";

                        xls.SetCellValue(mRow, 21, milestone.Progress.GetValueOrDefault().ToString("p"));

                        if (milestone.DM != null)
                            xls.SetCellValue(mRow, 22, milestone.DM.ToShortDateString());

                        if (milestoneNotes.ContainsKey(milestone.Id))
                        {
                            var notes = string.Join("\r\n\r\n", milestoneNotes[milestone.Id]);
                            xls.SetCellValue(mRow, 23, notes);
                        }
                        //var notes = string.Join("\r\n\r\n",
                        //    new NoteDao(Session).GetNotesForObject(M3Context.Current.User, milestone)
                        //        .Execute(Session).Select(n => n.ToString()).ToArray()
                        //);
                        //xls.SetCellValue(mRow, 22, notes);

                        for (int col = 13; col <= 24; col++)
                        {
                            if (col == 20) //status color
                            {
                                int XF = milestone.Progress < 0.5m ? redXF : milestone.Progress < 1 ? yellowXF : greenXF;
                                xls.SetCellFormat(mRow, col + 1, XF);
                            }
                        }
                    }
                }

                curRow += Math.Max(1, numChildRows);
            }
        }

        public static bool IsCompliance(Finding f)
        {
            return f.FindingType != null &&
                (f.FindingType.Name == "Compliance" || f.FindingType.Name.StartsWith("Class"));
        }
        public static bool IsEMS(Finding f)
        {
            return f.FindingType != null &&
                (f.FindingType.Name == "EMS");
        }

        public void EditAssignment(Finding f, int? assignedPoc, bool hasCapa, bool hasReview, NiftyDate targetDate, string assignmentComments, /*Risk? risk,*/ int? closurePoc, int? responsibleParty, int? fundingEntity, bool replaceWithBlank = true, string[] additionalEmails = null)
        {
            if (!f.HasAssignedPocPermission(M3Context.Current.User) && f.CreatedBy != M3Context.Current.User)
                new SecurityBll(Session).ValidateRecord(f, M3Context.Current.User, AccessType.Edit);

            var oldAssignedPoc = f.AssignedPoc;

            //new ReferenceBll(Session).UpdateReferences(f.Media, media);
            if (assignedPoc != null || replaceWithBlank)
                f.AssignedPoc = Session.Get<User>(assignedPoc);

            if (closurePoc != null || replaceWithBlank)
                if ((f.GetAudit() != null && f.GetAudit().HasLeadAuditorPermission(M3Context.Current.User)) ||
                    (f.GetAudit() == null && M3Context.Current.User == f.Auditor) || (f.GetAudit() != null && M3Context.Current.User == f.GetAudit().CreatedBy))
                    f.ClosurePoc = Session.Get<User>(closurePoc);

            //f.HasCapa = resolutionType=="capa";

            var user = M3Context.Current.User;
            if (user == f.ClosurePoc
                || (f.RelatedAudit == null || (!f.RelatedAudit.IsExternal && f.ClosurePoc == null))
                || (f.RelatedAudit != null && f.RelatedAudit.HasLeadAuditorPermission(user))
                || (f.RelatedAudit != null && user == f.RelatedAudit.CreatedBy)
                || f.Status == FindingStatus.Open)
            {
                f.HasReview = hasReview;
                f.HasCapa = hasCapa;
            }

            if (targetDate != null || replaceWithBlank)
                f.TargetDate = targetDate;

            if (assignmentComments != null || replaceWithBlank)
                f.AssignmentComments = assignmentComments;

            //if (risk != null || replaceWithBlank)
            //    f.Risk = risk;

            if (responsibleParty != null || replaceWithBlank)
                if (responsibleParty.HasValue)
                    f.ResponsibleParty = new TenantDao(Session).Get(responsibleParty.Value);
                else f.ResponsibleParty = null;

            if (fundingEntity != null || replaceWithBlank)
                if (fundingEntity.HasValue)
                    f.FundingEntity = new TenantDao(Session).Get(fundingEntity.Value);
                else f.FundingEntity = null;

            if (f.Status == FindingStatus.Open)
            {
                f.Status = FindingStatus.Assigned;

                f.History.Add(new FindingHistory(f)
                {
                    Date = NiftyDate.Now,
                    User = M3Context.Current.User.ToString(),
                    Comment = assignmentComments
                });

                new FindingBll(Session).SendAssignmentNotifications(f, additionalEmails);
                if (f.ResponsibleParty != null)
                    new FindingBll(Session).SendTenantAssignedNotifications(f);
            }
            else if (oldAssignedPoc != f.AssignedPoc)
            {
                new FindingBll(Session).SendAssignmentNotifications(f, additionalEmails);
            }
            f.AdditionalNotificationEmails = additionalEmails == null ? null : string.Join(",", additionalEmails);

            Session.Save(f);
        }

        public FileResult NavyAuditDashboard(Organization org, int[] ids, bool isInternal)
        {
            string sql = $@"
select 
	a.OrganizationId,
	o.Name as Organization,
	case when isnull(sum(case when ft.Name='Compliance' then 1 else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='Compliance' and f.FindingStatusEnum=4 then 1 else 0 end))/sum(case when ft.Name='Compliance' then 1 else 0 end) end as CompliancePercentClosed,
	
    sum(case when ft.Name='Compliance' and f.FindingStatusEnum=4 then 1 else 0 end) as CompliancePercentClosedN,
    sum(case when ft.Name='Compliance' then 1 else 0 end) as CompliancePercentClosedD,

	case when isnull(sum(case when ft.Name='Compliance' then pCounts.TotalMilestones else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='Compliance' then pCounts.OnTargetMilestones else 0 end))/sum(case when ft.Name='Compliance' then pCounts.TotalMilestones else 0 end) end as CompliancePercentOpenMilestonesOnTarget,

    sum(case when ft.Name='Compliance' then pCounts.OnTargetMilestones else 0 end) as CompliancePercentOpenMilestonesOnTargetN,
    sum(case when ft.Name='Compliance' then pCounts.TotalMilestones else 0 end) as CompliancePercentOpenMilestonesOnTargetD,

	avg(case when ft.Name='Compliance' and f.FindingStatusEnum=4 and not f.Observed is null then convert(decimal(19,5),datediff(hour,f.Observed,f.Closed))/24 else null end) as ComplianceAvgDaysToCloseFindings,
	
	case when isnull(sum(case when ft.Name='EMS' then 1 else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='EMS' and f.FindingStatusEnum=4 then 1 else 0 end))/sum(case when ft.Name='EMS' then 1 else 0 end) end as ConformancePercentClosed,

    sum(case when ft.Name='EMS' and f.FindingStatusEnum=4 then 1 else 0 end) as ConformancePercentClosedN,
    sum(case when ft.Name='EMS' then 1 else 0 end) as ConformancePercentClosedD,

	case when isnull(sum(case when ft.Name='EMS' then pCounts.TotalMilestones else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='EMS' then pCounts.OnTargetMilestones else 0 end))/sum(case when ft.Name='EMS' then pCounts.TotalMilestones else 0 end) end as ConformancePercentOpenMilestonesOnTarget,

    sum(case when ft.Name='EMS' then pCounts.OnTargetMilestones else 0 end) as ConformancePercentOpenMilestonesOnTargetN,
    sum(case when ft.Name='EMS' then pCounts.TotalMilestones else 0 end) as ConformancePercentOpenMilestonesOnTargetD,

	avg(case when ft.Name='EMS' and f.FindingStatusEnum=4 and not f.Observed is null then convert(decimal(19,5),datediff(hour,f.Observed,f.Closed))/24 else null end) as ConformanceAvgDaysToCloseFindings,

	substring((
		select 
			'; '+a.Name  AS [text()]
		from 
			(select distinct s.Name from ENV_Schedule s left join COR_Reference at on s.AuditTypeId=at.Id where s.OrganizationId=a.OrganizationId and isnull(at.Name,''){(isInternal ? "!=" : "=")}'Navy External Environmental Audit' {(ids != null ? "and s.Id in @ids" : "")}) a
		order by 
			a.Name 
		For XML PATH ('')
	),3,1000) as AuditNames,
	substring((
		select 
			'; '+convert(varchar,a.EndDate,101)  AS [text()]
		from 
			(select distinct s.EndDate from ENV_Schedule s left join COR_Reference at on s.AuditTypeId=at.Id where s.OrganizationId=a.OrganizationId and isnull(at.Name,''){(isInternal ? "!=" : "=")}'Navy External Environmental Audit' {(ids != null ? "and s.Id in @ids" : "")}) a
		order by 
			a.EndDate 
		For XML PATH ('')
	),3,1000) as AuditDates
from
	ENV_Finding f 
	join ENV_Schedule a on f.RelatedAuditId=a.Id
	join CON_Organization o on a.OrganizationId=o.Id
	left join COR_Reference at on a.AuditTypeId=at.Id
	left join COR_Reference ft on f.FindingTypeId=ft.Id
	left join (
		select
			f.Id as FindingId,
			count(*) as TotalMilestones,
			sum(case when m.DueDate>getdate() then 1 else 0 end) as OnTargetMilestones
		from 
			ENV_Finding f
			join ENV_Poam p on f.PoamId=p.Id
			join ENV_Milestone m on m.PoamId=p.Id
		where
			f.DD is null and p.DD is null and m.DD is null
			and f.FindingStatusEnum<>4 and f.FindingStatusEnum<>0 and m.Progress<1
		group by
			f.Id
	) pCounts on pCounts.FindingId=f.Id
where
	a.DD is null and f.DD is null and f.FindingStatusEnum!=0
	and isnull(at.Name,''){(isInternal ? "!=" : "=")}'Navy External Environmental Audit'
    and f.IsObservation=0
    and (a.OrganizationId = @orgId or o.KeyChain like @key + '.%')
    /**where**/
group by
	a.OrganizationId,
	o.Name
having
	sum(case when f.FindingStatusEnum in (0,4) then 0 else 1 end)>0
order by
	o.Name
";
            SqlBuilder b = new SqlBuilder();
            var query = b.AddTemplate(sql, new
            {
                orgId = org.Id,
                key = org.Key
            }, existingClauses: new[] { "where" });

            if (ids != null)
                b.Where("a.Id in @ids", new { ids });

            var results = Session.Connection.Query<NavyExternalAuditDashboardModel>(query.RawSql, query.Parameters, Session.GetDbTransaction());

            using (var p = new ExcelPackage())
            {
                ExcelWorksheet ws = p.Workbook.Worksheets.Add("Audit Dashboard");

                string[] captions = { "Installation", "% Finding Closed", "% Open Milestones on Target", "Avg Days to Close Findings", "% Finding Closed", "% Open Milestones on Target", "Avg Days to Close Findings", "Audit Type", "AUDIT DATES" };

                ws.Cells[1, 1].Value = "Environmental";
                ws.Cells[1, 2, 1, 7].Merge = true;
                ws.Cells[1, 2, 1, 7].Value = org;
                ws.Cells[1, 2, 1, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ws.Cells[1, 8].Value = "Report Date";
                ws.Cells[1, 9].Value = DateTime.Now.ToShortDateString();

                ws.Cells[2, 2, 2, 4].Merge = true;
                ws.Cells[2, 2, 2, 4].Value = "Compliance";
                ws.Cells[2, 2, 2, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ws.Cells[2, 5, 2, 7].Merge = true;
                ws.Cells[2, 5, 2, 7].Value = "Conformance";
                ws.Cells[2, 5, 2, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                for (int col = 0; col < captions.Length; col++)
                {
                    ws.Cells[3, col + 1].Value = captions[col];
                    ws.Column(col + 1).AutoFit();
                }

                int y = 4;
                foreach (var row in results)
                {
                    ws.Cells[y, 1].Value = row.Organization;
                    SetValueAndBackgroundColor(ws.Cells[y, 2], row.CompliancePercentClosed, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 3], row.CompliancePercentOpenMilestonesOnTarget, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 4], row.ComplianceAvgDaysToCloseFindings, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 5], row.ConformancePercentClosed, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 6], row.ConformancePercentOpenMilestonesOnTarget, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 7], row.ConformanceAvgDaysToCloseFindings, "n0", true);
                    ws.Cells[y, 8].Value = row.AuditNames;
                    ws.Cells[y, 9].Value = row.AuditDates;

                    y++;
                }

                SetBackgroundColor(ws.Cells[3, 1, 3, 9], Color.LightBlue);
                if (results.Count() > 0)
                {
                    SetBackgroundColor(ws.Cells[4, 8, y - 1, 9], Color.LightGray);
                }

                if (results.Count() > 1)
                {
                    SetBackgroundColor(ws.Cells[y, 1, y, 9], Color.LightGray);

                    y++;

                    ws.Cells[y, 1].Value = "Totals";

                    SetValueAndBackgroundColor(ws.Cells[y, 2], results.Sum(x => x.CompliancePercentClosedD) == 0 ? (decimal?)null : (results.Sum(x => x.CompliancePercentClosedN) / results.Sum(x => x.CompliancePercentClosedD)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 3], results.Sum(x => x.CompliancePercentOpenMilestonesOnTargetD) == 0 ? (decimal?)null : (results.Sum(x => x.CompliancePercentOpenMilestonesOnTargetN) / results.Sum(x => x.CompliancePercentOpenMilestonesOnTargetD)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 4], results.Average(x => x.ComplianceAvgDaysToCloseFindings), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 5], results.Sum(x => x.ConformancePercentClosedD) == 0 ? (decimal?)null : (results.Sum(x => x.ConformancePercentClosedN) / results.Sum(x => x.ConformancePercentClosedD)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 6], results.Sum(x => x.ConformancePercentOpenMilestonesOnTargetD) == 0 ? (decimal?)null : (results.Sum(x => x.ConformancePercentOpenMilestonesOnTargetN) / results.Sum(x => x.ConformancePercentOpenMilestonesOnTargetD)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 7], results.Average(x => x.ConformanceAvgDaysToCloseFindings), "n0", true);
                }

                ws.Column(1).AutoFit(20, 50);
                ws.Column(8).AutoFit(20, 50);
                ws.Column(9).AutoFit(20, 50);

                ws.Cells[1, 1, 3, 9].Style.Font.Bold = true;

                ws.Cells[1, 1, y, 9].Style.Border.Left.Style =
                    ws.Cells[1, 1, y, 9].Style.Border.Right.Style =
                    ws.Cells[1, 1, y, 9].Style.Border.Top.Style =
                    ws.Cells[1, 1, y, 9].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                ws.Cells[1, 1, y, 9].Style.Border.BorderAround(ExcelBorderStyle.Thick);

                {
                    var wsFindings = p.Workbook.Worksheets.Add("Finding Dump");
                    var findings = new FindingDao(Session).AuditDashboardFindings(org, ids, isInternal).Execute(Session);
                    PopulateFindingDump(wsFindings, findings);

                    var wsPoams = p.Workbook.Worksheets.Add("POAM Dump");
                    var poams = findings.Where(x => x.Poam != null).Select(x => x.Poam).Distinct().ToList();
                    PopulatePoamDump(wsPoams, poams);
                }

                return p.ToFileResult($"Navy{(isInternal ? "Internal" : "External")}AuditDashboard.xlsx");
            }
        }

        public void PopulatePoamDump(ExcelWorksheet ws, IEnumerable<Poam> poams)
        {
            var def = poams.GetExportData(defaultAutoFit: true)
                .Column("Plan of Action", x => x.Synopsis)
                .Column("Manager", x => x.Manager?.w_EntityName)
                .Column("Target Date", x => x.DueDate)
                .Column("Completion Date", x => x.Completed)
                .Column("Milestones", x => string.Join(", ", x.Milestones))
                .Column("Status", x => x.Status)
                .Column("Inactive", x => x.IsInactive)
                .Column("Organization", x => x.Organization)
                .Column("Milestone Summary (Name, POC, Progress, Target/Completion dates)", x => x.MilestoneSummary)
                .Column("Uncompletable Reason", x => x.UncompletableReason)
                .Column("Uncompletable Text", x => x.UncompletableText)
                .Column("POAM Approved Date", x => x.AuditorApproved);

            ExcelBll.PopulateExport(def, poams, ws);
        }

        public void PopulateFindingDump(ExcelWorksheet ws, IEnumerable<Finding> findings)
        {
            var def = findings.GetExportData(defaultAutoFit: true)
                .Column("Inactive", x => x.IsInactive)
                .Column("Organization", x => x.Organization)
                .Column("Is Observation", x => x.IsObservation)
                .Column("Is External", x => x.IsExternal)
                .Column("Subject", x => x.Subject)
                .Column("Finding Number", x => x.FindingNumber)
                .Column("Finding Type", x => x.FindingType)
                .Column("Category", x => x.Category)
                .Column("Location Details", x => x.Area)
                .Column("Media", x => x.w_Media)
                .Column("Description", x => x.Description)
                .Column("Required Actions / Other Comments", x => x.OtherComments)
                .Column("Status", x => x.Status)
                .Column("POAM", x => x.Poam)
                .Column("POAM Status", x => x.Poam?.Status)
                .Column("Review Status", x => x.ReviewStatus)
                .Column("Date Observed", x => x.Observed)
                .Column("Repeat Finding", x => x.IsRepeat)
                .Column("Region", x => x.Organization.Region?.Name)
                .Column("Responsible Party", x => x.ResponsibleParty)
                .Column("Evaluation Type", x => x.Inspection?.AuditItem?.AuditTemplate?.EvaluationType)
                .Column("Subject", x => x.Subject)
                .Column("POC", x => x.POC)
                .Column("Auditor", x => x.Auditor?.Entity)
                .Column("Inspection", x => x.Inspection)
                .Column("Checklist Name", x => x.ChecklistName)
                .Column("Item ID", x => x.ChecklistItemId)
                .Column("Citation Code", x => x.CitationCode)
                .Column("Requirement", x => x.Requirement)
                .Column("Finding Reference", x => x.FindingReference)
                .Column("Assigned To", x => x.AssignedPoc?.Entity)
                .Column("Risk", x => x.Risk)
                .Column("Has CAPA", x => x.HasCapa)
                .Column("Target Date", x => x.TargetDate)
                .Column("Assignment Comments", x => x.AssignmentComments)
                .Column("ISO 14001", x => x.Iso14001)
                .Column("Root Cause Code", x => x.RootCauseCode)
                .Column("Root Cause Description", x => x.RootCause)
                .Column("Preliminary Root Cause", x => x.PreliminaryRootCauseCode)
                .Column("Resolved By", x => x.ResolvedBy?.Entity)
                .Column("Date Resolved", x => x.Resolved)
                .Column("Corrective Actions", x => x.CorrectiveActions)
                .Column("Preventive Actions", x => x.PreventativeActions)
                .Column("CAPA Other Comments", x => x.CapaOtherComments)
                .Column("Date Closed", x => x.Closed)
                .Column("Closed By", x => x.ClosedBy?.Entity)
                .Column("Closure Comment", x => x.ClosureComment)
                .Column("Review Comment", x => x.ReviewComment)
                .Column("Audit/Inspection Title", x => x.RelatedAudit?.Name)
                .Column("Audit/Inspection Type", x => x.RelatedAudit?.AuditType)
                .Column("Audit - Agency Level", x => x.RelatedAudit?.AgencyLevel)
                .Column("Audit - Inspecting Agency", x => x.RelatedAudit?.InspectingAgency)
                .Column("Audit - Regulatory Media", x => x.RelatedAudit?.w_RegulatoryMedia)
                .Column("POAM Approved Date", x => x.Poam?.AuditorApproved);

            ExcelBll.PopulateExport(def, findings, ws);
        }

        public FileResult PWDExternalAuditDashboardExport(Organization org, int[] ids, bool mostRecentOnly)
        {
            Reports.ReportBll bll = new Reports.ReportBll(Session);
            IEnumerable<Finding> records = new FindingDao(Session).AuditDashboardFindings(org, ids).Execute(Session);
            if (mostRecentOnly)
            {
                var mostRecentAudits = records.Select(x => x.RelatedAudit).Distinct()
                    .GroupBy(x => x.Organization).Select(x => x.OrderByDescending(y => y.EndDate).First()).ToDictionary(x => x.Id);
                records = records.Where(x => mostRecentAudits.ContainsKey(x.RelatedAudit.Id));
            }
            var rr = bll.QueueReportByMapping(ReportTemplateMappingType.ExternalAuditDashboard, records, user: M3Context.Current.User);
            if (rr != null)
            {
                return FileStreamDownloadResult.Create(rr.File);
            }
            else
            {
                throw new InvalidOperationException("Please create report mapping for External Audit Dashboard");
            }
        }

        public FileResult PWDExternalAuditDashboard(Organization org, int[] ids, bool mostRecentOnly)
        {
            string sql = @"
select 
    " + (mostRecentOnly ? "a.Id," : "") + @"
	a.OrganizationId,
	o.Name as Organization,
	case when isnull(sum(case when ft.Name='Compliance' then 1 else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='Compliance' and f.FindingStatusEnum=4 then 1 else 0 end))/sum(case when ft.Name='Compliance' then 1 else 0 end) end as CompliancePercentClosed,

	sum(case when ft.Name='Compliance' and f.FindingStatusEnum=4 then 1 else 0 end) as ComplianceClosedFindings,
	sum(case when ft.Name='Compliance' then 1 else 0 end) as ComplianceTotalFindings,
	
	case when isnull(sum(case when ft.Name='Compliance' then pCounts.TotalMilestones else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='Compliance' then pCounts.OnTargetMilestones else 0 end))/sum(case when ft.Name='Compliance' then pCounts.TotalMilestones else 0 end) end as CompliancePercentOpenMilestonesOnTarget,

	sum(case when ft.Name='Compliance' then pCounts.OnTargetMilestones else 0 end) as ComplianceOpenMilestonesOnTarget,
	sum(case when ft.Name='Compliance' then pCounts.TotalMilestones else 0 end) as ComplianceTotalOpenMilestones,

	case when sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end) = 0 then null else
		convert(decimal(19,5),(select sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) and f.Observed<dateadd(year,-1,getdate()) then 1 else 0 end))) 
			/ convert(decimal(19,5),(select sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end)))
				end as CompliancePercentOpenFindingsAfter1Year,

	sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) and f.Observed<dateadd(year,-1,getdate()) then 1 else 0 end) as ComplianceOpenFindingsAfter1Year,
	sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end) as ComplianceTotalOpenFindings,

	case when sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end) = 0 then null else
		convert(decimal(19,5),(select sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) and p.UncompletableReason=1 then 1 else 0 end))) 
			/ convert(decimal(19,5),(select sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end)))
				end as CompliancePercentOpenFindingsOutOfControl,

	sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) and p.UncompletableReason=1 then 1 else 0 end) as ComplianceOpenFindingsOutOfControl,

    case when sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end) = 0 then null else
		convert(decimal(19,5),(select sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) and fc.Name='Policy' then 1 else 0 end))) 
			/ convert(decimal(19,5),(select sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) then 1 else 0 end)))
				end as CompliancePercentOpenPolicyFindings,

	sum(case when ft.Name = 'Compliance' and not f.FindingStatusEnum in (0,4) and fc.Name='Policy' then 1 else 0 end) as ComplianceOpenPolicyFindings,

	case when isnull(sum(case when ft.Name='EMS' then 1 else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='EMS' and f.FindingStatusEnum=4 then 1 else 0 end))/sum(case when ft.Name='EMS' then 1 else 0 end) end as ConformancePercentClosed,

	sum(case when ft.Name='EMS' and f.FindingStatusEnum=4 then 1 else 0 end) as ConformanceClosedFindings,
	sum(case when ft.Name='EMS' then 1 else 0 end) as ConformanceTotalFindings,

	case when isnull(sum(case when ft.Name='EMS' then pCounts.TotalMilestones else 0 end),0)=0 then null else
		convert(decimal(19,5),sum(case when ft.Name='EMS' then pCounts.OnTargetMilestones else 0 end))/sum(case when ft.Name='EMS' then pCounts.TotalMilestones else 0 end) end as ConformancePercentOpenMilestonesOnTarget,

	sum(case when ft.Name='EMS' then pCounts.OnTargetMilestones else 0 end) as ConformanceOpenMilestonesOnTarget,
	sum(case when ft.Name='EMS' then pCounts.TotalMilestones else 0 end) as ConformanceTotalOpenMilestones,

	case when sum(case when ft.Name = 'EMS' and not f.FindingStatusEnum in (0,4) then 1 else 0 end) = 0 then null else
		convert(decimal(19,5),(select sum(case when ft.Name = 'EMS' and not f.FindingStatusEnum in (0,4) and f.Observed<dateadd(year,-1,getdate()) then 1 else 0 end))) 
			/ convert(decimal(19,5),(select sum(case when ft.Name = 'EMS' and not f.FindingStatusEnum in (0,4) then 1 else 0 end)))
				end as ConformancePercentOpenFindingsAfter1Year,
    
	sum(case when ft.Name = 'EMS' and not f.FindingStatusEnum in (0,4) and f.Observed<dateadd(year,-1,getdate()) then 1 else 0 end) as ConformanceOpenFindingsAfter1Year,
	sum(case when ft.Name = 'EMS' and not f.FindingStatusEnum in (0,4) then 1 else 0 end) as ConformanceTotalOpenFindings,

    " + (mostRecentOnly ? @"
    a.Name as AuditNames,
    convert(varchar, a.EndDate, 101) as AuditDates,
    a.EndDate
    " : @"
	substring((
		select 
			'; '+a.Name  AS [text()]
		from 
			(select distinct s.Name from ENV_Schedule s join COR_Reference at on s.AuditTypeId=at.Id where s.OrganizationId=a.OrganizationId and at.Name='Navy External Environmental Audit' " + (ids != null ? "and s.Id in @ids" : "") + @") a
		order by 
			a.Name 
		For XML PATH ('')
	),3,1000) as AuditNames,
	substring((
		select 
			'; '+convert(varchar,a.EndDate,101)  AS [text()]
		from 
			(select distinct s.EndDate from ENV_Schedule s join COR_Reference at on s.AuditTypeId=at.Id where s.OrganizationId=a.OrganizationId and at.Name='Navy External Environmental Audit' " + (ids != null ? "and s.Id in @ids" : "") + @") a
		order by 
			a.EndDate 
		For XML PATH ('')
	),3,1000) as AuditDates") + @"
" + (mostRecentOnly ? "into #tmp" : "") + @"
from
	ENV_Finding f 
	join ENV_Schedule a on f.RelatedAuditId=a.Id
    left join ENV_Poam p on f.PoamId=p.Id
	join CON_Organization o on a.OrganizationId=o.Id
	join COR_Reference at on a.AuditTypeId=at.Id
	left join COR_Reference ft on f.FindingTypeId=ft.Id
    left join COR_Reference fc on f.FindingCategoryId=fc.Id
    left join (
		select
			f.Id as FindingId,
			count(*) as TotalMilestones,
			sum(case when m.DueDate>getdate() then 1 else 0 end) as OnTargetMilestones
		from 
			ENV_Finding f
			join ENV_Poam p on f.PoamId=p.Id
			join ENV_Milestone m on m.PoamId=p.Id
		where
			f.DD is null and p.DD is null and m.DD is null
			and f.FindingStatusEnum<>4 and m.Progress<1
		group by
			f.Id
	) pCounts on pCounts.FindingId=f.Id
where
	a.DD is null and f.DD is null
	and at.Name='Navy External Environmental Audit'
    and f.IsObservation=0
    and (a.OrganizationId = @orgId or o.KeyChain like @key + '.%')
    /**where**/
group by
    " + (mostRecentOnly ? @"a.Id,a.Name,a.EndDate," : "") + @"
	a.OrganizationId,
	o.Name
having
	sum(case when f.FindingStatusEnum in (0,4) then 0 else 1 end)>0
order by
	o.Name
" + (mostRecentOnly ? @"
select * from #tmp where Id=(select top 1 id from #tmp a where a.OrganizationId=#tmp.OrganizationId order by a.EndDate desc)
drop table #tmp
" : "");
            SqlBuilder b = new SqlBuilder();
            var query = b.AddTemplate(sql, new
            {
                orgId = org.Id,
                key = org.Key
            }, existingClauses: new[] { "where" });

            if (ids != null)
                b.Where("a.Id in @ids", new { ids });

            var results = Session.Connection.Query<PWDExternalAuditDashboardModel>(query.RawSql, query.Parameters, Session.GetDbTransaction());

            using (var p = new ExcelPackage())
            {
                ExcelWorksheet ws = p.Workbook.Worksheets.Add("Sheet1");

                string[] captions = { "Installation",
                    "% Finding Closed", "# of Closed Findings", "Total # of Findings",
                    "% Open Milestones on Target", "# of Open Milestones on Target","Total # of Open Milestones",
                    "% Findings Open after 1 Year","# of Findings Open after 1 Year","Total # of Open Findings",
                    "% Open Findings Out Of Control","# of Open Findings Outside of Control","Total # of Open Findings",
                    "% Open Policy Findings","# of Open Policy Findings","Total # of Open Findings",
                    "% Finding Closed","# of Closed Findings","Total # of Findings",
                    "% Open Milestones on Target","# of Open Milestones on Target","Total # of Open Milestones",
                    "% Findings Open after 1 Year", "# of Findings Open after 1 Year","Total # of Open Findings",
                    "Audit Type", "AUDIT DATES" };

                ws.Cells[1, 1].Value = "PWD Dashboard for Navy Environmental Audits";
                ws.Cells[1, 2, 1, 25].Merge = true;
                ws.Cells[1, 2, 1, 25].Value = org;
                ws.Cells[1, 2, 1, 25].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ws.Cells[1, 26].Value = "Report Date";
                ws.Cells[1, 27].Value = DateTime.Now.ToShortDateString();

                ws.Cells[2, 2, 2, 16].Merge = true;
                ws.Cells[2, 2, 2, 16].Value = "Compliance";
                ws.Cells[2, 2, 2, 16].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                ws.Cells[2, 17, 2, 25].Merge = true;
                ws.Cells[2, 17, 2, 25].Value = "Conformance";
                ws.Cells[2, 17, 2, 25].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

                for (int col = 0; col < captions.Length; col++)
                {
                    ws.Cells[3, col + 1].Value = captions[col];
                    if (col > 0 && col < 26)
                    {
                        ws.Cells[3, col + 1].Style.WrapText = true;
                    }
                }
                ws.Row(3).Height = 75;


                int y = 4;
                foreach (var row in results)
                {
                    ws.Cells[y, 1].Value = row.Organization;
                    SetValueAndBackgroundColor(ws.Cells[y, 2], row.CompliancePercentClosed, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 3], row.ComplianceClosedFindings, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 4], row.ComplianceTotalFindings, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 5], row.CompliancePercentOpenMilestonesOnTarget, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 6], row.ComplianceOpenMilestonesOnTarget, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 7], row.ComplianceTotalOpenMilestones, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 8], row.CompliancePercentOpenFindingsAfter1Year, "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 9], row.ComplianceOpenFindingsAfter1Year, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 10], row.ComplianceTotalOpenFindings, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 11], row.CompliancePercentOpenFindingsOutOfControl, "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 12], row.ComplianceOpenFindingsOutOfControl, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 13], row.ComplianceTotalOpenFindings, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 14], row.CompliancePercentOpenPolicyFindings, "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 15], row.ComplianceOpenPolicyFindings, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 16], row.ComplianceTotalOpenFindings, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 17], row.ConformancePercentClosed, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 18], row.ConformanceClosedFindings, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 19], row.ConformanceTotalFindings, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 20], row.ConformancePercentOpenMilestonesOnTarget, "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 21], row.ConformanceOpenMilestonesOnTarget, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 22], row.ConformanceTotalOpenMilestones, "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 23], row.ConformancePercentOpenFindingsAfter1Year, "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 24], row.ConformanceOpenFindingsAfter1Year, "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 25], row.ConformanceTotalOpenFindings, "n0", true);

                    ws.Cells[y, 26].Value = row.AuditNames;
                    ws.Cells[y, 27].Value = row.AuditDates;

                    y++;
                }

                SetBackgroundColor(ws.Cells[3, 1, 3, 27], Color.LightBlue);
                if (results.Count() > 0)
                {
                    SetBackgroundColor(ws.Cells[4, 26, y - 1, 27], Color.LightGray);
                }

                if (results.Count() > 1)
                {
                    SetBackgroundColor(ws.Cells[y, 1, y, 27], Color.LightGray);

                    y++;

                    ws.Cells[y, 1].Value = "Totals";

                    SetValueAndBackgroundColor(ws.Cells[y, 2], results.Sum(x => x.ComplianceTotalFindings) == 0 ? (decimal?)null : (results.Sum(x => x.ComplianceClosedFindings) / results.Sum(x => x.ComplianceTotalFindings)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 3], results.Sum(x => x.ComplianceClosedFindings), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 4], results.Sum(x => x.ComplianceTotalFindings), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 5], results.Sum(x => x.ComplianceTotalOpenMilestones) == 0 ? (decimal?)null : (results.Sum(x => x.ComplianceOpenMilestonesOnTarget) / results.Sum(x => x.ComplianceTotalOpenMilestones)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 6], results.Sum(x => x.ComplianceOpenMilestonesOnTarget), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 7], results.Sum(x => x.ComplianceTotalOpenMilestones), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 8], results.Sum(x => x.ComplianceTotalOpenFindings) == 0 ? (decimal?)null : (results.Sum(x => x.ComplianceOpenFindingsAfter1Year) / results.Sum(x => x.ComplianceTotalOpenFindings)), "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 9], results.Sum(x => x.ComplianceOpenFindingsAfter1Year), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 10], results.Sum(x => x.ComplianceTotalOpenFindings), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 11], results.Sum(x => x.ComplianceTotalOpenFindings) == 0 ? (decimal?)null : (results.Sum(x => x.ComplianceOpenFindingsOutOfControl) / results.Sum(x => x.ComplianceTotalOpenFindings)), "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 12], results.Sum(x => x.ComplianceOpenFindingsOutOfControl), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 13], results.Sum(x => x.ComplianceTotalOpenFindings), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 14], results.Sum(x => x.ComplianceTotalOpenFindings) == 0 ? (decimal?)null : (results.Sum(x => x.ComplianceOpenPolicyFindings) / results.Sum(x => x.ComplianceTotalOpenFindings)), "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 15], results.Sum(x => x.ComplianceOpenPolicyFindings), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 16], results.Sum(x => x.ComplianceTotalOpenFindings), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 17], results.Sum(x => x.ConformanceTotalFindings) == 0 ? (decimal?)null : (results.Sum(x => x.ConformanceClosedFindings) / results.Sum(x => x.ConformanceTotalFindings)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 18], results.Sum(x => x.ConformanceClosedFindings), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 19], results.Sum(x => x.ConformanceTotalFindings), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 20], results.Sum(x => x.ConformanceTotalOpenMilestones) == 0 ? (decimal?)null : (results.Sum(x => x.ConformanceOpenMilestonesOnTarget) / results.Sum(x => x.ConformanceTotalOpenMilestones)), "p0");
                    SetValueAndBackgroundColor(ws.Cells[y, 21], results.Sum(x => x.ConformanceOpenMilestonesOnTarget), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 22], results.Sum(x => x.ConformanceTotalOpenMilestones), "n0", true);

                    SetValueAndBackgroundColor(ws.Cells[y, 23], results.Sum(x => x.ConformanceTotalOpenFindings) == 0 ? (decimal?)null : (results.Sum(x => x.ConformanceOpenFindingsAfter1Year) / results.Sum(x => x.ConformanceTotalOpenFindings)), "p0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 24], results.Sum(x => x.ConformanceOpenFindingsAfter1Year), "n0", true);
                    SetValueAndBackgroundColor(ws.Cells[y, 25], results.Sum(x => x.ConformanceTotalOpenFindings), "n0", true);
                }

                ws.Column(1).AutoFit(20, 50);
                ws.Column(26).AutoFit(20, 50);
                ws.Column(27).AutoFit(20, 50);

                ws.Cells[1, 1, 3, 27].Style.Font.Bold = true;

                ws.Cells[1, 1, y, 27].Style.Border.Left.Style =
                    ws.Cells[1, 1, y, 27].Style.Border.Right.Style =
                    ws.Cells[1, 1, y, 27].Style.Border.Top.Style =
                    ws.Cells[1, 1, y, 27].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                ws.Cells[1, 1, y, 27].Style.Border.BorderAround(ExcelBorderStyle.Thick);

                return p.ToFileResult("PWDExternalAuditDashboard.xlsx");
            }
        }

        public void FinalizeCarryover(Finding f)
        {
            var fdao = new FileDao(Session);
            var fbll = new FileBll(Session);

            var o = f.CarryoverFrom;
            var op = o.Poam;

            f.CorrectiveActions = f.CarryoverFrom.CorrectiveActions;
            f.PreventativeActions = f.CarryoverFrom.PreventativeActions;
            f.CapaOtherComments = f.CarryoverFrom.CapaOtherComments;

            //clone the POAM.
            f.Poam = Session.Save(new Poam(op.Name + " (K " + DateTime.Now.Year + ")", f.Organization)
            {
                Synopsis = op.Synopsis,
                Manager = op.Manager,
                DueDate = op.DueDate,
                AuditorApproved = op.AuditorApproved,
                IsExternal = op.IsExternal,
                Progress = op.Progress,
                Completed = op.Completed
            });
            f.Poam.SetStatus(op.Status, M3Context.Current.User);


            //Copy any attachments from finding
            foreach (var file in fdao.GetForObject(o).Execute(Session))
                fbll.CopyFileToObject(file.File, f);

            //Copy any attachments from poam
            foreach (var file in fdao.GetForObject(op).Execute(Session))
                fbll.CopyFileToObject(file.File, f.Poam);

            foreach (var om in op.Milestones)
            {
                var m = Session.Save(new Milestone(om.Name, f.Poam)
                {
                    Synopsis = om.Synopsis,
                    Poc = om.Poc,
                    DueDate = om.DueDate,
                    Progress = om.Progress,
                    Completed = om.Completed,
                    Name = om.Name,
                    CapaRelationship = om.CapaRelationship
                });
                f.Poam.Milestones.Add(m);

                m.Task = Session.Save(new Task(m.Organization)
                {
                    Organization = m.Organization,
                    Author = M3Context.Current.User,
                    Distribution = Session.Save(new Distribution() { Users = om.Task.Distribution.Users.ToSet() }),
                    RelatedTypeName = om.Task.RelatedTypeName,
                    w_Regarding = om.Task.w_Regarding,
                    DueDate = om.Task.DueDate,
                    Message = om.Task.Message,
                    Notify = om.Task.Notify,
                    CompletedDate = om.Task.CompletedDate,
                    CompletedUser = om.Task.CompletedUser
                });

                //Copy any attachments from milestone
                foreach (var file in fdao.GetForObject(om).Execute(Session))
                    fbll.CopyFileToObject(file.File, m);

                //Copy any notes from milestone
                foreach (var c2o in Session.Query<Communication2Object>().Where(x => x.ClassName == typeof(Milestone).FullName && x.ObjectId == om.Id))
                    Session.Save(new Communication2Object() { Communication = c2o.Communication, RelatedObject = m });
            }

            //Close out the old finding and poam
            foreach (var m in op.Milestones.Where(x => x.Progress < 1))
            {
                m.Progress = 1;
                m.Task.CompletedDate = NiftyDate.Now;
                m.Task.CompletedUser = M3Context.Current.User;
                Session.Save(m);
            }
            op.Synopsis += ". Closed as a carryover finding. New finding: " + f.FindingNumber;
            op.Progress = 1;
            op.Completed = NiftyDate.Now;

            o.Status = FindingStatus.Closed;
            o.Closed = NiftyDate.Now;
            o.ClosedBy = M3Context.Current.User;

            o.History.Add(new FindingHistory(o)
            {
                Date = NiftyDate.Now,
                User = M3Context.Current.User.ToString(),
                Comment = "Closed as a carryover finding. New finding: " + f.FindingNumber
            });

            //If the source Finding's POAM is related to multiple Findings, then the following additional actions need to occur upon finalizing the Carryover Finding:
            //A.The other Findings that are linked to the source Finding's POAM should be unlinked from the source Finding's POAM and,
            //B.Linked to the new Carryover Finding POAM.
            foreach (var otherF in op.Findings.Where(x => x != o).ToList())
            {
                otherF.Poam = f.Poam;
                op.Findings.Remove(otherF);
            }

            f.ClosurePoc = o.ClosurePoc;
            f.HasReview = o.HasReview;
            f.HasCapa = o.HasCapa;
            f.TargetDate = o.TargetDate;
            f.AssignmentComments = o.AssignmentComments;

            f.ResponsibleParty = o.ResponsibleParty;
            f.SetStatus(FindingStatus.Assigned, M3Context.Current.User, "Auto-assigned carryover finding");

            Session.Save(f);
        }

        void SetValueAndBackgroundColor(ExcelRange range, decimal? val, string format, bool shadeNullOnly = false)
        {
            if (val == null)
                range.Value = "NA";
            else
                range.Value = val.Value.ToString(format);

            var style = range.Style;
            style.Fill.PatternType = ExcelFillStyle.Solid;
            if (val == null)
            {
                style.Fill.BackgroundColor.SetColor(Color.LightGray);
            }
            else if (shadeNullOnly)
            {
                style.Fill.PatternType = ExcelFillStyle.None;
            }
            else if (val >= 0.9m)
            {
                style.Fill.BackgroundColor.SetColor(Color.ForestGreen);
                style.Font.Color.SetColor(Color.White);
            }
            else if (val >= 0.75m)
            {
                style.Fill.BackgroundColor.SetColor(Color.Yellow);
            }
            else
            {
                style.Fill.BackgroundColor.SetColor(Color.Red);
                style.Font.Color.SetColor(Color.White);
            }
        }
        void SetBackgroundColor(ExcelRange range, Color color)
        {
            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(color);
        }

        public void Delete(Finding f, bool deleteAssociated = true)
        {
            f.DD = NiftyDate.Now;
            f.DeletedBy = M3Context.Current.User;
            Session.Save(f);

            if (deleteAssociated)
            {
                if (f.Poam != null && f.Poam.Findings.Count == 1)
                {
                    f.Poam.DD = NiftyDate.Now;
                    f.Poam.DeletedBy = M3Context.Current.User;
                    foreach (var m in f.Poam.Milestones)
                    {
                        m.DD = m.Task.DD = NiftyDate.Now;
                        m.DeletedBy = m.Task.DeletedBy = M3Context.Current.User;
                        Session.Save(m);
                        Session.Save(m.Task);
                    }
                    Session.Save(f.Poam);
                }
            }
        }

        class CNRMACapaTrakerReportModel
        {
            public CNRMACapaTrakerReportModel(Finding finding, Milestone milestone, int? milestoneIndex, int? milestoneCount)
            {
                Finding = finding;
                Milestone = milestone;
                MilestoneIndex = milestoneIndex;
                MilestoneCount = milestoneCount;
            }
            public Finding Finding { get; set; }
            public Milestone Milestone { get; set; }
            public int? MilestoneIndex { get; set; }
            public int? MilestoneCount { get; set; }
        }
        public ActionResult CNRMACapaTrakerReport(List<Finding> allFindings)
        {
            List<CNRMACapaTrakerReportModel> dataSource = new List<CNRMACapaTrakerReportModel>();

            foreach (var f in allFindings.Where(x => x.Status != FindingStatus.Draft && x.Status != FindingStatus.Closed))
            {
                var milestones = f.Poam == null ? null : f.Poam.Milestones.Where(m => m.Progress != 1).ToList();

                if (milestones == null || !milestones.Any())
                {
                    dataSource.Add(new CNRMACapaTrakerReportModel(f, null, null, 0));
                }
                else
                {
                    int i = 1;
                    foreach (var m in milestones)
                        dataSource.Add(new CNRMACapaTrakerReportModel(f, m, i++, milestones.Count));
                }
            }

            var def = dataSource.GetExportData(defaultAutoFit: true)
                //Finding fields
                .Column("Finding Number", x => x.Finding.FindingNumber)
                .Column("Subject", x => x.Finding.Subject)
                .Column("Finding Status", x => x.Finding.Status.ToStringDescription())
                .Column("Finding Description", x => x.Finding.Description)
                .Column("Finding Year", x => x.Finding.Observed?.InCurrentZone().Year)
                .Column("Finding Category", x => x.Finding.Category?.Name)
                .Column("Corrective Action Plan", x => x.Finding.Poam?.Synopsis)
                //Milestone fields
                .Column("Number of Open Milestones", x => x.Milestone == null ? null : (x.MilestoneIndex + " of " + x.MilestoneCount))
                .Column("Open Milestone", x => x.Milestone == null ? null : (x.Milestone.Name + ": " + x.Milestone.Synopsis))
                .Column("% Complete", x => x.Milestone?.Progress?.ToString("p"))
                .Column("EV POC / Milestone POC", x => x.Milestone?.Poc)
                .Column("Action POC / Finding POC", x => x.Finding.POC)
                .Column("Date Added", x => x.Milestone?.DC)
                .Column("Target Date for Completion", x => x.Milestone?.Task.DueDate,
                    formatter: (x, c) =>
                    {
                        if (x.Milestone?.Task.DueDate != null && x.Milestone?.Task.DueDate < NiftyDate.Now)
                        {
                            c.Style.Fill.PatternType = ExcelFillStyle.Solid;
                            c.Style.Fill.BackgroundColor.SetColor(Color.Red);
                        }
                    })
                .Column("Actual Date of Completion", x => x.Milestone?.Completed)
                .Column("Comment", x => x.Milestone == null ? null : string.Join("\r\n\r\n",
                    new NoteDao(Session).GetNotesForObject(M3Context.Current.User, x.Milestone)
                        .Execute(Session).OrderByDescending(n => n.DC).Select(n => n.DC.ToShortDateString() + " " + n.ToString()).ToArray()
                ))
                .Column("Date Last Revised", x => x.Milestone?.DM);


            var externalPkg = ExcelBll.GenerateExport(def, dataSource.Where(x => x.Finding.IsExternal));
            var internalPkg = ExcelBll.GenerateExport(def, dataSource.Where(x => !x.Finding.IsExternal));

            externalPkg.Workbook.Worksheets[1].Name = "External";
            externalPkg.Workbook.Worksheets.Add("Internal", internalPkg.Workbook.Worksheets[1]);

            return externalPkg.ToFileResult($"CAPATracker {DateTime.Now.ToString("MM-dd-yyyy")}.xlsx");
        }


        #region Copy To Finding

        public void CopyToFinding(Finding finding, SampleComponent sc)
        {
            sc.Finding = finding;

            finding.AddEmsObject(sc.Sample.EmsObject as NamedAssociationBusinessObject);
            finding.Observed = sc.Sample.SampleDate;
            if (sc.Sample.MonitoringProfile != null)
            {
                finding.Media.Add(sc.Sample.MonitoringProfile.Media);
            }
            Session.Save(sc);
        }

        public void CopyToFinding(Finding finding, Inspection inspection, int? questionId)
        {
            //default some stuff from the inspection
            var question = Session.Get<ChecklistQuestion>(questionId);

            finding.Organization = inspection.Organization;
            finding.ChecklistName = inspection.Name;
            finding.ChecklistQuestion = question;
            finding.ClosurePoc = inspection.Auditor;
            finding.Observed = inspection.Completed;

            if (inspection.Latitude != null && inspection.Longitude != null)
                finding.LatLong = inspection.Latitude + "," + inspection.Longitude;

            finding.AddEmsObject(inspection.EmsObject);

            var emsObj = inspection.EmsObject as IHasLocation;

            if (emsObj != null)
                finding.Area = emsObj.Location + (emsObj.Area == null ? "" : " - " + emsObj.Area);

            if (question != null)
            {
                finding.ChecklistItemId = question.ItemId;

                finding.Requirement = question.Name;

                finding.CitationCode = question.CitationCode;
                finding.Description = inspection.Comments.GetSafe(questionId.GetValueOrDefault());

                if (finding.Poam != null)
                    new PoamDao(Session).Save(finding.Poam);
            }

            if (inspection.AuditItem != null)
                CopyToFinding(finding, inspection.AuditItem);
        }

        public void CopyToFinding(Finding finding, AuditItem auditItem)
        {
            finding.Organization = auditItem.Audit.Organization;

            if (auditItem.AuditTemplate.Poc != null)
                finding.POC = auditItem.AuditTemplate.Poc.Entity.Name;

            finding.Auditor = auditItem.AuditTemplate.Auditor.Users.FirstOrDefault();

            if (auditItem.AuditTemplate.Media != null)
                finding.Media.Add(auditItem.AuditTemplate.Media);

            CopyToFinding(finding, auditItem.Audit);
        }

        public void CopyToFinding(Finding finding, Audit audit)
        {
            finding.Organization = audit.Organization;
            //finding.ChecklistIsExternal = audit.IsExternal;
            finding.IsExternal = audit.IsExternal;
            finding.ClosurePoc = audit.DefaultClosureReviewPoc ?? audit.LeadAuditor;
            finding.AssignedPoc = audit.DefaultAssignedPoc;

            if (audit.IsExternal)
            {
                finding.HasReview = true;
                finding.HasCapa = true;
            }
        }
        #endregion
    }
}