using Dapper;
using Drg.M3.Client;
using Drg.M3.Client.Models.NOV;
using Drg.M3.Dal.Wrappers;
using Drg.M3.Domain;
using System.Collections.Generic;

namespace Drg.M3.Dao.QA
{
    public sealed class NovDao : AbstractDao<PersistentObject>
    {
        public NovDao(IM3Session session) : base(session) { }

        public SqlBuilder.Template BuildQuery(Organization org,
                                              string ActivityName = null,
                                              string NovNumber = null,
                                              string Status = null,
                                              string StatuteViolated = null,
                                              string PoamName = null,
                                              string QuickSearch = null)
        {
            string sql = @"
                select
                    n.TBLNOVID,
                    a.ACT_NAME as ActivityName,
                    n.NOVNUMBER as NovNumber,
                    n.NOVSTATUS as Status,
                    n.NOVSTATUTEVIOLATED as StatuteViolated,
                    p.Id as PoamId,
                    p.Name as PoamName,
                    n.NOVDESC as Description,
                    n.NOVCOMMENT as PlanOfAction
                from 
                    CON_Organization o
                    join nov.dbo.tbl_novs n on n.ACT_UIC=o.NovUIC
                    join nov.dbo.tbl_activity a on a.ACT_UIC=n.ACT_UIC
                    left join ENV_Poam p on p.NOVID=n.TBLNOVID

                    /**where**/
                ";

            SqlBuilder b = new SqlBuilder();

            var query = b.AddTemplate(sql);

            if (M3Context.Current.User.CurrentRecursive)
                b.Where("(o.Id=@orgId OR o.KeyChain like @key+'.%')", new { orgId = org.Id, key = org.Key });
            else
                b.Where("o.Id=@orgId", new { orgId = org.Id });

            if (ActivityName != null)
                b.Where("a.ACT_NAME like '%'+@ActivityName+'%'", new { ActivityName });
            if (NovNumber != null)
                b.Where("n.NOVID like '%'+@NovNumber+'%'", new { NovNumber });
            if (Status != null)
                b.Where("n.NOVSTATUS like '%'+@Status+'%'", new { Status });
            if (StatuteViolated != null)
                b.Where("n.NOVSTATUTEVIOLATED like '%'+@StatuteViolated+'%'", new { StatuteViolated });
            if (PoamName != null)
                b.Where("p.Name like '%'+@PoamName+'%'", new { PoamName });

            if (QuickSearch != null)
                b.Where(@"(
                    a.ACT_NAME like '%'+@QuickSearch+'%' OR 
                    n.NOVID like '%'+@QuickSearch+'%' OR 
                    n.NOVSTATUS like '%'+@QuickSearch+'%' OR 
                    n.NOVSTATUTEVIOLATED like '%'+@QuickSearch+'%' OR 
                    p.Name like '%'+@QuickSearch+'%')", new { QuickSearch });

            return query;
        }

        public IEnumerable<ActivityModel> GetActivities()
        {
            return Session.Connection.Query<ActivityModel>(@"
select distinct 
    n.ACT_UIC as UIC,
	a.ACT_NAME as Name
from 
    nov.dbo.tbl_novs n
    join nov.dbo.tbl_activity a on a.ACT_UIC=n.ACT_UIC
", transaction: Session.GetDbTransaction());
        }

        public IEnumerable<NovModel> GetNovsByUIC(string uic)
        {
            return Session.Connection.Query<NovModel>(@"
select
    a.ACT_NAME as Activity,
	n.NOVNUMBER as NovNumber,
	n.NOVSTATUS as Status,
	n.NOVSTATUTEVIOLATED as StatuteViolated,
	p.Id as PoamId
from 
    nov.dbo.tbl_novs n
    join nov.dbo.tbl_activity a on a.ACT_UIC=n.ACT_UIC
	left join ENV_Poam p on p.NOVID=n.NOVID
where
    n.ACT_UIC = @uic
", new { uic }, transaction: Session.GetDbTransaction());

        }
    }
}
