using System;
using System.Collections.Generic;
using System.Linq;
using Drg.M3.Domain.QA;
using NHibernate.Criterion;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Dao.QA
{
    public sealed class ListBuilderItemDao : AbstractDao<ListBuilderItem>
    {
        public ListBuilderItemDao(IM3Session session) : base(session) { }
        public IDaoResult<ListBuilderItem> GetAll(string quickSearch, string name, string category, string citationCode, string referenceDetails, string itemId, string chapter, string district,
            DateTime? revisionDateFrom, DateTime? revisionDateTo, string section)
        {
            var cr = M3DetachedCriteria.For<ListBuilderItem>();

            if (!string.IsNullOrEmpty(name))
                cr.Add(Restrictions.InsensitiveLike("Name", name, MatchMode.Anywhere));

            if (!string.IsNullOrEmpty(category) && category != "null")
            {
                var or = new Disjunction();
                foreach (string cat in category.Split(','))
                    or.Add(Restrictions.InsensitiveLike("Category", cat, MatchMode.Exact));
                cr.Add(or);
            }

            if (!string.IsNullOrEmpty(citationCode))
                cr.Add(Restrictions.InsensitiveLike("CitationCode", citationCode, MatchMode.Anywhere));

            //if (!string.IsNullOrEmpty(referenceDetails))
            //    cr.Add(Restrictions.InsensitiveLike("ReferenceDetails", referenceDetails, MatchMode.Anywhere));

            if (!string.IsNullOrEmpty(itemId))
                cr.Add(Restrictions.InsensitiveLike("ItemId", itemId, MatchMode.Anywhere));

            if (!string.IsNullOrEmpty(chapter) && chapter != "null")
            {
                var or = new Disjunction();
                foreach (string cat in chapter.Split(','))
                    or.Add(Restrictions.InsensitiveLike("Chapter", cat, MatchMode.Anywhere));
                cr.Add(or);
            }

            if (!string.IsNullOrEmpty(district) && district != "null")
            {
                var or = new Disjunction();
                foreach (string cat in district.Split(','))
                    or.Add(Restrictions.InsensitiveLike("District", cat, MatchMode.Anywhere));
                cr.Add(or);
            }

            if (!string.IsNullOrEmpty(section) && section != "null")
            {
                var or = new Disjunction();
                foreach (string cat in section.Split(','))
                    or.Add(Restrictions.InsensitiveLike("SectionHeading", cat, MatchMode.Anywhere));
                cr.Add(or);
            }

            if (revisionDateFrom != null)
                cr.Add(Restrictions.Ge("RevisionDate", revisionDateFrom));
            if (revisionDateTo != null)
                cr.Add(Restrictions.Le("RevisionDate", revisionDateTo));

            if (!string.IsNullOrEmpty(quickSearch))
            {
                var dj = new Disjunction();
                dj.Add(Restrictions.InsensitiveLike("Name", quickSearch, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("Category", quickSearch, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("CitationCode", quickSearch, MatchMode.Anywhere));
                //dj.Add(Restrictions.InsensitiveLike("ReferenceDetails", quickSearch, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("ItemId", quickSearch, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("Chapter", quickSearch, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("District", quickSearch, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("SectionHeading", quickSearch, MatchMode.Anywhere));
                cr.Add(dj);
            }

            cr.Add(Restrictions.IsNotNull("CitationCode"));
            cr.Add(Restrictions.Not(Restrictions.Eq("CitationCode", "")));

            cr.AddOrder(Order.Asc("ItemIdPrimary"));
            cr.AddOrder(Order.Asc("ItemIdSecondary"));
            cr.AddOrder(Order.Asc("ItemIdTertiary"));

            return cr.ToDaoResult<ListBuilderItem>();
        }

        public IList<string> GetMediaChapters()
        {
            return Session.QueryOver<ListBuilderItem>().Where(w => w.Chapter != null && w.Chapter != " ").Select(Projections.Group<ListBuilderItem>(x => x.Chapter)).List<string>();
        }
        public IList<string> GetJurisdictions()
        {
            return Session.QueryOver<ListBuilderItem>().Where(w => w.Category != null && w.Category != " ").Select(Projections.Group<ListBuilderItem>(x => x.Category)).List<string>();
        }
        public IList<string> GetSections()
        {
            return Session.QueryOver<ListBuilderItem>().Where(w => w.SectionHeading != null && w.SectionHeading != " ").Select(Projections.Group<ListBuilderItem>(x => x.SectionHeading)).List<string>();
        }
        public IList<string> GetDistricts()
        {
            return Session.QueryOver<ListBuilderItem>().Where(w => w.District != null && w.District != " ").Select(Projections.Group<ListBuilderItem>(x => x.District)).List<string>();
        }
        
        public ListBuilderItem GetByItemID(string itemId)
        {
            return Session.Query<ListBuilderItem>().Where(x => x.ItemId == itemId).FirstOrDefault();
        }
    }
}
