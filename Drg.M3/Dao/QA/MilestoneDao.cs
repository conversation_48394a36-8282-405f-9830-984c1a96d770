using System.Collections.Generic;
using System.Linq;
using Drg.M3.Domain;

using NHibernate.Criterion;
using Drg.M3.Domain.QA;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Dao.QA
{
    public sealed class MilestoneDao : AbstractDao<Milestone>
    {
        public MilestoneDao(IM3Session session) : base(session) { }

        public override Milestone Save(Milestone entity)
        {
            //always save the poam as well
            Session.Save(entity.Poam);
            return base.Save(entity);
        }
        public IDaoResult<Milestone> GetAll(User user, int? progressMin, int? progressMax, int? daysUntilDue)
        {
            return GetAll(new[] { user }, progressMin, progressMax, daysUntilDue);
        }
        public IDaoResult<Milestone> GetAll(IEnumerable<User> users, int? progressMin, int? progressMax, int? daysUntilDue)
        {
            return GetAll(users, progressMin, progressMax, daysUntilDue, null, null, null, null, null, null, null, null);
        }
        public IDaoResult<Milestone> GetAll(User user, int? progressMin, int? progressMax, int? daysUntilDue, string search, string milestoneName, string poamName,
            string poc, NiftyDate dueDateFrom, NiftyDate dueDateTo, int? poamStatus, string poamManager)
        {
            return GetAll(new[] { user }, progressMin, progressMax, daysUntilDue, search, milestoneName, poamName, poc, dueDateFrom, dueDateTo, poamStatus, poamManager);
        }
        public IDaoResult<Milestone> GetAll(IEnumerable<User> users, int? progressMin, int? progressMax, int? daysUntilDue, string search, string milestoneName, string poamName,
            string poc, NiftyDate dueDateFrom, NiftyDate dueDateTo, int? poamStatus, string poamManager)
        {
            var cr = Session.CreateCriteria<Milestone>();
            cr.CreateAlias("Poam", "Poam");
            cr.CreateAlias("Poc", "Poc", NHibernate.SqlCommand.JoinType.LeftOuterJoin);

            cr.Add(Restrictions.Or(
                Restrictions.In("Poam.Manager", users.ToArray()),
                Restrictions.In("Poc.Id", users.Select(x => x.Id).ToArray())
            ));

            if (progressMin != null)
                cr.Add(Restrictions.Ge("Progress", (progressMin.Value * 0.01m)));
            if (progressMax != null)
                cr.Add(Restrictions.Le("Progress", (progressMax.Value * 0.01m)));

            if (daysUntilDue != null)
                cr.Add(Restrictions.Le("DueDate", NiftyDate.Now.AddDays(daysUntilDue.Value)));

            if (!string.IsNullOrEmpty(search))
            {
                cr.CreateAlias("Poam.Manager", "Manager", NHibernate.SqlCommand.JoinType.LeftOuterJoin);
                var dj = new Disjunction();
                dj.Add(Restrictions.InsensitiveLike("Name", search, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("Poam.Name", search, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("Poc.w_EntityName", search, MatchMode.Anywhere));
                dj.Add(Restrictions.InsensitiveLike("Manager.w_EntityName", search, MatchMode.Anywhere));
                cr.Add(dj);
            }

            if (!string.IsNullOrEmpty(milestoneName))
                cr.Add(Restrictions.InsensitiveLike("Name", milestoneName, MatchMode.Anywhere));

            if (!string.IsNullOrEmpty(poamName))
                cr.Add(Restrictions.InsensitiveLike("Poam.Name", poamName, MatchMode.Anywhere));

            if (!string.IsNullOrEmpty(poc))
                cr.Add(Restrictions.InsensitiveLike("Poc.Name", poc, MatchMode.Anywhere));

            if (dueDateFrom != null)
                cr.Add(Restrictions.Ge("DueDate", dueDateFrom));
            if (dueDateTo != null)
                cr.Add(Restrictions.Le("DueDate", dueDateTo));

            if (poamStatus != null)
                cr.Add(Restrictions.Eq("Poam.Status", (PoamStatus)poamStatus.Value));

            if (!string.IsNullOrEmpty(poamManager))
            {
                cr.CreateAlias("Poam.Manager", "manager2");
                cr.Add(Restrictions.InsensitiveLike("manager2.w_EntityName", poamManager, MatchMode.Anywhere));
            }

            return cr.ToDaoResult<Milestone>();
        }

        public Milestone GetByTask(Task t)
        {
            var cr = Session.CreateCriteria<Milestone>();

            cr.Add(Restrictions.Eq("Task", t));

            return cr.UniqueResult<Milestone>();
        }
    }
}
