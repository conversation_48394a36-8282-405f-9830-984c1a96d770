using System.Collections.Generic;

using Drg.M3.Domain;
using NHibernate.Criterion;
using Drg.M3.Domain.Compliance;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Dao
{
    public sealed class MonitoredSubstanceDao : AbstractDao<MonitoredSubstance>
    {
        public MonitoredSubstanceDao(IM3Session session) : base(session) { }

        public IList<MonitoredSubstance> GetAll(Organization org, bool recursive, bool shared)
        {
            var cr = Session.CreateCriteria<MonitoredSubstance>();

            var cb = new GenericCriteriaBuilder(cr);

            cr.Add(cb.Org(org, recursive, false, shared));
            //when viewing shared, it should only be at other organizations
            if (shared)
            {
                cr.Add(Restrictions.Not(Restrictions.Eq("Organization", org)));
            }

            cr.Add(cb.NotDeleted());

            return cr.List<MonitoredSubstance>();
        }
    }
}
