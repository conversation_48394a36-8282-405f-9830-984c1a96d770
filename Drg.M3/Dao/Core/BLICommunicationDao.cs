using Drg.M3.Domain;
using NHibernate.Criterion;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Dao
{
    public class BLICommunicationDao : AbstractDao<BLICommunication>
    {
        public BLICommunicationDao(IM3Session session) : base(session) { }

        public IDaoResult<BLICommunication> GetForEntity(Entity entity, int? numRows, string search, bool? isRead, bool? isDraft, bool? isImportant, bool? hasAttachments, NiftyDate fromDate, NiftyDate toDate)
        {
            var cr = M3DetachedCriteria.For<BLICommunication>();
            var cb = new GenericCriteriaBuilder(cr);

            // find user in distribution
            var cr2 = cr.CreateCriteria("Distribution", "d");
            cr2.CreateAlias("Entities", "e", NHibernate.SqlCommand.JoinType.LeftOuterJoin);
            cr2.CreateAlias("Organizations", "o", NHibernate.SqlCommand.JoinType.LeftOuterJoin);
            cr2.CreateAlias("Users", "u", NHibernate.SqlCommand.JoinType.LeftOuterJoin);

            var dj = new Disjunction();
            dj.Add(Restrictions.Eq("e.Id", entity.Id));
            dj.Add(Restrictions.Eq("o.Id", entity.Organization.Id));
            if (entity.User != null)
                dj.Add(Restrictions.Eq("u.Id", entity.User.Id));
            cr2.Add(dj);

            if (!string.IsNullOrWhiteSpace(search))
            {
                cr.CreateAlias("Author", "a");
                cr.CreateAlias("a.Entity", "From");

                var or = new Disjunction();
                or.Add(Restrictions.InsensitiveLike("Subject", search, MatchMode.Anywhere));
                or.Add(Restrictions.InsensitiveLike("Message", search, MatchMode.Anywhere));
                or.Add(Restrictions.InsensitiveLike("From.Name", search, MatchMode.Anywhere));
                or.Add(Restrictions.InsensitiveLike("From.SortName", search, MatchMode.Anywhere));

                cr.Add(or);
            }

            if (isDraft.HasValue)
                cr.Add(Restrictions.Eq("IsDraft", isDraft));
            if (isImportant.HasValue)
                cr.Add(Restrictions.Eq("Important", isImportant));
            if (hasAttachments.HasValue)
                cr.Add(Restrictions.Eq("HasAttachments", hasAttachments));

            if (fromDate != null)
                cr.Add(Restrictions.Ge("DC", fromDate));
            if (toDate != null)
                cr.Add(Restrictions.Le("DC", toDate));

            if (numRows.HasValue)
                cr.SetMaxResults(numRows.Value);

            return cr.ToDaoResult<BLICommunication>();
        }

        public IDaoResult<BLICommunication> GetByAuthorUser(User user, int? numRows, bool? drafts, bool? nullDistribution, string search, bool? isImportant, bool? hasAttachments, NiftyDate fromDate, NiftyDate toDate)
        {
            var cr = M3DetachedCriteria.For<BLICommunication>();

            cr.Add(Restrictions.Eq("Author", user));

            if (!string.IsNullOrWhiteSpace(search))
            {
                var cr2 = cr.CreateCriteria("Distribution", "d");

                var or = new Disjunction();
                or.Add(Restrictions.InsensitiveLike("Subject", search, MatchMode.Anywhere));
                or.Add(Restrictions.InsensitiveLike("Message", search, MatchMode.Anywhere));
                or.Add(Restrictions.InsensitiveLike("d.w", search, MatchMode.Anywhere));

                cr.Add(or);
            }

            if (isImportant != null)
                cr.Add(Restrictions.Eq("Important", isImportant));

            if (hasAttachments != null)
                cr.Add(Restrictions.Eq("HasAttachments", hasAttachments));

            if (fromDate != null)
                cr.Add(Restrictions.Ge("DC", fromDate));
            if (toDate != null)
                cr.Add(Restrictions.Le("DC", toDate));

            if (drafts.HasValue)
                cr.Add(Restrictions.Eq("IsDraft", drafts));

            if (nullDistribution == true)
                cr.Add(Restrictions.IsNull("Distribution"));
            else if (nullDistribution == false)
                cr.Add(Restrictions.IsNotNull("Distribution"));

            if (numRows.HasValue)
                cr.SetMaxResults(numRows.Value);

            return cr.ToDaoResult<BLICommunication>();
        }
    }
}
