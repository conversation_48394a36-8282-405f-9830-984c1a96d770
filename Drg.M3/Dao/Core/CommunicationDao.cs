using System;
using System.Collections.Generic;
using System.Linq;
using Drg.M3.Domain;
using NHibernate.Criterion;
using Drg.Core;
using System.ComponentModel;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Dao
{
    public enum SearchMethod
    {
        [Description("Exact phrase")]
        ContainsExact = 1,
        [Description("All words")]
        AllWords = 2,
        [Description("Any words")]
        AnyWords = 3
    }

    public sealed class CommunicationDao : AbstractDao<Communication>
    {
        public CommunicationDao(IM3Session session) : base(session) { }
        public IDaoResult<Communication> Search(Organization org, bool recursive, string searchText, SearchMethod searchMethod, string type,
            Type relatedType, string relatedName, NiftyDate date1, NiftyDate date2,
            bool searchSubject, bool searchBody, /*bool searchAttachments,*/ bool includeNotes, bool includeTasks, /*bool includeEmails, bool includeNotifications,*/
            User author)
        {
            var cr = M3DetachedCriteria.For<Communication>();
            AddOrg(cr, org, recursive);

            if (!string.IsNullOrEmpty(searchText))
            {
                Junction term = null;
                switch (searchMethod)
                {
                    case SearchMethod.ContainsExact:
                        {
                            term = new Disjunction();
                            if (searchSubject) term.Add(Restrictions.Like("Subject", searchText, MatchMode.Anywhere));
                            if (searchBody) term.Add(Restrictions.Like("Message", searchText, MatchMode.Anywhere));
                        }
                        break;
                    case SearchMethod.AnyWords:
                        {
                            term = new Disjunction();
                            var words = searchText.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                            foreach (var word in words)
                            {
                                if (searchSubject) term.Add(Restrictions.Like("Subject", searchText, MatchMode.Anywhere));
                                if (searchBody) term.Add(Restrictions.Like("Message", searchText, MatchMode.Anywhere));
                            }
                        }
                        break;
                    case SearchMethod.AllWords:
                        {
                            term = new Conjunction();
                            var words = searchText.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                            foreach (var word in words)
                            {
                                var dj = new Disjunction();
                                if (searchSubject) dj.Add(Restrictions.Like("Subject", searchText, MatchMode.Anywhere));
                                if (searchBody) dj.Add(Restrictions.Like("Message", searchText, MatchMode.Anywhere));
                                term.Add(dj);
                            }
                        }
                        break;
                }
                //TODO: implement searching on attachments
                //if (searchAttachments) dj.Add(Restrictions.Like("Subject", searchText, MatchMode.Anywhere));

                cr.Add(term);
            }

            if (!string.IsNullOrEmpty(type))
                cr.Add(Restrictions.Like("w_Type", type, MatchMode.Anywhere));

            if (date1 != null)
                cr.Add(Restrictions.Ge("DC", date1));

            if (date2 != null)
                cr.Add(Restrictions.Le("DC", date2));

            {
                var dj = new Disjunction();
                if (includeNotes) dj.Add(Restrictions.Eq("w_CommunicationType", "Note"));
                if (includeTasks) dj.Add(Restrictions.Eq("w_CommunicationType", "Task"));
                //if (includeEmails) dj.Add(Restrictions.Eq("w_CommunicationType", "Message"));
                //if (includeNotifications) dj.Add(Restrictions.Eq("w_CommunicationType", "Notification"));
                cr.Add(dj);
            }

            if (author != null)
                cr.Add(Restrictions.Eq("Author", author));

            if (relatedType != null)
            {
                cr.Join("Communication2Objects", "c2o");
                cr.Add(Restrictions.Eq("c2o.ClassName", relatedType.FullName));
                cr.SetResultTransformer(CriteriaSpecification.DistinctRootEntity);

                if (!string.IsNullOrEmpty(relatedName))
                {
                    var dcRelated = M3DetachedCriteria.For(relatedType);
                    dcRelated.Add(Restrictions.Like("Name", relatedName, MatchMode.Anywhere));
                    dcRelated.SetProjection(Projections.Distinct(Projections.Property("Id")));
                    cr.Add(Subqueries.PropertyIn("c2o.ObjectId", dcRelated));
                }
            }

            return cr.ToDaoResult<Communication>();
        }

        public override Communication Save(Communication comm)
        {
            List<string> regarding = new List<string>();

            if (comm.Communication2Objects.Count > 0)
            {
                foreach (var obj in comm.Communication2Objects.Select(x => x.RelatedObject))
                    regarding.Add(obj.ToString());
                comm.w_Regarding = string.Join(", ", regarding.ToArray());
            }

            if (comm is Task)
            {
                var task = comm as Task;
                comm.w_Type = task.TaskType == null ? null : task.TaskType.Name;
                comm.w_CommunicationType = "Task";
            }
            else if (comm is Note)
            {
                var note = comm as Note;
                comm.w_Type = note.NoteType == null ? null : note.NoteType.Name;
                if (comm.Author == null)
                    comm.w_CommunicationType = "Notification";
                else if (regarding.Count == 0)
                    comm.w_CommunicationType = "Message";
                else
                    comm.w_CommunicationType = "Note";
            }

            return base.Save(comm);
        }

        public IList<Communication> GetCommunicationsForObject(PersistentObject obj)
        {
            return GetCommunicationsForObject(obj, -1);
        }

        public IList<Communication> GetCommunicationsForObject(PersistentObject obj, int maxResults)
        {
            //IQuery q = Session.CreateQuery("select c2o.Communication from Communication2Object c2o where c2o.ObjectId=? and c2o.ClassName=?")
            //        .SetParameters(obj.Id, obj.GetType().FullName);

            var qo = Session.QueryOver<Communication>();

            qo.JoinQueryOver<Communication2Object>(c => c.Communication2Objects)
                .Where(co => co.ObjectId == obj.Id && co.ClassName == obj.GetType().FullName);

            if (maxResults > 0)
                qo.Take(maxResults);

            return qo.List();
        }

        public IList<Communication> GetAllByRecipient(User user)
        {
            return GetAllByRecipient(user, -1);
        }

        public IList<Communication> GetAllByRecipient(User user, int maxResults)
        {
            var cr = Session.CreateCriteria(typeof(Communication));

            var cr2 = cr.CreateCriteria("Distribution", "d");
            cr2.CreateAlias("Users", "u", NHibernate.SqlCommand.JoinType.LeftOuterJoin);
            cr2.CreateAlias("Organizations", "o", NHibernate.SqlCommand.JoinType.LeftOuterJoin);
            cr2.CreateAlias("UserGroups", "ug", NHibernate.SqlCommand.JoinType.LeftOuterJoin);

            var dj = new Disjunction();
            dj.Add(Restrictions.Eq("u.Id", user.Id));
            dj.Add(Restrictions.Eq("o.Id", user.Organization.Id));
            dj.Add(Restrictions.In("ug.Id", user.UserGroups.ToList().ConvertAll(ug => ug.Id)));
            cr2.Add(dj);

            cr.Add(Restrictions.Eq("IsDraft", false));

            if (maxResults > 0)
                cr.SetMaxResults(maxResults);

            return cr.List<Communication>();
        }

        public IList<Communication> GetAllByAuthor(User u)
        {
            return GetAllByAuthor(u, -1);
        }

        public IList<Communication> GetAllByAuthor(User u, int maxResults)
        {
            var cr = Session.CreateCriteria<Communication>();
            cr.Add(Restrictions.Eq("Author", u));

            if (maxResults > 0)
                cr.SetMaxResults(maxResults);

            return cr.List<Communication>();
        }
    }
}