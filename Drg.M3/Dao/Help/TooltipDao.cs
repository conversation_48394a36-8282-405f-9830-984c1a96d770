using System.Collections.Generic;
using NHibernate;
using NHibernate.Criterion;
using Drg.M3.Domain;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Dao
{
    public sealed class TooltipDao : AbstractDao<Tooltip>
    {
        public TooltipDao(IM3Session session) : base(session) { }

        public IList<Tooltip> GetForElements(string[] htmlElements)
        {
            ICriteria c = Session.CreateCriteria<Tooltip>();
            c.Add(Restrictions.In("HtmlElementId", htmlElements));
            c.Add(Restrictions.IsNull("DD"));

            return c.List<Tooltip>();
        }
        public Tooltip GetForElement(string htmlElement)
        {
            ICriteria c = Session.CreateCriteria<Tooltip>();
            c.Add(Restrictions.Eq("HtmlElementId", htmlElement));
            c.Add(Restrictions.IsNull("DD"));
            c.SetMaxResults(1);

            return c.UniqueR<PERSON>ult<Tooltip>();
        }
    }
}
