using System;
using System.Collections.Generic;
using Drg.M3.Dal;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.Impl;
using NHibernate.SqlCommand;
using NHibernate.Transform;
using System.Linq;

namespace Drg.M3.Dao
{
    public static class Extensions
    {
        public static IQueryOver<TRoot, TSubType> Or<TRoot, TSubType>(this IQueryOver<TRoot, TSubType> input, params ICriterion[] criteria)
        {
            if (criteria.Length == 0)
                return input;
            else if (criteria.Length == 1)
                return input.Where(criteria[0]);
            else
            {
                var or = Restrictions.Or(criteria[0], criteria[1]);
                for (int i = 2; i < criteria.Length; i++)
                    or = Restrictions.Or(or, criteria[i]);

                return input.Where(or);
            }
        }
        public static IQuery SetParameters(this IQuery input, params object[] parameters)
        {
            for (int i = 0; i < parameters.Length; i++)
                input.SetParameter(i, parameters[i]);
            return input;
        }
        public static string Join(this ICriteria input, string associationPath)
        {
            return Join(input, associationPath, JoinType.LeftOuterJoin);
        }
        public static string Join(this ICriteria input, string associationPath, JoinType joinType)
        {
            return Join(input, associationPath, joinType, null);
        }
        public static string Join(this ICriteria input, string associationPath, JoinType joinType, ICriterion withClause)
        {
            //ensure the parent join
            if (associationPath.IndexOf(".") > -1)
                associationPath = Join(input, associationPath.Substring(0, associationPath.LastIndexOf("."))) + "." + associationPath.Substring(associationPath.LastIndexOf(".") + 1);

            if (input is CriteriaImpl.Subcriteria)
            {
                var root = input.GetPrivate<CriteriaImpl>("root");
                return Join(root, associationPath, joinType);
            }
            else if (input is CriteriaImpl)
            {
                var paths = input.GetPrivate<Dictionary<string, ICriteria>>("subcriteriaByPath");
                var aliases = input.GetPrivate<Dictionary<string, ICriteria>>("subcriteriaByAlias");

                if (paths.ContainsKey(associationPath))
                {
                    return paths[associationPath].Alias;
                }
                else if (!string.IsNullOrWhiteSpace(input.Alias) && paths.ContainsKey(input.Alias + "." + associationPath))
                {
                    return paths[input.Alias + "." + associationPath].Alias;
                }
                else if (associationPath.IndexOf(".") > -1 
                    && paths.ContainsKey(associationPath.Substring(associationPath.IndexOf(".") + 1))
                    && paths[associationPath.Substring(associationPath.IndexOf(".") + 1)] is NHibernate.Impl.CriteriaImpl.Subcriteria
                    && (paths[associationPath.Substring(associationPath.IndexOf(".") + 1)] as NHibernate.Impl.CriteriaImpl.Subcriteria).Parent.Alias == associationPath.Substring(0, associationPath.IndexOf(".")))
                {
                    return paths[associationPath.Substring(associationPath.IndexOf(".") + 1)].Alias;
                }

                string alias = "alias" + aliases.Count + "_";

                var cr = input.CreateAlias(associationPath, alias, joinType, withClause);

                return alias;
            }
            else
            {
                throw new ArgumentException("input must be an instance of CriteriaImpl");
            }
        }

        public static ICriteria Join(this ICriteria input, string associationPath, string alias)
        {
            return Join(input, associationPath, alias, JoinType.LeftOuterJoin);
        }
        public static ICriteria Join(this ICriteria input, string associationPath, string alias, JoinType joinType)
        {
            //ensure the parent join
            if (associationPath.IndexOf(".") > -1)
                Join(input, associationPath.Substring(0, associationPath.LastIndexOf(".")));

            if (input is CriteriaImpl.Subcriteria)
            {
                var root = input.GetPrivate<CriteriaImpl>("root");
                return Join(root, associationPath, alias, joinType);
            }
            else if (input is CriteriaImpl)
            {
                var paths = input.GetPrivate<Dictionary<string, ICriteria>>("subcriteriaByPath");
                var aliases = input.GetPrivate<Dictionary<string, ICriteria>>("subcriteriaByAlias");

                if (paths.ContainsKey(associationPath) && !aliases.ContainsKey(alias))
                    throw new ArgumentException("That associationPath already exists with a different alias");
                
                if (aliases.ContainsKey(alias) && !paths.ContainsKey(associationPath))
                    throw new ArgumentException("That alias already exists with a different associationPath");

                if (aliases.ContainsKey(alias) && paths.ContainsKey(associationPath))
                    return aliases[alias];

                var cr = input.CreateAlias(associationPath,alias,joinType);

                return cr;
            }
            else
            {
                throw new ArgumentException("input must be an instance of CriteriaImpl");
            }
        }

        //public static string GetSqlAlias(this CriteriaImpl input, string alias)
        //{
        //    if (alias == "this") return "this_";

        //    var subcriteriaList = input.GetType().GetField("subcriteriaList", BindingFlags.NonPublic | BindingFlags.Instance).GetValue(input) as List<NHibernate.Impl.CriteriaImpl.Subcriteria>;
        //    var matchingSubcriteria = subcriteriaList.Where(cr2 =>
        //        cr2.Alias.Substring(0, Math.Min(10, cr2.Alias.Length)).Equals(
        //            alias.Substring(0, Math.Min(10, alias.Length)),
        //                StringComparison.CurrentCultureIgnoreCase));

        //    var dict = new Dictionary<string, NHibernate.Impl.CriteriaImpl.Subcriteria>(StringComparer.CurrentCultureIgnoreCase);
        //    //var dict2 = new Dictionary<ICriteria, string>();

        //    foreach (var cr in matchingSubcriteria)
        //    {
        //        string sqlAlias = cr.Alias.Substring(0, Math.Min(10, cr.Alias.Length));
        //        int i = 1;
        //        while (dict.ContainsKey(sqlAlias + i + "_")) i++;
        //        sqlAlias = sqlAlias + i + "_";
        //        dict.Add(sqlAlias, cr);
        //        //dict2.Add(cr, sqlAlias);

        //        if (cr.Alias == alias)
        //            return sqlAlias;
        //    }

        //    throw new ArgumentException("Alias not found");
        //}
        //public static string GetSqlAlias(this CriteriaImpl.Subcriteria input)
        //{
        //    var root = input.GetType().GetField("root", BindingFlags.NonPublic | BindingFlags.Instance).GetValue(input) as CriteriaImpl;
        //    return GetSqlAlias(root, input.Alias);
        //}
        //public static string GetSqlAlias(this ICriteria input)
        //{
        //    if (input is CriteriaImpl)
        //    {
        //        return GetSqlAlias(input as CriteriaImpl);
        //    }
        //    else if (input is CriteriaImpl.Subcriteria)
        //    {
        //        return GetSqlAlias(input as CriteriaImpl.Subcriteria);
        //    }
        //    else
        //    {
        //        throw new ArgumentException("input must be an instance of CriteriaImpl or CriteriaImpl.Subcriteria");
        //    }
        //}

        public static string Join(this DetachedCriteria input, string associationPath)
        {
            return Join(input.GetInternalCriteria(), associationPath);
        }
        public static string Join(this DetachedCriteria input, string associationPath, JoinType joinType)
        {
            return Join(input.GetInternalCriteria(), associationPath, joinType);
        }
        public static string Join(this DetachedCriteria input, string associationPath, JoinType joinType, ICriterion withClause)
        {
            return Join(input.GetInternalCriteria(), associationPath, joinType, withClause);
        }

        public static ICriteria Join(this DetachedCriteria input, string associationPath, string alias)
        {
            return Join(input.GetInternalCriteria(), associationPath, alias);
        }

        public static ICriteria Join(this DetachedCriteria input, string associationPath, string alias, JoinType joinType)
        {
            return Join(input.GetInternalCriteria(), associationPath, alias, joinType);
        }

        public static ICriteria GetInternalCriteria(this DetachedCriteria input)
        {
            return input.GetPrivate<ICriteria>("criteria");
        }

        public static IDaoResult<T> ToDaoResult<T>(this DetachedCriteria input)
            //where T : PersistentObject
        {
            return DaoResult<T>.Create<T>(input);
        }
        public static IDaoResult<T> ToDaoResult<T>(this ICriteria input)
            //where T : PersistentObject
        {
            return DaoResult<T>.Create<T>(input.Detach());
        }
        public static IDaoResult<T> ToDaoResult<T>(this QueryOver<T> input)
            //where T : PersistentObject
        {
            return ToDaoResult<T>(input.DetachedCriteria);
        }
        public static IProjection GetProjection(this DetachedCriteria input)
        {
            var impl = input.GetPrivate<CriteriaImpl>("impl");
            return impl.GetPrivate<IProjection>("projection");
        }

        public static ICriteria HintRecompile(this ICriteria cr)
        {
            cr.SetComment(AuditInterceptor.QueryHintRecompileCommentString);
            return cr;
        }
        public static DetachedCriteria HintRecompile(this DetachedCriteria cr)
        {
            cr.GetInternalCriteria().SetComment(AuditInterceptor.QueryHintRecompileCommentString);
            return cr;
        }
        public static ICriteria HintOptimizeForUnknown(this ICriteria cr)
        {
            cr.SetComment(AuditInterceptor.QueryHintOptimizeForUnknownCommentString);
            return cr;
        }
        public static DetachedCriteria HintOptimizeForUnknown(this DetachedCriteria cr)
        {
            cr.GetInternalCriteria().SetComment(AuditInterceptor.QueryHintOptimizeForUnknownCommentString);
            return cr;
        }

        public static IResultTransformer GetResultTransformer(this DetachedCriteria input)
        {
            var impl = input.GetPrivate<CriteriaImpl>("impl");
            return impl.GetPrivate<IResultTransformer>("resultTransformer");
        }
    }
}
