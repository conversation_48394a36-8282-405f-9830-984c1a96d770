using Drg.M3.Dal;
using NHibernate;
using NHibernate.Criterion;
using NHibernate.Impl;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;

namespace Drg.M3.Dao
{
    public class M3QueryOver
    {
        public static M3QueryOver<T, T> Of<T>()
        {
            return new M3QueryOver<T, T>(QueryOver.Of<T>());
        }

        public static M3QueryOver<T, T> Of<T>(Expression<Func<T>> alias)
        {
            return new M3QueryOver<T, T>(QueryOver.Of<T>(alias));
        }

        public static M3QueryOver<T, T> Of<T>(string entityName)
        {
            return new M3QueryOver<T, T>(QueryOver.Of<T>(entityName));
        }

        public static M3QueryOver<T, T> Of<T>(string entityName, Expression<Func<T>> alias)
        {
            return new M3QueryOver<T, T>(QueryOver.Of<T>(entityName, alias));
        }
    }
    public class M3QueryOver<TRoot, TSubType> : QueryOver<TRoot, TSubType>
    {
        public M3QueryOver(QueryOver<TRoot, TSubType> queryOver)
            : base(queryOver.GetPrivate<CriteriaImpl>("impl"), queryOver.GetPrivate<ICriteria>("criteria"))
        {
        }

        public new M3QueryOver<TRoot, TSubType> And(ICriterion expression)
        {
            base.And(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> And(System.Linq.Expressions.Expression<Func<bool>> expression)
        {
            base.And(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> And(System.Linq.Expressions.Expression<Func<TSubType, bool>> expression)
        {
            base.And(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> AndNot(System.Linq.Expressions.Expression<Func<bool>> expression)
        {
            base.And(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> AndNot(System.Linq.Expressions.Expression<Func<TSubType, bool>> expression)
        {
            base.AndNot(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias<U>(System.Linq.Expressions.Expression<Func<IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinAlias<U>(path, alias, joinType, withClause);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias<U>(System.Linq.Expressions.Expression<Func<U>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinAlias<U>(path, alias, joinType, withClause);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias(System.Linq.Expressions.Expression<Func<object>> path, System.Linq.Expressions.Expression<Func<object>> alias, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinAlias(path, alias, joinType);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias<U>(System.Linq.Expressions.Expression<Func<TSubType, IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinAlias<U>(path, alias, joinType, withClause);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias<U>(System.Linq.Expressions.Expression<Func<TSubType, U>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinAlias<U>(path, alias, joinType, withClause);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias(System.Linq.Expressions.Expression<Func<TSubType, object>> path, System.Linq.Expressions.Expression<Func<object>> alias, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinAlias(path, alias, joinType);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias(System.Linq.Expressions.Expression<Func<object>> path, System.Linq.Expressions.Expression<Func<object>> alias)
        {
            var qo = base.JoinAlias(path, alias);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> JoinAlias(System.Linq.Expressions.Expression<Func<TSubType, object>> path, System.Linq.Expressions.Expression<Func<object>> alias)
        {
            var qo = base.JoinAlias(path, alias);
            return this;
        }

        /// <summary>
        /// Creates up to 6 unique aliases for unaliased joins.
        /// </summary>
        /// <typeparam name="U"></typeparam>
        /// <returns></returns>
        private Expression<Func<U>> createAlias<U>()
        {
            if (base.DetachedCriteria.GetCriteriaByAlias("_sillias1") == null)
            {
                U _sillias1 = default(U);
                return () => _sillias1;
            }
            else if (base.DetachedCriteria.GetCriteriaByAlias("_sillias2") == null)
            {
                U _sillias2 = default(U);
                return () => _sillias2;
            }
            else if (base.DetachedCriteria.GetCriteriaByAlias("_sillias3") == null)
            {
                U _sillias3 = default(U);
                return () => _sillias3;
            }
            else if (base.DetachedCriteria.GetCriteriaByAlias("_sillias4") == null)
            {
                U _sillias4 = default(U);
                return () => _sillias4;
            }
            else if (base.DetachedCriteria.GetCriteriaByAlias("_sillias5") == null)
            {
                U _sillias5 = default(U);
                return () => _sillias5;
            }
            else if (base.DetachedCriteria.GetCriteriaByAlias("_sillias6") == null)
            {
                U _sillias6 = default(U);
                return () => _sillias6;
            }
            throw new ArgumentException("Too many unnamed joins. Try aliasing some of your joins.");
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType, withClause);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType, withClause);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<IEnumerable<U>>> path, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>(), joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, IEnumerable<U>>> path, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>(), joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias)
        {
            var qo = base.JoinQueryOver<U>(path, alias);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, IEnumerable<U>>> path, System.Linq.Expressions.Expression<Func<U>> alias)
        {
            var qo = base.JoinQueryOver<U>(path, alias);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<IEnumerable<U>>> path)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>());
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, IEnumerable<U>>> path)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>());
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<U>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType, withClause);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<U>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, U>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType, ICriterion withClause)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType, withClause);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, U>> path, System.Linq.Expressions.Expression<Func<U>> alias, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, alias, joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<U>> path, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>(), joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, U>> path, NHibernate.SqlCommand.JoinType joinType)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>(), joinType);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<U>> path, System.Linq.Expressions.Expression<Func<U>> alias)
        {
            var qo = base.JoinQueryOver<U>(path, alias);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, U>> path, System.Linq.Expressions.Expression<Func<U>> alias)
        {
            var qo = base.JoinQueryOver<U>(path, alias);
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<U>> path)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>());
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, U> JoinQueryOver<U>(System.Linq.Expressions.Expression<Func<TSubType, U>> path)
        {
            var qo = base.JoinQueryOver<U>(path, createAlias<U>());
            return new M3QueryOver<TRoot, U>(qo);
        }

        public new M3QueryOver<TRoot, TSubType> Select(params IProjection[] projections)
        {
            base.Select(projections);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> Select(params System.Linq.Expressions.Expression<Func<TRoot, object>>[] projections)
        {
            base.Select(projections);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> SelectList(Func<NHibernate.Criterion.Lambda.QueryOverProjectionBuilder<TRoot>, NHibernate.Criterion.Lambda.QueryOverProjectionBuilder<TRoot>> list)
        {
            base.SelectList(list);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> TransformUsing(NHibernate.Transform.IResultTransformer resultTransformer)
        {
            base.TransformUsing(resultTransformer);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> Where(ICriterion expression)
        {
            base.Where(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> Where(System.Linq.Expressions.Expression<Func<bool>> expression)
        {
            base.Where(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> Where(System.Linq.Expressions.Expression<Func<TSubType, bool>> expression)
        {
            base.Where(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> WhereNot(System.Linq.Expressions.Expression<Func<bool>> expression)
        {
            base.WhereNot(expression);
            return this;
        }

        public new M3QueryOver<TRoot, TSubType> WhereNot(System.Linq.Expressions.Expression<Func<TSubType, bool>> expression)
        {
            base.WhereNot(expression);
            return this;
        }

        public new QueryOver<TRoot> CacheMode(CacheMode cacheMode)
        {
            base.CacheMode(cacheMode);
            return this;
        }

        public new QueryOver<TRoot> CacheRegion(string cacheRegion)
        {
            base.CacheRegion(cacheRegion);
            return this;
        }

        public new QueryOver<TRoot> Cacheable()
        {
            base.Cacheable();
            return this;
        }

        public new QueryOver<TRoot> ClearOrders()
        {
            base.ClearOrders();
            return this;
        }

        public new M3QueryOver<TRoot, TRoot> Clone()
        {
            return new M3QueryOver<TRoot, TRoot>(base.Clone());
        }

        public new IQueryOver<TRoot, TRoot> GetExecutableQueryOver(ISession session)
        {
            return base.GetExecutableQueryOver(session);
        }

        public new IQueryOver<TRoot, TRoot> GetExecutableQueryOver(IStatelessSession session)
        {
            return base.GetExecutableQueryOver(session);
        }

        public new QueryOver<TRoot> Skip(int firstResult)
        {
            base.Skip(firstResult);
            return this;
        }

        public new QueryOver<TRoot> Take(int maxResults)
        {
            base.Take(maxResults);
            return this;
        }

        public new M3QueryOver<TRoot, TRoot> ToRowCountInt64Query()
        {
            return new M3QueryOver<TRoot, TRoot>(
                base.ToRowCountInt64Query());
        }

        public new M3QueryOver<TRoot, TRoot> ToRowCountQuery()
        {
            return new M3QueryOver<TRoot, TRoot>(
                base.ToRowCountQuery());
        }
    }

    public static class M3QueryOverExtensions
    {
        public static QueryOver<TRoot, TSubType> Detach<TRoot, TSubType>(this IQueryOver<TRoot, TSubType> queryOver)
        {
            var impl = (CriteriaImpl)queryOver.RootCriteria;
            impl.Session = null;
            return (QueryOver<TRoot,TSubType>)queryOver;
        }

        public static QueryOver<TRoot, TSubType> HintRecompile<TRoot, TSubType>(this QueryOver<TRoot, TSubType> qo)
        {
            qo.RootCriteria.SetComment(AuditInterceptor.QueryHintRecompileCommentString);
            return qo;
        }
    }
}
