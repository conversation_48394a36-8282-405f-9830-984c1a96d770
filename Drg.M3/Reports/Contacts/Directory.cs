using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using Drg.M3.Bll.Reports;
using Drg.M3.Domain;
using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Controls;
using GrapeCity.ActiveReports.SectionReportModel;
using GrapeCity.ActiveReports.Document.Section;

namespace Drg.M3.Reports.Contacts
{
    /// <summary>
    /// Summary description for Directory.
    /// </summary>
    [GridReport("Directory", null, typeof(Entity), typeof(Modules.Core))]
    public partial class Directory : GrapeCity.ActiveReports.SectionReport
    {

        public Directory()
        {
            //
            // Required for Windows Form Designer support
            //
            InitializeComponent();
        }
    }
}
