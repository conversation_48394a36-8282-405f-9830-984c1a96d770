namespace Drg.M3.Reports.QA
{
    /// <summary>
    /// Summary description for InternalAuditFindingOutbrief.
    /// </summary>
    partial class InternalAuditReport
    {


        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(InternalAuditReport));
            GrapeCity.ActiveReports.Chart.ChartArea chartArea1 = new GrapeCity.ActiveReports.Chart.ChartArea();
            GrapeCity.ActiveReports.Chart.Axis axis1 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis2 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis3 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis4 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis5 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Series series1 = new GrapeCity.ActiveReports.Chart.Series();
            GrapeCity.ActiveReports.Chart.Title title1 = new GrapeCity.ActiveReports.Chart.Title();
            GrapeCity.ActiveReports.Chart.Title title2 = new GrapeCity.ActiveReports.Chart.Title();
            GrapeCity.ActiveReports.Chart.ChartArea chartArea2 = new GrapeCity.ActiveReports.Chart.ChartArea();
            GrapeCity.ActiveReports.Chart.Axis axis6 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis7 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis8 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis9 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Axis axis10 = new GrapeCity.ActiveReports.Chart.Axis();
            GrapeCity.ActiveReports.Chart.Legend legend1 = new GrapeCity.ActiveReports.Chart.Legend();
            GrapeCity.ActiveReports.Chart.Title title3 = new GrapeCity.ActiveReports.Chart.Title();
            GrapeCity.ActiveReports.Chart.Title title4 = new GrapeCity.ActiveReports.Chart.Title();
            GrapeCity.ActiveReports.Chart.Title title5 = new GrapeCity.ActiveReports.Chart.Title();
            GrapeCity.ActiveReports.Chart.Title title6 = new GrapeCity.ActiveReports.Chart.Title();
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.sr_41_alt = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.label2 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label3 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label18 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label27 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label28 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label30 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line29 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line2 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line21 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label19 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label131 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label132 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label133 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label134 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label135 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label136 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label4 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label5 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label6 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label7 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label8 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label9 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label10 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label11 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label12 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label13 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label14 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label15 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label16 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label17 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label20 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label22 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label23 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chartControl3 = new GrapeCity.ActiveReports.SectionReportModel.ChartControl();
            this.label24 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label25 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label26 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lbl_41 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label_4_1_Normal = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.subComplianceMediaFindings = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak1 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.pageBreak2 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label33 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label34 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label35 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label36 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.pageBreak3 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label37 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label38 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.pageBreak4 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label39 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label40 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.pageBreak6 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label41 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.subComplianceChecklists = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak11 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label130 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.subReport5 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak7 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label45 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.subReport3 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak9 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label46 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.subReport1 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak10 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label42 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.subReport2 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak12 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label47 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.shape4 = new GrapeCity.ActiveReports.SectionReportModel.Shape();
            this.label49 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label50 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line27 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line30 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.pageBreak14 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.label48 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label51 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label52 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label53 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label54 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label55 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label56 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label57 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label58 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label59 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label60 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label61 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label62 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label63 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label64 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label65 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label66 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label67 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label68 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label69 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label70 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label71 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label72 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label73 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label74 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label75 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label76 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label77 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label78 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label79 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label80 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label81 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line44 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line28 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line31 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line32 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line33 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line45 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line34 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line35 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line36 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line37 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line38 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line39 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line40 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line41 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line42 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line43 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.shape1 = new GrapeCity.ActiveReports.SectionReportModel.Shape();
            this.label43 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label44 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line3 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line4 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line5 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label82 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label83 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label84 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label85 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line6 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label86 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label87 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line7 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label88 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label89 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line8 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label90 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label91 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line9 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label92 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label93 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line10 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label94 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label95 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line11 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label96 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label97 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line12 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label98 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label99 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line13 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label100 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label101 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line14 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label102 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label103 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line15 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label104 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label105 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line16 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label106 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label107 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line17 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label108 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label109 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line18 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.pageBreak16 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.shape2 = new GrapeCity.ActiveReports.SectionReportModel.Shape();
            this.label110 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label111 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line19 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line20 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label112 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label113 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line22 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label114 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label115 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line23 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label116 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label117 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line24 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label118 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label119 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line25 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label120 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label121 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line26 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label122 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label123 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line46 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label124 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label125 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line47 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label126 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label127 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line48 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label128 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label129 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line49 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label137 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label138 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line50 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label139 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label140 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line51 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.chart_internal_audit_compliance_findings = new GrapeCity.ActiveReports.SectionReportModel.ChartControl();
            this.pageBreak13 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.subReport4 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.subReport6 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.label141 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label142 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.pageBreak15 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.pageBreak17 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.pageBreak18 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.subReport7 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.pageBreak19 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.pageBreak20 = new GrapeCity.ActiveReports.SectionReportModel.PageBreak();
            this.textBox1 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox2 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.customControl1 = new GrapeCity.ActiveReports.SectionReportModel.CustomControl();
            this.reportHeader1 = new GrapeCity.ActiveReports.SectionReportModel.ReportHeader();
            this.label1 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line1 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.reportFooter1 = new GrapeCity.ActiveReports.SectionReportModel.ReportFooter();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label28)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label131)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label132)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label133)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label134)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label135)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label136)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lbl_41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label_4_1_Normal)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label33)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label34)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label35)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label36)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label37)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label38)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label39)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label40)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label130)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label45)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label46)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label42)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label47)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label49)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label50)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label48)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label51)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label52)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label53)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label54)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label55)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label56)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label57)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label58)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label59)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label60)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label61)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label62)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label63)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label64)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label65)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label66)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label67)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label68)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label69)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label70)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label71)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label72)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label73)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label74)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label75)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label76)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label77)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label78)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label79)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label80)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label81)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label43)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label44)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label82)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label83)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label84)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label85)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label86)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label87)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label88)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label89)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label90)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label91)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label92)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label93)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label94)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label95)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label96)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label97)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label98)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label99)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label100)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label101)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label102)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label103)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label104)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label105)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label106)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label107)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label108)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label109)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label110)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label111)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label112)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label113)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label114)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label115)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label116)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label117)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label118)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label119)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label120)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label121)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label122)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label123)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label124)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label125)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label126)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label127)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label128)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label129)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label137)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label138)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label139)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label140)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chart_internal_audit_compliance_findings)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label141)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label142)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.customControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.CanShrink = true;
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.sr_41_alt,
            this.label2,
            this.label3,
            this.label18,
            this.label27,
            this.label28,
            this.label30,
            this.line29,
            this.line2,
            this.line21,
            this.label19,
            this.label131,
            this.label132,
            this.label133,
            this.label134,
            this.label135,
            this.label136,
            this.label4,
            this.label5,
            this.label6,
            this.label7,
            this.label8,
            this.label9,
            this.label10,
            this.label11,
            this.label12,
            this.label13,
            this.label14,
            this.label15,
            this.label16,
            this.label17,
            this.label20,
            this.label22,
            this.label23,
            this.chartControl3,
            this.label24,
            this.label25,
            this.label26,
            this.lbl_41,
            this.label_4_1_Normal,
            this.subComplianceMediaFindings,
            this.pageBreak1,
            this.pageBreak2,
            this.label33,
            this.label34,
            this.label35,
            this.label36,
            this.pageBreak3,
            this.label37,
            this.label38,
            this.pageBreak4,
            this.label39,
            this.label40,
            this.pageBreak6,
            this.label41,
            this.subComplianceChecklists,
            this.pageBreak11,
            this.label130,
            this.subReport5,
            this.pageBreak7,
            this.label45,
            this.subReport3,
            this.pageBreak9,
            this.label46,
            this.subReport1,
            this.pageBreak10,
            this.label42,
            this.subReport2,
            this.pageBreak12,
            this.label47,
            this.shape4,
            this.label49,
            this.label50,
            this.line27,
            this.line30,
            this.pageBreak14,
            this.label48,
            this.label51,
            this.label52,
            this.label53,
            this.label54,
            this.label55,
            this.label56,
            this.label57,
            this.label58,
            this.label59,
            this.label60,
            this.label61,
            this.label62,
            this.label63,
            this.label64,
            this.label65,
            this.label66,
            this.label67,
            this.label68,
            this.label69,
            this.label70,
            this.label71,
            this.label72,
            this.label73,
            this.label74,
            this.label75,
            this.label76,
            this.label77,
            this.label78,
            this.label79,
            this.label80,
            this.label81,
            this.line44,
            this.line28,
            this.line31,
            this.line32,
            this.line33,
            this.line45,
            this.line34,
            this.line35,
            this.line36,
            this.line37,
            this.line38,
            this.line39,
            this.line40,
            this.line41,
            this.line42,
            this.line43,
            this.shape1,
            this.label43,
            this.label44,
            this.line3,
            this.line4,
            this.line5,
            this.label82,
            this.label83,
            this.label84,
            this.label85,
            this.line6,
            this.label86,
            this.label87,
            this.line7,
            this.label88,
            this.label89,
            this.line8,
            this.label90,
            this.label91,
            this.line9,
            this.label92,
            this.label93,
            this.line10,
            this.label94,
            this.label95,
            this.line11,
            this.label96,
            this.label97,
            this.line12,
            this.label98,
            this.label99,
            this.line13,
            this.label100,
            this.label101,
            this.line14,
            this.label102,
            this.label103,
            this.line15,
            this.label104,
            this.label105,
            this.line16,
            this.label106,
            this.label107,
            this.line17,
            this.label108,
            this.label109,
            this.line18,
            this.pageBreak16,
            this.shape2,
            this.label110,
            this.label111,
            this.line19,
            this.line20,
            this.label112,
            this.label113,
            this.line22,
            this.label114,
            this.label115,
            this.line23,
            this.label116,
            this.label117,
            this.line24,
            this.label118,
            this.label119,
            this.line25,
            this.label120,
            this.label121,
            this.line26,
            this.label122,
            this.label123,
            this.line46,
            this.label124,
            this.label125,
            this.line47,
            this.label126,
            this.label127,
            this.line48,
            this.label128,
            this.label129,
            this.line49,
            this.label137,
            this.label138,
            this.line50,
            this.label139,
            this.label140,
            this.line51,
            this.chart_internal_audit_compliance_findings,
            this.pageBreak13,
            this.subReport4,
            this.subReport6,
            this.label141,
            this.label142,
            this.pageBreak15,
            this.pageBreak17,
            this.pageBreak18,
            this.subReport7,
            this.pageBreak19,
            this.pageBreak20,
            this.textBox1,
            this.textBox2,
            this.customControl1});
            this.detail.Height = 89.30093F;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.detail_Format);
            // 
            // sr_41_alt
            // 
            this.sr_41_alt.CloseBorder = false;
            this.sr_41_alt.Height = 6.585999F;
            this.sr_41_alt.Left = 0F;
            this.sr_41_alt.Name = "sr_41_alt";
            this.sr_41_alt.Report = null;
            this.sr_41_alt.ReportName = "subReport8";
            this.sr_41_alt.Top = 39.306F;
            this.sr_41_alt.Visible = false;
            this.sr_41_alt.Width = 7.710001F;
            // 
            // label2
            // 
            this.label2.Height = 0.2F;
            this.label2.HyperLink = null;
            this.label2.Left = 0F;
            this.label2.Name = "label2";
            this.label2.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label2.Text = "Executive Summary";
            this.label2.Top = 0.04999998F;
            this.label2.Width = 1.906F;
            // 
            // label3
            // 
            this.label3.Height = 1.003F;
            this.label3.HyperLink = null;
            this.label3.Left = 0F;
            this.label3.Name = "label3";
            this.label3.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label3.Text = resources.GetString("label3.Text");
            this.label3.Top = 0.362F;
            this.label3.Width = 7.47F;
            // 
            // label18
            // 
            this.label18.Height = 1.001998F;
            this.label18.HyperLink = null;
            this.label18.Left = 0.003F;
            this.label18.Name = "label18";
            this.label18.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label18.Text = resources.GetString("label18.Text");
            this.label18.Top = 12.68F;
            this.label18.Width = 7.47F;
            // 
            // label27
            // 
            this.label27.Height = 0.2F;
            this.label27.HyperLink = null;
            this.label27.Left = 4.897593E-07F;
            this.label27.Name = "label27";
            this.label27.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label27.Text = "2.0 Audit Scope and Methodology";
            this.label27.Top = 9.952001F;
            this.label27.Width = 2.864F;
            // 
            // label28
            // 
            this.label28.Height = 0.4199994F;
            this.label28.HyperLink = null;
            this.label28.Left = 4.897593E-07F;
            this.label28.Name = "label28";
            this.label28.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label28.Text = "Audit activities were conducted in accordance with ISO 14001:2004, ISO 19001:2002" +
    " and OPNAV M-5090.1.  Procedures for both the EMS audit and compliance assessmen" +
    "t included:";
            this.label28.Top = 10.212F;
            this.label28.Width = 7.47F;
            // 
            // label30
            // 
            this.label30.Height = 0.8045F;
            this.label30.HyperLink = null;
            this.label30.Left = 0.00300049F;
            this.label30.Name = "label30";
            this.label30.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label30.Text = resources.GetString("label30.Text");
            this.label30.Top = 13.732F;
            this.label30.Width = 7.47F;
            // 
            // line29
            // 
            this.line29.Height = 7.322006F;
            this.line29.Left = 0F;
            this.line29.LineWeight = 1F;
            this.line29.Name = "line29";
            this.line29.Top = 68.26099F;
            this.line29.Width = 0.001000404F;
            this.line29.X1 = 0.001000404F;
            this.line29.X2 = 0F;
            this.line29.Y1 = 68.26099F;
            this.line29.Y2 = 75.58299F;
            // 
            // line2
            // 
            this.line2.Height = 8.281013F;
            this.line2.Left = 0F;
            this.line2.LineWeight = 1F;
            this.line2.Name = "line2";
            this.line2.Top = 75.533F;
            this.line2.Width = 0.0199996F;
            this.line2.X1 = 0F;
            this.line2.X2 = 0.0199996F;
            this.line2.Y1 = 75.533F;
            this.line2.Y2 = 83.81401F;
            // 
            // line21
            // 
            this.line21.Height = 5.029999F;
            this.line21.Left = 0.001F;
            this.line21.LineWeight = 1F;
            this.line21.Name = "line21";
            this.line21.Top = 84.26399F;
            this.line21.Width = 0F;
            this.line21.X1 = 0.001F;
            this.line21.X2 = 0.001F;
            this.line21.Y1 = 84.26399F;
            this.line21.Y2 = 89.29399F;
            // 
            // label19
            // 
            this.label19.Height = 0.6075001F;
            this.label19.HyperLink = null;
            this.label19.Left = 0F;
            this.label19.Name = "label19";
            this.label19.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label19.Text = resources.GetString("label19.Text");
            this.label19.Top = 1.4275F;
            this.label19.Width = 7.47F;
            // 
            // label131
            // 
            this.label131.Height = 0.9615002F;
            this.label131.HyperLink = null;
            this.label131.Left = 0F;
            this.label131.Name = "label131";
            this.label131.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label131.Text = resources.GetString("label131.Text");
            this.label131.Top = 2.0975F;
            this.label131.Width = 7.47F;
            // 
            // label132
            // 
            this.label132.Height = 0.2F;
            this.label132.HyperLink = null;
            this.label132.Left = 1.965F;
            this.label132.Name = "label132";
            this.label132.Style = "color: Green; font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label132.Text = "(Lead Auditor customize this section)";
            this.label132.Top = 5.362F;
            this.label132.Width = 4.416F;
            // 
            // label133
            // 
            this.label133.Height = 0.2F;
            this.label133.HyperLink = null;
            this.label133.Left = 0F;
            this.label133.Name = "label133";
            this.label133.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label133.Text = "1.0 Introduction";
            this.label133.Top = 5.362F;
            this.label133.Width = 1.906F;
            // 
            // label134
            // 
            this.label134.Height = 1.421F;
            this.label134.HyperLink = null;
            this.label134.Left = 0F;
            this.label134.Name = "label134";
            this.label134.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label134.Text = resources.GetString("label134.Text");
            this.label134.Top = 5.632001F;
            this.label134.Width = 7.47F;
            // 
            // label135
            // 
            this.label135.Height = 1.368F;
            this.label135.HyperLink = null;
            this.label135.Left = 0F;
            this.label135.Name = "label135";
            this.label135.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label135.Text = resources.GetString("label135.Text");
            this.label135.Top = 7.053F;
            this.label135.Width = 7.47F;
            // 
            // label136
            // 
            this.label136.Height = 1.3685F;
            this.label136.HyperLink = null;
            this.label136.Left = 0F;
            this.label136.Name = "label136";
            this.label136.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label136.Text = resources.GetString("label136.Text");
            this.label136.Top = 8.493502F;
            this.label136.Width = 7.47F;
            // 
            // label4
            // 
            this.label4.Height = 1.003001F;
            this.label4.HyperLink = null;
            this.label4.Left = 0.502F;
            this.label4.Name = "label4";
            this.label4.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label4.Text = resources.GetString("label4.Text");
            this.label4.Top = 14.597F;
            this.label4.Width = 6.97F;
            // 
            // label5
            // 
            this.label5.Height = 0.7844996F;
            this.label5.HyperLink = null;
            this.label5.Left = 0.502F;
            this.label5.Name = "label5";
            this.label5.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label5.Text = resources.GetString("label5.Text");
            this.label5.Top = 15.6625F;
            this.label5.Width = 6.97F;
            // 
            // label6
            // 
            this.label6.Height = 0.4094993F;
            this.label6.HyperLink = null;
            this.label6.Left = 0.502F;
            this.label6.Name = "label6";
            this.label6.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label6.Text = "Regulatory compliance deficiency:  A failure to comply with federal, state or loc" +
    "al environmental regulation or law. For overseas installations, this includes ap" +
    "plicable FGS and OEBGD.";
            this.label6.Top = 16.5095F;
            this.label6.Width = 6.97F;
            // 
            // label7
            // 
            this.label7.Height = 0.4094993F;
            this.label7.HyperLink = null;
            this.label7.Left = 0.502F;
            this.label7.Name = "label7";
            this.label7.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label7.Text = "Policy compliance deficiency:  A failure to comply with DoD, Navy, regional, or i" +
    "nstallation environmental policy.";
            this.label7.Top = 16.9815F;
            this.label7.Width = 6.97F;
            // 
            // label8
            // 
            this.label8.Height = 0.6075011F;
            this.label8.HyperLink = null;
            this.label8.Left = 0.502F;
            this.label8.Name = "label8";
            this.label8.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label8.Text = resources.GetString("label8.Text");
            this.label8.Top = 17.4535F;
            this.label8.Width = 6.97F;
            // 
            // label9
            // 
            this.label9.Height = 0.6075011F;
            this.label9.HyperLink = null;
            this.label9.Left = 0.502F;
            this.label9.Name = "label9";
            this.label9.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label9.Text = resources.GetString("label9.Text");
            this.label9.Top = 18.1235F;
            this.label9.Width = 6.97F;
            // 
            // label10
            // 
            this.label10.Height = 0.4204989F;
            this.label10.HyperLink = null;
            this.label10.Left = 0.502F;
            this.label10.Name = "label10";
            this.label10.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label10.Text = "Noteworthy practice:  An EMS or compliance management strength recognized by the " +
    "audit team that has the potential to be implemented at other locations.";
            this.label10.Top = 18.7935F;
            this.label10.Width = 6.97F;
            // 
            // label11
            // 
            this.label11.Height = 0.201499F;
            this.label11.HyperLink = null;
            this.label11.Left = 0.502F;
            this.label11.Name = "label11";
            this.label11.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label11.Text = "Positive observation:  An EMS or compliance management strength recognized by the" +
    " audit team.";
            this.label11.Top = 19.2765F;
            this.label11.Width = 6.97F;
            // 
            // label12
            // 
            this.label12.Height = 0.4094992F;
            this.label12.HyperLink = null;
            this.label12.Left = 0.502F;
            this.label12.Name = "label12";
            this.label12.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label12.Text = "Tier 1 Audit:  Inspections and recordkeeping required to be conducted by regulati" +
    "on or internal procedures/management plans.  These inspections are typically con" +
    "ducted by process owners.  ";
            this.label12.Top = 19.5405F;
            this.label12.Width = 6.97F;
            // 
            // label13
            // 
            this.label13.Height = 0.4094992F;
            this.label13.HyperLink = null;
            this.label13.Left = 0.502F;
            this.label13.Name = "label13";
            this.label13.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label13.Text = "Tier 2 Audit:  Environmental oversight inspections of the practice owners.  These" +
    " inspections/audits are conducted to ensure that the Tier 1 requirements are bei" +
    "ng met.";
            this.label13.Top = 20.0125F;
            this.label13.Width = 6.97F;
            // 
            // label14
            // 
            this.label14.Height = 0.4094992F;
            this.label14.HyperLink = null;
            this.label14.Left = 0.502F;
            this.label14.Name = "label14";
            this.label14.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label14.Text = "Tier 3 Audit:  Comprehensive evaluation of compliance with all environmental requ" +
    "irements for each environmental media.  ";
            this.label14.Top = 20.4845F;
            this.label14.Width = 6.97F;
            // 
            // label15
            // 
            this.label15.Height = 0.6169981F;
            this.label15.HyperLink = null;
            this.label15.Left = 0.023F;
            this.label15.Name = "label15";
            this.label15.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label15.Text = resources.GetString("label15.Text");
            this.label15.Top = 21.087F;
            this.label15.Width = 7.47F;
            // 
            // label16
            // 
            this.label16.Height = 0.2F;
            this.label16.HyperLink = null;
            this.label16.Left = 0.023F;
            this.label16.Name = "label16";
            this.label16.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label16.Text = "3.0 EMS Background";
            this.label16.Top = 21.847F;
            this.label16.Width = 2.334F;
            // 
            // label17
            // 
            this.label17.Height = 1.950002F;
            this.label17.HyperLink = null;
            this.label17.Left = 0.023F;
            this.label17.Name = "label17";
            this.label17.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label17.Text = resources.GetString("label17.Text");
            this.label17.Top = 22.1095F;
            this.label17.Width = 7.47F;
            // 
            // label20
            // 
            this.label20.Height = 0.2099991F;
            this.label20.HyperLink = null;
            this.label20.Left = 0.023F;
            this.label20.Name = "label20";
            this.label20.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label20.Text = "The following legal requirements or drivers were followed during the External Env" +
    "ironmental Audit:\r\n";
            this.label20.Top = 24.13F;
            this.label20.Width = 7.47F;
            // 
            // label22
            // 
            this.label22.Height = 0.2F;
            this.label22.HyperLink = null;
            this.label22.Left = 0.023F;
            this.label22.Name = "label22";
            this.label22.Style = "font-size: 12pt; font-style: italic; font-weight: bold; ddo-char-set: 1";
            this.label22.Text = "3.1 Environmental Management System";
            this.label22.Top = 25.8305F;
            this.label22.Width = 4.094F;
            // 
            // label23
            // 
            this.label23.Height = 1.731498F;
            this.label23.HyperLink = null;
            this.label23.Left = 0.023F;
            this.label23.Name = "label23";
            this.label23.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label23.Text = resources.GetString("label23.Text");
            this.label23.Top = 26.093F;
            this.label23.Width = 7.47F;
            // 
            // chartControl3
            // 
            this.chartControl3.AutoRefresh = true;
            this.chartControl3.Backdrop = new GrapeCity.ActiveReports.Chart.BackdropItem(GrapeCity.ActiveReports.Chart.Graphics.GradientType.Vertical, System.Drawing.Color.White, System.Drawing.Color.SteelBlue);
            chartArea1.AntiAliasMode = GrapeCity.ActiveReports.Chart.Graphics.AntiAliasMode.Graphics;
            axis1.AxisType = GrapeCity.ActiveReports.Chart.AxisType.Categorical;
            axis1.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis1.LabelsAtBottom = true;
            axis1.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 1D, 0F, false);
            axis1.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis1.StaggerLabels = true;
            axis1.Title = "ISO Element";
            axis1.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis2.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis2.LabelsGap = 0;
            axis2.LabelsVisible = false;
            axis2.Line = new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None);
            axis2.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis2.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis2.Position = 0D;
            axis2.TickOffset = 0D;
            axis2.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis2.Visible = false;
            axis3.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis3.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 1D, 0F, false);
            axis3.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis3.Position = 0D;
            axis3.SmartLabels = false;
            axis3.Title = "# of Findings";
            axis3.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F), -90F);
            axis4.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis4.LabelsVisible = false;
            axis4.Line = new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None);
            axis4.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis4.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis4.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis4.Visible = false;
            axis5.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis5.LabelsGap = 0;
            axis5.LabelsVisible = false;
            axis5.Line = new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None);
            axis5.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis5.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis5.Position = 0D;
            axis5.TickOffset = 0D;
            axis5.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis5.Visible = false;
            chartArea1.Axes.AddRange(new GrapeCity.ActiveReports.Chart.AxisBase[] {
            axis1,
            axis2,
            axis3,
            axis4,
            axis5});
            chartArea1.Backdrop = new GrapeCity.ActiveReports.Chart.BackdropItem(GrapeCity.ActiveReports.Chart.Graphics.BackdropStyle.Transparent, System.Drawing.Color.White, System.Drawing.Color.White, GrapeCity.ActiveReports.Chart.Graphics.GradientType.Vertical, System.Drawing.Drawing2D.HatchStyle.DottedGrid, null, GrapeCity.ActiveReports.Chart.Graphics.PicturePutStyle.Stretched);
            chartArea1.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            chartArea1.Light = new GrapeCity.ActiveReports.Chart.Light(new GrapeCity.ActiveReports.Chart.Graphics.Point3d(10F, 40F, 20F), GrapeCity.ActiveReports.Chart.LightType.InfiniteDirectional, 0.3F);
            chartArea1.Name = "defaultArea";
            chartArea1.Projection = new GrapeCity.ActiveReports.Chart.Projection(GrapeCity.ActiveReports.Chart.Graphics.ProjectionType.Orthogonal, 0.1F, 0.1F);
            this.chartControl3.ChartAreas.AddRange(new GrapeCity.ActiveReports.Chart.ChartArea[] {
            chartArea1});
            this.chartControl3.ChartBorder = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            this.chartControl3.ColorPalette = GrapeCity.ActiveReports.Chart.ColorPalette.Iceberg;
            this.chartControl3.Height = 6.802001F;
            this.chartControl3.Left = 0.023F;
            this.chartControl3.Name = "chartControl3";
            series1.AxisX = axis1;
            series1.AxisY = axis3;
            series1.ChartArea = chartArea1;
            series1.Legend = null;
            series1.LegendText = "";
            series1.Name = "Findings";
            series1.Properties = new GrapeCity.ActiveReports.Chart.CustomProperties(new GrapeCity.ActiveReports.Chart.KeyValuePair[] {
            new GrapeCity.ActiveReports.Chart.KeyValuePair("BarType", GrapeCity.ActiveReports.Chart.BarType.Bar),
            new GrapeCity.ActiveReports.Chart.KeyValuePair("Marker", new GrapeCity.ActiveReports.Chart.Marker())});
            series1.Type = GrapeCity.ActiveReports.Chart.ChartType.Bar3D;
            series1.ValueMembersY = null;
            series1.ValueMemberX = null;
            this.chartControl3.Series.AddRange(new GrapeCity.ActiveReports.Chart.Series[] {
            series1});
            title1.Alignment = GrapeCity.ActiveReports.Chart.Alignment.Center;
            title1.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            title1.DockArea = null;
            title1.Font = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 14F));
            title1.Name = "header";
            title1.Text = "Findings by ISO 14001 Element";
            title2.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            title2.DockArea = null;
            title2.Docking = GrapeCity.ActiveReports.Chart.DockType.Bottom;
            title2.Font = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            title2.Name = "footer";
            title2.Text = "";
            this.chartControl3.Titles.AddRange(new GrapeCity.ActiveReports.Chart.Title[] {
            title1,
            title2});
            this.chartControl3.Top = 27.9275F;
            this.chartControl3.UIOptions = GrapeCity.ActiveReports.Chart.UIOptions.ForceHitTesting;
            this.chartControl3.Width = 7.687F;
            // 
            // label24
            // 
            this.label24.Height = 0.2F;
            this.label24.HyperLink = null;
            this.label24.Left = 0.021F;
            this.label24.Name = "label24";
            this.label24.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label24.Text = "4.0 Environmental Compliance Background";
            this.label24.Top = 37.664F;
            this.label24.Width = 4.834F;
            // 
            // label25
            // 
            this.label25.Height = 0.6274978F;
            this.label25.HyperLink = null;
            this.label25.Left = 0.021F;
            this.label25.Name = "label25";
            this.label25.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label25.Text = resources.GetString("label25.Text");
            this.label25.Top = 37.9265F;
            this.label25.Width = 7.47F;
            // 
            // label26
            // 
            this.label26.Height = 0.6274978F;
            this.label26.HyperLink = null;
            this.label26.Left = 0.021F;
            this.label26.Name = "label26";
            this.label26.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label26.Text = resources.GetString("label26.Text");
            this.label26.Top = 38.6165F;
            this.label26.Width = 7.47F;
            // 
            // lbl_41
            // 
            this.lbl_41.Height = 0.2F;
            this.lbl_41.HyperLink = null;
            this.lbl_41.Left = 0.021F;
            this.lbl_41.Name = "lbl_41";
            this.lbl_41.Style = "font-size: 12pt; font-style: italic; font-weight: bold; ddo-char-set: 1";
            this.lbl_41.Text = "4.1 Environmental Compliance Assessment";
            this.lbl_41.Top = 39.384F;
            this.lbl_41.Width = 3.563F;
            // 
            // label_4_1_Normal
            // 
            this.label_4_1_Normal.Height = 1.0335F;
            this.label_4_1_Normal.HyperLink = null;
            this.label_4_1_Normal.Left = 0.021F;
            this.label_4_1_Normal.Name = "label_4_1_Normal";
            this.label_4_1_Normal.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label_4_1_Normal.Text = resources.GetString("label_4_1_Normal.Text");
            this.label_4_1_Normal.Top = 39.637F;
            this.label_4_1_Normal.Width = 7.47F;
            // 
            // subComplianceMediaFindings
            // 
            this.subComplianceMediaFindings.CloseBorder = false;
            this.subComplianceMediaFindings.Height = 1F;
            this.subComplianceMediaFindings.Left = 0.021F;
            this.subComplianceMediaFindings.Name = "subComplianceMediaFindings";
            this.subComplianceMediaFindings.Report = null;
            this.subComplianceMediaFindings.ReportName = "subReport1";
            this.subComplianceMediaFindings.Top = 46.08F;
            this.subComplianceMediaFindings.Width = 7.689F;
            // 
            // pageBreak1
            // 
            this.pageBreak1.Height = 0.01F;
            this.pageBreak1.Left = 0F;
            this.pageBreak1.Name = "pageBreak1";
            this.pageBreak1.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak1.Top = 45.892F;
            this.pageBreak1.Width = 6.5F;
            // 
            // pageBreak2
            // 
            this.pageBreak2.Height = 0.01F;
            this.pageBreak2.Left = 0F;
            this.pageBreak2.Name = "pageBreak2";
            this.pageBreak2.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak2.Top = 47.215F;
            this.pageBreak2.Width = 6.5F;
            // 
            // label33
            // 
            this.label33.Height = 0.2F;
            this.label33.HyperLink = null;
            this.label33.Left = 0F;
            this.label33.Name = "label33";
            this.label33.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label33.Text = "5.0 Required Follow-up Action";
            this.label33.Top = 47.4775F;
            this.label33.Width = 4.834F;
            // 
            // label34
            // 
            this.label34.Height = 1.574996F;
            this.label34.HyperLink = null;
            this.label34.Left = 0F;
            this.label34.Name = "label34";
            this.label34.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label34.Text = resources.GetString("label34.Text");
            this.label34.Top = 47.74F;
            this.label34.Width = 7.47F;
            // 
            // label35
            // 
            this.label35.Height = 0.2F;
            this.label35.HyperLink = null;
            this.label35.Left = 0F;
            this.label35.Name = "label35";
            this.label35.Style = "font-size: 12pt; font-weight: bold; ddo-char-set: 0";
            this.label35.Text = "6.0 Freedom of Information Act";
            this.label35.Top = 49.448F;
            this.label35.Width = 4.834F;
            // 
            // label36
            // 
            this.label36.Height = 1.387497F;
            this.label36.HyperLink = null;
            this.label36.Left = 0F;
            this.label36.Name = "label36";
            this.label36.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.label36.Text = resources.GetString("label36.Text");
            this.label36.Top = 49.7105F;
            this.label36.Width = 7.47F;
            // 
            // pageBreak3
            // 
            this.pageBreak3.Height = 0.01F;
            this.pageBreak3.Left = 0F;
            this.pageBreak3.Name = "pageBreak3";
            this.pageBreak3.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak3.Top = 51.1605F;
            this.pageBreak3.Width = 6.5F;
            // 
            // label37
            // 
            this.label37.Height = 0.2F;
            this.label37.HyperLink = null;
            this.label37.Left = 0F;
            this.label37.Name = "label37";
            this.label37.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label37.Text = "APPENDIX A - AUDIT TEAM";
            this.label37.Top = 51.27856F;
            this.label37.Width = 7.667F;
            // 
            // label38
            // 
            this.label38.Height = 0.2309973F;
            this.label38.HyperLink = null;
            this.label38.Left = 0F;
            this.label38.Name = "label38";
            this.label38.Style = "color: Green; font-family: Times New Roman; font-size: 12pt; font-weight: normal;" +
    " text-align: center; ddo-char-set: 0";
            this.label38.Text = "(Lead Auditor complete)";
            this.label38.Top = 51.53857F;
            this.label38.Width = 7.667F;
            // 
            // pageBreak4
            // 
            this.pageBreak4.Height = 0.01F;
            this.pageBreak4.Left = 0F;
            this.pageBreak4.Name = "pageBreak4";
            this.pageBreak4.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak4.Top = 52.936F;
            this.pageBreak4.Width = 6.5F;
            // 
            // label39
            // 
            this.label39.Height = 0.2F;
            this.label39.HyperLink = null;
            this.label39.Left = 0F;
            this.label39.Name = "label39";
            this.label39.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label39.Text = "APPENDIX B - INSTALLATION POINTS OF CONTACT";
            this.label39.Top = 53.0085F;
            this.label39.Width = 7.667F;
            // 
            // label40
            // 
            this.label40.Height = 0.2309973F;
            this.label40.HyperLink = null;
            this.label40.Left = 0F;
            this.label40.Name = "label40";
            this.label40.Style = "color: Green; font-family: Times New Roman; font-size: 12pt; font-weight: normal;" +
    " text-align: center; ddo-char-set: 0";
            this.label40.Text = "(Lead Auditor complete)";
            this.label40.Top = 53.2685F;
            this.label40.Width = 7.667F;
            // 
            // pageBreak6
            // 
            this.pageBreak6.Height = 0.01F;
            this.pageBreak6.Left = 0F;
            this.pageBreak6.Name = "pageBreak6";
            this.pageBreak6.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak6.Top = 53.7589F;
            this.pageBreak6.Width = 6.5F;
            // 
            // label41
            // 
            this.label41.Height = 0.2F;
            this.label41.HyperLink = null;
            this.label41.Left = 0F;
            this.label41.Name = "label41";
            this.label41.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label41.Text = "APPENDIX C - COMPLIANCE CHECKLISTS";
            this.label41.Top = 53.812F;
            this.label41.Width = 7.667F;
            // 
            // subComplianceChecklists
            // 
            this.subComplianceChecklists.CloseBorder = false;
            this.subComplianceChecklists.Height = 1F;
            this.subComplianceChecklists.Left = 0F;
            this.subComplianceChecklists.Name = "subComplianceChecklists";
            this.subComplianceChecklists.Report = null;
            this.subComplianceChecklists.ReportName = "subReport4";
            this.subComplianceChecklists.Top = 54.09201F;
            this.subComplianceChecklists.Width = 7.646F;
            // 
            // pageBreak11
            // 
            this.pageBreak11.Height = 0.01F;
            this.pageBreak11.Left = 0F;
            this.pageBreak11.Name = "pageBreak11";
            this.pageBreak11.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak11.Top = 55.33293F;
            this.pageBreak11.Width = 6.5F;
            // 
            // label130
            // 
            this.label130.Height = 0.2F;
            this.label130.HyperLink = null;
            this.label130.Left = 0F;
            this.label130.Name = "label130";
            this.label130.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label130.Text = "APPENDIX D - SUMMARY OF FINDINGS AND OBSERVATIONS";
            this.label130.Top = 55.45099F;
            this.label130.Width = 7.667F;
            // 
            // subReport5
            // 
            this.subReport5.CloseBorder = false;
            this.subReport5.Height = 1F;
            this.subReport5.Left = 0F;
            this.subReport5.Name = "subReport5";
            this.subReport5.Report = null;
            this.subReport5.ReportName = "subReport5";
            this.subReport5.Top = 55.72199F;
            this.subReport5.Width = 7.646F;
            // 
            // pageBreak7
            // 
            this.pageBreak7.Height = 0.01F;
            this.pageBreak7.Left = 0F;
            this.pageBreak7.Name = "pageBreak7";
            this.pageBreak7.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak7.Top = 56.812F;
            this.pageBreak7.Width = 6.5F;
            // 
            // label45
            // 
            this.label45.Height = 0.2F;
            this.label45.HyperLink = null;
            this.label45.Left = 0F;
            this.label45.Name = "label45";
            this.label45.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label45.Text = "APPENDIX E - NOTEWORTHY PRACTICE, OBSERVATION, RECOMMENDATION FORMS";
            this.label45.Top = 56.875F;
            this.label45.Width = 7.667F;
            // 
            // subReport3
            // 
            this.subReport3.CloseBorder = false;
            this.subReport3.Height = 1F;
            this.subReport3.Left = 0F;
            this.subReport3.Name = "subReport3";
            this.subReport3.Report = null;
            this.subReport3.ReportName = "subReport3";
            this.subReport3.Top = 57.142F;
            this.subReport3.Width = 7.667F;
            // 
            // pageBreak9
            // 
            this.pageBreak9.Height = 0.01F;
            this.pageBreak9.Left = 0F;
            this.pageBreak9.Name = "pageBreak9";
            this.pageBreak9.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak9.Top = 59.46222F;
            this.pageBreak9.Width = 6.5F;
            // 
            // label46
            // 
            this.label46.Height = 0.2F;
            this.label46.HyperLink = null;
            this.label46.Left = 0F;
            this.label46.Name = "label46";
            this.label46.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label46.Text = "APPENDIX F - EMS NONCONFORMITY FORMS";
            this.label46.Top = 59.58028F;
            this.label46.Width = 7.667F;
            // 
            // subReport1
            // 
            this.subReport1.CloseBorder = false;
            this.subReport1.Height = 1.021002F;
            this.subReport1.Left = 0F;
            this.subReport1.Name = "subReport1";
            this.subReport1.Report = null;
            this.subReport1.ReportName = "subReport1";
            this.subReport1.Top = 59.83529F;
            this.subReport1.Width = 7.667F;
            // 
            // pageBreak10
            // 
            this.pageBreak10.Height = 0.01F;
            this.pageBreak10.Left = 0F;
            this.pageBreak10.Name = "pageBreak10";
            this.pageBreak10.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak10.Top = 60.90594F;
            this.pageBreak10.Width = 6.5F;
            // 
            // label42
            // 
            this.label42.Height = 0.2F;
            this.label42.HyperLink = null;
            this.label42.Left = 0F;
            this.label42.Name = "label42";
            this.label42.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label42.Text = "APPENDIX G - COMPLIANCE DEFICIENCY FORMS";
            this.label42.Top = 61.024F;
            this.label42.Width = 7.667F;
            // 
            // subReport2
            // 
            this.subReport2.CloseBorder = false;
            this.subReport2.Height = 1F;
            this.subReport2.Left = 0F;
            this.subReport2.Name = "subReport2";
            this.subReport2.Report = null;
            this.subReport2.ReportName = "subReport2";
            this.subReport2.Top = 61.28201F;
            this.subReport2.Width = 7.667F;
            // 
            // pageBreak12
            // 
            this.pageBreak12.Height = 0.01F;
            this.pageBreak12.Left = 0F;
            this.pageBreak12.Name = "pageBreak12";
            this.pageBreak12.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak12.Top = 62.389F;
            this.pageBreak12.Width = 6.5F;
            // 
            // label47
            // 
            this.label47.Height = 0.2F;
            this.label47.HyperLink = null;
            this.label47.Left = 0F;
            this.label47.Name = "label47";
            this.label47.Style = "font-size: 12pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label47.Text = "APPENDIX H - ABBREVIATIONS AND ACRONYMS";
            this.label47.Top = 62.50705F;
            this.label47.Width = 7.667F;
            // 
            // shape4
            // 
            this.shape4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(135)))), ((int)(((byte)(206)))), ((int)(((byte)(235)))));
            this.shape4.Height = 0.5F;
            this.shape4.Left = 0F;
            this.shape4.Name = "shape4";
            this.shape4.RoundingRadius = 9.999999F;
            this.shape4.Top = 62.77007F;
            this.shape4.Width = 7.667F;
            // 
            // label49
            // 
            this.label49.Height = 0.2F;
            this.label49.HyperLink = null;
            this.label49.Left = 0F;
            this.label49.Name = "label49";
            this.label49.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label49.Text = "Term/Acronym";
            this.label49.Top = 62.90607F;
            this.label49.Width = 1.906F;
            // 
            // label50
            // 
            this.label50.Height = 0.2F;
            this.label50.HyperLink = null;
            this.label50.Left = 1.907F;
            this.label50.Name = "label50";
            this.label50.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label50.Text = "Definition";
            this.label50.Top = 62.90606F;
            this.label50.Width = 5.739F;
            // 
            // line27
            // 
            this.line27.Height = 7.322002F;
            this.line27.Left = 1.906F;
            this.line27.LineWeight = 1F;
            this.line27.Name = "line27";
            this.line27.Top = 62.77106F;
            this.line27.Width = 0.0009999275F;
            this.line27.X1 = 1.907F;
            this.line27.X2 = 1.906F;
            this.line27.Y1 = 62.77106F;
            this.line27.Y2 = 70.09306F;
            // 
            // line30
            // 
            this.line30.Height = 0.001014709F;
            this.line30.Left = 0F;
            this.line30.LineWeight = 1F;
            this.line30.Name = "line30";
            this.line30.Top = 64.11606F;
            this.line30.Width = 7.666F;
            this.line30.X1 = 0F;
            this.line30.X2 = 7.666F;
            this.line30.Y1 = 64.11606F;
            this.line30.Y2 = 64.11707F;
            // 
            // pageBreak14
            // 
            this.pageBreak14.Height = 0.01F;
            this.pageBreak14.Left = 0F;
            this.pageBreak14.Name = "pageBreak14";
            this.pageBreak14.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak14.Top = 70.16357F;
            this.pageBreak14.Width = 6.5F;
            // 
            // label48
            // 
            this.label48.Height = 0.2F;
            this.label48.HyperLink = null;
            this.label48.Left = 0F;
            this.label48.Name = "label48";
            this.label48.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label48.Text = "Aspect";
            this.label48.Top = 63.34006F;
            this.label48.Width = 1.906F;
            // 
            // label51
            // 
            this.label51.Height = 0.356002F;
            this.label51.HyperLink = null;
            this.label51.Left = 1.907F;
            this.label51.Name = "label51";
            this.label51.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label51.Text = "A characteristic of a practice that can cause, in normal operation or upset mode," +
    " an impact to an environmental or other resource.  Each practice may have severa" +
    "l aspects.";
            this.label51.Top = 63.34006F;
            this.label51.Width = 5.739F;
            // 
            // label52
            // 
            this.label52.Height = 0.2F;
            this.label52.HyperLink = null;
            this.label52.Left = 0F;
            this.label52.Name = "label52";
            this.label52.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label52.Text = "BSO";
            this.label52.Top = 63.84606F;
            this.label52.Width = 1.906F;
            // 
            // label53
            // 
            this.label53.Height = 0.2F;
            this.label53.HyperLink = null;
            this.label53.Left = 1.907F;
            this.label53.Name = "label53";
            this.label53.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label53.Text = "Budget Support Officer";
            this.label53.Top = 63.84606F;
            this.label53.Width = 5.739F;
            // 
            // label54
            // 
            this.label54.Height = 0.2F;
            this.label54.HyperLink = null;
            this.label54.Left = 0F;
            this.label54.Name = "label54";
            this.label54.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label54.Text = "CNIC";
            this.label54.Top = 64.18907F;
            this.label54.Width = 1.906F;
            // 
            // label55
            // 
            this.label55.Height = 0.1999999F;
            this.label55.HyperLink = null;
            this.label55.Left = 1.907F;
            this.label55.Name = "label55";
            this.label55.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label55.Text = "Commander, Naval Installations Command";
            this.label55.Top = 64.18907F;
            this.label55.Width = 5.739F;
            // 
            // label56
            // 
            this.label56.Height = 0.2F;
            this.label56.HyperLink = null;
            this.label56.Left = 0F;
            this.label56.Name = "label56";
            this.label56.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label56.Text = "CNO";
            this.label56.Top = 64.53905F;
            this.label56.Width = 1.906F;
            // 
            // label57
            // 
            this.label57.Height = 0.1999999F;
            this.label57.HyperLink = null;
            this.label57.Left = 1.907F;
            this.label57.Name = "label57";
            this.label57.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label57.Text = "Chief of Naval Operations";
            this.label57.Top = 64.53905F;
            this.label57.Width = 5.739F;
            // 
            // label58
            // 
            this.label58.Height = 0.2F;
            this.label58.HyperLink = null;
            this.label58.Left = 0F;
            this.label58.Name = "label58";
            this.label58.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label58.Text = "CO";
            this.label58.Top = 64.88907F;
            this.label58.Width = 1.906F;
            // 
            // label59
            // 
            this.label59.Height = 0.1999999F;
            this.label59.HyperLink = null;
            this.label59.Left = 1.907F;
            this.label59.Name = "label59";
            this.label59.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label59.Text = "Commanding Officer";
            this.label59.Top = 64.88907F;
            this.label59.Width = 5.739F;
            // 
            // label60
            // 
            this.label60.Height = 0.2F;
            this.label60.HyperLink = null;
            this.label60.Left = 0F;
            this.label60.Name = "label60";
            this.label60.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label60.Text = "Conformance";
            this.label60.Top = 65.22906F;
            this.label60.Width = 1.906F;
            // 
            // label61
            // 
            this.label61.Height = 0.1999999F;
            this.label61.HyperLink = null;
            this.label61.Left = 1.907F;
            this.label61.Name = "label61";
            this.label61.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label61.Text = "Adherence to Navy and Regional EMS criteria.";
            this.label61.Top = 65.22906F;
            this.label61.Width = 5.739F;
            // 
            // label62
            // 
            this.label62.Height = 0.2F;
            this.label62.HyperLink = null;
            this.label62.Left = 0F;
            this.label62.Name = "label62";
            this.label62.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label62.Text = "Compliance";
            this.label62.Top = 65.56006F;
            this.label62.Width = 1.906F;
            // 
            // label63
            // 
            this.label63.Height = 0.3769981F;
            this.label63.HyperLink = null;
            this.label63.Left = 1.907F;
            this.label63.Name = "label63";
            this.label63.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label63.Text = "Adherence to Federal, state, local, DoD, Navy, Regional and other applicable lega" +
    "l, regulatory, or policy requirements.";
            this.label63.Top = 65.56006F;
            this.label63.Width = 5.739F;
            // 
            // label64
            // 
            this.label64.Height = 0.2F;
            this.label64.HyperLink = null;
            this.label64.Left = 0F;
            this.label64.Name = "label64";
            this.label64.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label64.Text = "DoD";
            this.label64.Top = 66.06606F;
            this.label64.Width = 1.906F;
            // 
            // label65
            // 
            this.label65.Height = 0.1999999F;
            this.label65.HyperLink = null;
            this.label65.Left = 1.907F;
            this.label65.Name = "label65";
            this.label65.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label65.Text = "Department of Defense";
            this.label65.Top = 66.06606F;
            this.label65.Width = 5.739F;
            // 
            // label66
            // 
            this.label66.Height = 0.2F;
            this.label66.HyperLink = null;
            this.label66.Left = 0F;
            this.label66.Name = "label66";
            this.label66.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label66.Text = "EMP";
            this.label66.Top = 66.39706F;
            this.label66.Width = 1.906F;
            // 
            // label67
            // 
            this.label67.Height = 0.1999999F;
            this.label67.HyperLink = null;
            this.label67.Left = 1.907F;
            this.label67.Name = "label67";
            this.label67.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label67.Text = "Environmental Management Procedure";
            this.label67.Top = 66.39706F;
            this.label67.Width = 5.739F;
            // 
            // label68
            // 
            this.label68.Height = 0.2F;
            this.label68.HyperLink = null;
            this.label68.Left = 0F;
            this.label68.Name = "label68";
            this.label68.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label68.Text = "EMS";
            this.label68.Top = 66.72707F;
            this.label68.Width = 1.906F;
            // 
            // label69
            // 
            this.label69.Height = 0.1999999F;
            this.label69.HyperLink = null;
            this.label69.Left = 1.907F;
            this.label69.Name = "label69";
            this.label69.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label69.Text = "Environmental Management System";
            this.label69.Top = 66.72707F;
            this.label69.Width = 5.739F;
            // 
            // label70
            // 
            this.label70.Height = 0.2F;
            this.label70.HyperLink = null;
            this.label70.Left = 0F;
            this.label70.Name = "label70";
            this.label70.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label70.Text = "Environmental Records";
            this.label70.Top = 67.06705F;
            this.label70.Width = 1.906F;
            // 
            // label71
            // 
            this.label71.Height = 0.9929993F;
            this.label71.HyperLink = null;
            this.label71.Left = 1.907F;
            this.label71.Name = "label71";
            this.label71.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label71.Text = resources.GetString("label71.Text");
            this.label71.Top = 67.06705F;
            this.label71.Width = 5.739F;
            // 
            // label72
            // 
            this.label72.Height = 0.2F;
            this.label72.HyperLink = null;
            this.label72.Left = 0F;
            this.label72.Name = "label72";
            this.label72.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label72.Text = "Environmental Requirement";
            this.label72.Top = 68.19006F;
            this.label72.Width = 1.906F;
            // 
            // label73
            // 
            this.label73.Height = 0.5139992F;
            this.label73.HyperLink = null;
            this.label73.Left = 1.907F;
            this.label73.Name = "label73";
            this.label73.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label73.Text = resources.GetString("label73.Text");
            this.label73.Top = 68.19006F;
            this.label73.Width = 5.739F;
            // 
            // label74
            // 
            this.label74.Height = 0.2F;
            this.label74.HyperLink = null;
            this.label74.Left = 0F;
            this.label74.Name = "label74";
            this.label74.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label74.Text = "EO";
            this.label74.Top = 68.83305F;
            this.label74.Width = 1.906F;
            // 
            // label75
            // 
            this.label75.Height = 0.1999962F;
            this.label75.HyperLink = null;
            this.label75.Left = 1.907F;
            this.label75.Name = "label75";
            this.label75.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label75.Text = "Executive Order";
            this.label75.Top = 68.83305F;
            this.label75.Width = 5.739F;
            // 
            // label76
            // 
            this.label76.Height = 0.2F;
            this.label76.HyperLink = null;
            this.label76.Left = 0F;
            this.label76.Name = "label76";
            this.label76.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label76.Text = "EOC";
            this.label76.Top = 69.16306F;
            this.label76.Width = 1.906F;
            // 
            // label77
            // 
            this.label77.Height = 0.1999962F;
            this.label77.HyperLink = null;
            this.label77.Left = 1.907F;
            this.label77.Name = "label77";
            this.label77.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label77.Text = "Emergency Operations Center";
            this.label77.Top = 69.16306F;
            this.label77.Width = 5.739F;
            // 
            // label78
            // 
            this.label78.Height = 0.2F;
            this.label78.HyperLink = null;
            this.label78.Left = 0F;
            this.label78.Name = "label78";
            this.label78.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label78.Text = "EPA";
            this.label78.Top = 69.50305F;
            this.label78.Width = 1.906F;
            // 
            // label79
            // 
            this.label79.Height = 0.1999962F;
            this.label79.HyperLink = null;
            this.label79.Left = 1.907F;
            this.label79.Name = "label79";
            this.label79.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label79.Text = "Environmental Protection Agency";
            this.label79.Top = 69.50305F;
            this.label79.Width = 5.739F;
            // 
            // label80
            // 
            this.label80.Height = 0.2F;
            this.label80.HyperLink = null;
            this.label80.Left = 0F;
            this.label80.Name = "label80";
            this.label80.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label80.Text = "EQA";
            this.label80.Top = 69.83406F;
            this.label80.Width = 1.906F;
            // 
            // label81
            // 
            this.label81.Height = 0.1999962F;
            this.label81.HyperLink = null;
            this.label81.Left = 1.907F;
            this.label81.Name = "label81";
            this.label81.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label81.Text = "Environmental Quality Assessment";
            this.label81.Top = 69.83406F;
            this.label81.Width = 5.739F;
            // 
            // line44
            // 
            this.line44.Height = 7.322002F;
            this.line44.Left = 7.666F;
            this.line44.LineWeight = 1F;
            this.line44.Name = "line44";
            this.line44.Top = 62.77206F;
            this.line44.Width = 0.0009999275F;
            this.line44.X1 = 7.667F;
            this.line44.X2 = 7.666F;
            this.line44.Y1 = 62.77206F;
            this.line44.Y2 = 70.09406F;
            // 
            // line28
            // 
            this.line28.Height = 0.001010895F;
            this.line28.Left = 0F;
            this.line28.LineWeight = 1F;
            this.line28.Name = "line28";
            this.line28.Top = 63.76906F;
            this.line28.Width = 7.666F;
            this.line28.X1 = 0F;
            this.line28.X2 = 7.666F;
            this.line28.Y1 = 63.76906F;
            this.line28.Y2 = 63.77007F;
            // 
            // line31
            // 
            this.line31.Height = 0.0009994507F;
            this.line31.Left = 0F;
            this.line31.LineWeight = 1F;
            this.line31.Name = "line31";
            this.line31.Top = 64.45805F;
            this.line31.Width = 7.666F;
            this.line31.X1 = 0F;
            this.line31.X2 = 7.666F;
            this.line31.Y1 = 64.45805F;
            this.line31.Y2 = 64.45905F;
            // 
            // line32
            // 
            this.line32.Height = 0.0009994507F;
            this.line32.Left = 0F;
            this.line32.LineWeight = 1F;
            this.line32.Name = "line32";
            this.line32.Top = 64.81206F;
            this.line32.Width = 7.666F;
            this.line32.X1 = 0F;
            this.line32.X2 = 7.666F;
            this.line32.Y1 = 64.81206F;
            this.line32.Y2 = 64.81306F;
            // 
            // line33
            // 
            this.line33.Height = 0.0009918213F;
            this.line33.Left = 0F;
            this.line33.LineWeight = 1F;
            this.line33.Name = "line33";
            this.line33.Top = 65.15607F;
            this.line33.Width = 7.666F;
            this.line33.X1 = 0F;
            this.line33.X2 = 7.666F;
            this.line33.Y1 = 65.15607F;
            this.line33.Y2 = 65.15706F;
            // 
            // line45
            // 
            this.line45.Height = 0.0009918213F;
            this.line45.Left = 0F;
            this.line45.LineWeight = 1F;
            this.line45.Name = "line45";
            this.line45.Top = 65.49007F;
            this.line45.Width = 7.666F;
            this.line45.X1 = 0F;
            this.line45.X2 = 7.666F;
            this.line45.Y1 = 65.49007F;
            this.line45.Y2 = 65.49106F;
            // 
            // line34
            // 
            this.line34.Height = 0.001014709F;
            this.line34.Left = 0F;
            this.line34.LineWeight = 1F;
            this.line34.Name = "line34";
            this.line34.Top = 66.00406F;
            this.line34.Width = 7.666F;
            this.line34.X1 = 0F;
            this.line34.X2 = 7.666F;
            this.line34.Y1 = 66.00406F;
            this.line34.Y2 = 66.00507F;
            // 
            // line35
            // 
            this.line35.Height = 0.001014709F;
            this.line35.Left = 0F;
            this.line35.LineWeight = 1F;
            this.line35.Name = "line35";
            this.line35.Top = 66.33806F;
            this.line35.Width = 7.666F;
            this.line35.X1 = 0F;
            this.line35.X2 = 7.666F;
            this.line35.Y1 = 66.33806F;
            this.line35.Y2 = 66.33907F;
            // 
            // line36
            // 
            this.line36.Height = 0.00100708F;
            this.line36.Left = 0F;
            this.line36.LineWeight = 1F;
            this.line36.Name = "line36";
            this.line36.Top = 66.64206F;
            this.line36.Width = 7.666F;
            this.line36.X1 = 0F;
            this.line36.X2 = 7.666F;
            this.line36.Y1 = 66.64206F;
            this.line36.Y2 = 66.64307F;
            // 
            // line37
            // 
            this.line37.Height = 0.0009994507F;
            this.line37.Left = 0F;
            this.line37.LineWeight = 1F;
            this.line37.Name = "line37";
            this.line37.Top = 66.99606F;
            this.line37.Width = 7.666F;
            this.line37.X1 = 0F;
            this.line37.X2 = 7.666F;
            this.line37.Y1 = 66.99606F;
            this.line37.Y2 = 66.99706F;
            // 
            // line38
            // 
            this.line38.Height = 0.0009841919F;
            this.line38.Left = 0F;
            this.line38.LineWeight = 1F;
            this.line38.Name = "line38";
            this.line38.Top = 68.13007F;
            this.line38.Width = 7.666F;
            this.line38.X1 = 0F;
            this.line38.X2 = 7.666F;
            this.line38.Y1 = 68.13007F;
            this.line38.Y2 = 68.13106F;
            // 
            // line39
            // 
            this.line39.Height = 0.0009841919F;
            this.line39.Left = 0F;
            this.line39.LineWeight = 1F;
            this.line39.Name = "line39";
            this.line39.Top = 68.76407F;
            this.line39.Width = 7.666F;
            this.line39.X1 = 0F;
            this.line39.X2 = 7.666F;
            this.line39.Y1 = 68.76407F;
            this.line39.Y2 = 68.76505F;
            // 
            // line40
            // 
            this.line40.Height = 0.0009994507F;
            this.line40.Left = 0F;
            this.line40.LineWeight = 1F;
            this.line40.Name = "line40";
            this.line40.Top = 69.10805F;
            this.line40.Width = 7.666F;
            this.line40.X1 = 0F;
            this.line40.X2 = 7.666F;
            this.line40.Y1 = 69.10805F;
            this.line40.Y2 = 69.10905F;
            // 
            // line41
            // 
            this.line41.Height = 0.0009994507F;
            this.line41.Left = 0F;
            this.line41.LineWeight = 1F;
            this.line41.Name = "line41";
            this.line41.Top = 69.44205F;
            this.line41.Width = 7.666F;
            this.line41.X1 = 0F;
            this.line41.X2 = 7.666F;
            this.line41.Y1 = 69.44205F;
            this.line41.Y2 = 69.44305F;
            // 
            // line42
            // 
            this.line42.Height = 0.0009994507F;
            this.line42.Left = 0F;
            this.line42.LineWeight = 1F;
            this.line42.Name = "line42";
            this.line42.Top = 69.77605F;
            this.line42.Width = 7.666F;
            this.line42.X1 = 0F;
            this.line42.X2 = 7.666F;
            this.line42.Y1 = 69.77605F;
            this.line42.Y2 = 69.77705F;
            // 
            // line43
            // 
            this.line43.Height = 0.0009918213F;
            this.line43.Left = 0F;
            this.line43.LineWeight = 1F;
            this.line43.Name = "line43";
            this.line43.Top = 70.10005F;
            this.line43.Width = 7.666F;
            this.line43.X1 = 0F;
            this.line43.X2 = 7.666F;
            this.line43.Y1 = 70.10005F;
            this.line43.Y2 = 70.10104F;
            // 
            // shape1
            // 
            this.shape1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(135)))), ((int)(((byte)(206)))), ((int)(((byte)(235)))));
            this.shape1.Height = 0.5F;
            this.shape1.Left = 0F;
            this.shape1.Name = "shape1";
            this.shape1.RoundingRadius = 9.999999F;
            this.shape1.Top = 70.26405F;
            this.shape1.Width = 7.667F;
            // 
            // label43
            // 
            this.label43.Height = 0.2F;
            this.label43.HyperLink = null;
            this.label43.Left = 0F;
            this.label43.Name = "label43";
            this.label43.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label43.Text = "Term/Acronym";
            this.label43.Top = 70.40005F;
            this.label43.Width = 1.906F;
            // 
            // label44
            // 
            this.label44.Height = 0.2F;
            this.label44.HyperLink = null;
            this.label44.Left = 1.907F;
            this.label44.Name = "label44";
            this.label44.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label44.Text = "Definition";
            this.label44.Top = 70.40005F;
            this.label44.Width = 5.739F;
            // 
            // line3
            // 
            this.line3.Height = 8.280983F;
            this.line3.Left = 1.906F;
            this.line3.LineWeight = 1F;
            this.line3.Name = "line3";
            this.line3.Top = 70.26405F;
            this.line3.Width = 0.0009999275F;
            this.line3.X1 = 1.907F;
            this.line3.X2 = 1.906F;
            this.line3.Y1 = 70.26405F;
            this.line3.Y2 = 78.54504F;
            // 
            // line4
            // 
            this.line4.Height = 8.280983F;
            this.line4.Left = 7.666F;
            this.line4.LineWeight = 1F;
            this.line4.Name = "line4";
            this.line4.Top = 70.26405F;
            this.line4.Width = 0.0009999275F;
            this.line4.X1 = 7.666F;
            this.line4.X2 = 7.667F;
            this.line4.Y1 = 70.26405F;
            this.line4.Y2 = 78.54504F;
            // 
            // line5
            // 
            this.line5.Height = 0.0009841919F;
            this.line5.Left = 0F;
            this.line5.LineWeight = 1F;
            this.line5.Name = "line5";
            this.line5.Top = 71.08405F;
            this.line5.Width = 7.666F;
            this.line5.X1 = 0F;
            this.line5.X2 = 7.666F;
            this.line5.Y1 = 71.08405F;
            this.line5.Y2 = 71.08504F;
            // 
            // label82
            // 
            this.label82.Height = 0.2F;
            this.label82.HyperLink = null;
            this.label82.Left = 0F;
            this.label82.Name = "label82";
            this.label82.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label82.Text = "EQMB";
            this.label82.Top = 70.83405F;
            this.label82.Width = 1.906F;
            // 
            // label83
            // 
            this.label83.Height = 0.1999962F;
            this.label83.HyperLink = null;
            this.label83.Left = 1.907F;
            this.label83.Name = "label83";
            this.label83.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label83.Text = "Environmental Quality Management Board";
            this.label83.Top = 70.83405F;
            this.label83.Width = 5.739F;
            // 
            // label84
            // 
            this.label84.Height = 0.2F;
            this.label84.HyperLink = null;
            this.label84.Left = 0F;
            this.label84.Name = "label84";
            this.label84.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label84.Text = "FRT";
            this.label84.Top = 71.15504F;
            this.label84.Width = 1.906F;
            // 
            // label85
            // 
            this.label85.Height = 0.1999962F;
            this.label85.HyperLink = null;
            this.label85.Left = 1.907F;
            this.label85.Name = "label85";
            this.label85.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label85.Text = "Facility Response Team";
            this.label85.Top = 71.15504F;
            this.label85.Width = 5.739F;
            // 
            // line6
            // 
            this.line6.Height = 0.0009765625F;
            this.line6.Left = 0F;
            this.line6.LineWeight = 1F;
            this.line6.Name = "line6";
            this.line6.Top = 71.41405F;
            this.line6.Width = 7.666F;
            this.line6.X1 = 0F;
            this.line6.X2 = 7.666F;
            this.line6.Y1 = 71.41405F;
            this.line6.Y2 = 71.41502F;
            // 
            // label86
            // 
            this.label86.Height = 0.2F;
            this.label86.HyperLink = null;
            this.label86.Left = 0F;
            this.label86.Name = "label86";
            this.label86.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label86.Text = "Impact";
            this.label86.Top = 71.49504F;
            this.label86.Width = 1.906F;
            // 
            // label87
            // 
            this.label87.Height = 0.356002F;
            this.label87.HyperLink = null;
            this.label87.Left = 1.907F;
            this.label87.Name = "label87";
            this.label87.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label87.Text = "An effect of a practice\'s aspect on an environmental or other resource.  Each pra" +
    "ctice may have several impacts";
            this.label87.Top = 71.49504F;
            this.label87.Width = 5.739F;
            // 
            // line7
            // 
            this.line7.Height = 0.0009841919F;
            this.line7.Left = 0F;
            this.line7.LineWeight = 1F;
            this.line7.Name = "line7";
            this.line7.Top = 71.91005F;
            this.line7.Width = 7.666F;
            this.line7.X1 = 0F;
            this.line7.X2 = 7.666F;
            this.line7.Y1 = 71.91005F;
            this.line7.Y2 = 71.91103F;
            // 
            // label88
            // 
            this.label88.Height = 0.2F;
            this.label88.HyperLink = null;
            this.label88.Left = 0F;
            this.label88.Name = "label88";
            this.label88.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label88.Text = "Internal Assessment";
            this.label88.Top = 71.98004F;
            this.label88.Width = 1.906F;
            // 
            // label89
            // 
            this.label89.Height = 0.6689978F;
            this.label89.HyperLink = null;
            this.label89.Left = 1.907F;
            this.label89.Name = "label89";
            this.label89.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label89.Text = resources.GetString("label89.Text");
            this.label89.Top = 71.98004F;
            this.label89.Width = 5.739F;
            // 
            // line8
            // 
            this.line8.Height = 0.0009765625F;
            this.line8.Left = 0F;
            this.line8.LineWeight = 1F;
            this.line8.Name = "line8";
            this.line8.Top = 72.70906F;
            this.line8.Width = 7.666F;
            this.line8.X1 = 0F;
            this.line8.X2 = 7.666F;
            this.line8.Y1 = 72.70906F;
            this.line8.Y2 = 72.71004F;
            // 
            // label90
            // 
            this.label90.Height = 0.3660004F;
            this.label90.HyperLink = null;
            this.label90.Left = 0F;
            this.label90.Name = "label90";
            this.label90.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label90.Text = "Internal Assessment Plan (IAP)";
            this.label90.Top = 72.77905F;
            this.label90.Width = 1.906F;
            // 
            // label91
            // 
            this.label91.Height = 0.5129996F;
            this.label91.HyperLink = null;
            this.label91.Left = 1.907F;
            this.label91.Name = "label91";
            this.label91.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label91.Text = resources.GetString("label91.Text");
            this.label91.Top = 72.77905F;
            this.label91.Width = 5.739F;
            // 
            // line9
            // 
            this.line9.Height = 0.0009994507F;
            this.line9.Left = 0F;
            this.line9.LineWeight = 1F;
            this.line9.Name = "line9";
            this.line9.Top = 73.36105F;
            this.line9.Width = 7.666F;
            this.line9.X1 = 0F;
            this.line9.X2 = 7.666F;
            this.line9.Y1 = 73.36105F;
            this.line9.Y2 = 73.36205F;
            // 
            // label92
            // 
            this.label92.Height = 0.2100098F;
            this.label92.HyperLink = null;
            this.label92.Left = 0F;
            this.label92.Name = "label92";
            this.label92.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label92.Text = "ISO";
            this.label92.Top = 73.42204F;
            this.label92.Width = 1.906F;
            // 
            // label93
            // 
            this.label93.Height = 0.2099982F;
            this.label93.HyperLink = null;
            this.label93.Left = 1.907F;
            this.label93.Name = "label93";
            this.label93.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label93.Text = "International Standards Organization";
            this.label93.Top = 73.42204F;
            this.label93.Width = 5.739F;
            // 
            // line10
            // 
            this.line10.Height = 0.00100708F;
            this.line10.Left = 0F;
            this.line10.LineWeight = 1F;
            this.line10.Name = "line10";
            this.line10.Top = 73.69203F;
            this.line10.Width = 7.666F;
            this.line10.X1 = 0F;
            this.line10.X2 = 7.666F;
            this.line10.Y1 = 73.69203F;
            this.line10.Y2 = 73.69304F;
            // 
            // label94
            // 
            this.label94.Height = 0.2100097F;
            this.label94.HyperLink = null;
            this.label94.Left = 0F;
            this.label94.Name = "label94";
            this.label94.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label94.Text = "NAB";
            this.label94.Top = 73.76303F;
            this.label94.Width = 1.906F;
            // 
            // label95
            // 
            this.label95.Height = 0.2099982F;
            this.label95.HyperLink = null;
            this.label95.Left = 1.907F;
            this.label95.Name = "label95";
            this.label95.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label95.Text = "Naval Amphibious Base";
            this.label95.Top = 73.76303F;
            this.label95.Width = 5.739F;
            // 
            // line11
            // 
            this.line11.Height = 0.001029968F;
            this.line11.Left = 0F;
            this.line11.LineWeight = 1F;
            this.line11.Name = "line11";
            this.line11.Top = 74.03204F;
            this.line11.Width = 7.666F;
            this.line11.X1 = 0F;
            this.line11.X2 = 7.666F;
            this.line11.Y1 = 74.03204F;
            this.line11.Y2 = 74.03307F;
            // 
            // label96
            // 
            this.label96.Height = 0.2100097F;
            this.label96.HyperLink = null;
            this.label96.Left = 0F;
            this.label96.Name = "label96";
            this.label96.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label96.Text = "NAS";
            this.label96.Top = 74.10306F;
            this.label96.Width = 1.906F;
            // 
            // label97
            // 
            this.label97.Height = 0.2099982F;
            this.label97.HyperLink = null;
            this.label97.Left = 1.907F;
            this.label97.Name = "label97";
            this.label97.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label97.Text = "Naval Air Station";
            this.label97.Top = 74.10306F;
            this.label97.Width = 5.739F;
            // 
            // line12
            // 
            this.line12.Height = 0.0009918213F;
            this.line12.Left = 0F;
            this.line12.LineWeight = 1F;
            this.line12.Name = "line12";
            this.line12.Top = 74.38205F;
            this.line12.Width = 7.666F;
            this.line12.X1 = 0F;
            this.line12.X2 = 7.666F;
            this.line12.Y1 = 74.38205F;
            this.line12.Y2 = 74.38304F;
            // 
            // label98
            // 
            this.label98.Height = 0.2100097F;
            this.label98.HyperLink = null;
            this.label98.Left = 0F;
            this.label98.Name = "label98";
            this.label98.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label98.Text = "NAVFAC";
            this.label98.Top = 74.44307F;
            this.label98.Width = 1.906F;
            // 
            // label99
            // 
            this.label99.Height = 0.2099982F;
            this.label99.HyperLink = null;
            this.label99.Left = 1.907F;
            this.label99.Name = "label99";
            this.label99.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label99.Text = "Naval Facilities Engineering Command";
            this.label99.Top = 74.44307F;
            this.label99.Width = 5.739F;
            // 
            // line13
            // 
            this.line13.Height = 0.0009918213F;
            this.line13.Left = 0F;
            this.line13.LineWeight = 1F;
            this.line13.Name = "line13";
            this.line13.Top = 74.71205F;
            this.line13.Width = 7.666F;
            this.line13.X1 = 0F;
            this.line13.X2 = 7.666F;
            this.line13.Y1 = 74.71205F;
            this.line13.Y2 = 74.71304F;
            // 
            // label100
            // 
            this.label100.Height = 0.2100097F;
            this.label100.HyperLink = null;
            this.label100.Left = 0F;
            this.label100.Name = "label100";
            this.label100.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label100.Text = "NAVSTA";
            this.label100.Top = 74.77304F;
            this.label100.Width = 1.906F;
            // 
            // label101
            // 
            this.label101.Height = 0.2099982F;
            this.label101.HyperLink = null;
            this.label101.Left = 1.907F;
            this.label101.Name = "label101";
            this.label101.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label101.Text = "Naval Station";
            this.label101.Top = 74.77304F;
            this.label101.Width = 5.739F;
            // 
            // line14
            // 
            this.line14.Height = 0.0009994507F;
            this.line14.Left = 0F;
            this.line14.LineWeight = 1F;
            this.line14.Name = "line14";
            this.line14.Top = 75.04205F;
            this.line14.Width = 7.666F;
            this.line14.X1 = 0F;
            this.line14.X2 = 7.666F;
            this.line14.Y1 = 75.04205F;
            this.line14.Y2 = 75.04305F;
            // 
            // label102
            // 
            this.label102.Height = 0.2100097F;
            this.label102.HyperLink = null;
            this.label102.Left = 0F;
            this.label102.Name = "label102";
            this.label102.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label102.Text = "NOSC";
            this.label102.Top = 75.10306F;
            this.label102.Width = 1.906F;
            // 
            // label103
            // 
            this.label103.Height = 0.2099982F;
            this.label103.HyperLink = null;
            this.label103.Left = 1.907F;
            this.label103.Name = "label103";
            this.label103.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label103.Text = "Navy On-Scene Commander";
            this.label103.Top = 75.10306F;
            this.label103.Width = 5.739F;
            // 
            // line15
            // 
            this.line15.Height = 0.0009918213F;
            this.line15.Left = 0F;
            this.line15.LineWeight = 1F;
            this.line15.Name = "line15";
            this.line15.Top = 75.38205F;
            this.line15.Width = 7.666F;
            this.line15.X1 = 0F;
            this.line15.X2 = 7.666F;
            this.line15.Y1 = 75.38205F;
            this.line15.Y2 = 75.38304F;
            // 
            // label104
            // 
            this.label104.Height = 0.2100097F;
            this.label104.HyperLink = null;
            this.label104.Left = 0F;
            this.label104.Name = "label104";
            this.label104.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label104.Text = "NSA";
            this.label104.Top = 75.44203F;
            this.label104.Width = 1.906F;
            // 
            // label105
            // 
            this.label105.Height = 0.2099982F;
            this.label105.HyperLink = null;
            this.label105.Left = 1.907F;
            this.label105.Name = "label105";
            this.label105.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label105.Text = "Naval Support Activity";
            this.label105.Top = 75.44203F;
            this.label105.Width = 5.739F;
            // 
            // line16
            // 
            this.line16.Height = 0.0009918213F;
            this.line16.Left = 0F;
            this.line16.LineWeight = 1F;
            this.line16.Name = "line16";
            this.line16.Top = 75.71205F;
            this.line16.Width = 7.666F;
            this.line16.X1 = 0F;
            this.line16.X2 = 7.666F;
            this.line16.Y1 = 75.71205F;
            this.line16.Y2 = 75.71304F;
            // 
            // label106
            // 
            this.label106.Height = 0.2100097F;
            this.label106.HyperLink = null;
            this.label106.Left = 0F;
            this.label106.Name = "label106";
            this.label106.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label106.Text = "NWS";
            this.label106.Top = 75.78305F;
            this.label106.Width = 1.906F;
            // 
            // label107
            // 
            this.label107.Height = 0.2099982F;
            this.label107.HyperLink = null;
            this.label107.Left = 1.907F;
            this.label107.Name = "label107";
            this.label107.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label107.Text = "Naval Weapons Station";
            this.label107.Top = 75.78305F;
            this.label107.Width = 5.739F;
            // 
            // line17
            // 
            this.line17.Height = 0.0009918213F;
            this.line17.Left = 0F;
            this.line17.LineWeight = 1F;
            this.line17.Name = "line17";
            this.line17.Top = 76.05205F;
            this.line17.Width = 7.666F;
            this.line17.X1 = 0F;
            this.line17.X2 = 7.666F;
            this.line17.Y1 = 76.05205F;
            this.line17.Y2 = 76.05304F;
            // 
            // label108
            // 
            this.label108.Height = 0.2100097F;
            this.label108.HyperLink = null;
            this.label108.Left = 0F;
            this.label108.Name = "label108";
            this.label108.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label108.Text = "Operational Controls";
            this.label108.Top = 76.11308F;
            this.label108.Width = 1.906F;
            // 
            // label109
            // 
            this.label109.Height = 2.356994F;
            this.label109.HyperLink = null;
            this.label109.Left = 1.907F;
            this.label109.Name = "label109";
            this.label109.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label109.Text = resources.GetString("label109.Text");
            this.label109.Top = 76.11308F;
            this.label109.Width = 5.739F;
            // 
            // line18
            // 
            this.line18.Height = 0.001045227F;
            this.line18.Left = 0F;
            this.line18.LineWeight = 1F;
            this.line18.Name = "line18";
            this.line18.Top = 78.54504F;
            this.line18.Width = 7.666F;
            this.line18.X1 = 0F;
            this.line18.X2 = 7.666F;
            this.line18.Y1 = 78.54504F;
            this.line18.Y2 = 78.54608F;
            // 
            // pageBreak16
            // 
            this.pageBreak16.Height = 0.01F;
            this.pageBreak16.Left = 0F;
            this.pageBreak16.Name = "pageBreak16";
            this.pageBreak16.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak16.Top = 78.6976F;
            this.pageBreak16.Width = 6.5F;
            // 
            // shape2
            // 
            this.shape2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(135)))), ((int)(((byte)(206)))), ((int)(((byte)(235)))));
            this.shape2.Height = 0.5F;
            this.shape2.Left = 0F;
            this.shape2.Name = "shape2";
            this.shape2.RoundingRadius = 9.999999F;
            this.shape2.Top = 78.77406F;
            this.shape2.Width = 7.667F;
            // 
            // label110
            // 
            this.label110.Height = 0.2F;
            this.label110.HyperLink = null;
            this.label110.Left = 0F;
            this.label110.Name = "label110";
            this.label110.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label110.Text = "Term/Acronym";
            this.label110.Top = 78.91011F;
            this.label110.Width = 1.906F;
            // 
            // label111
            // 
            this.label111.Height = 0.2F;
            this.label111.HyperLink = null;
            this.label111.Left = 1.907F;
            this.label111.Name = "label111";
            this.label111.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label111.Text = "Definition";
            this.label111.Top = 78.91003F;
            this.label111.Width = 5.739F;
            // 
            // line19
            // 
            this.line19.Height = 5.030975F;
            this.line19.Left = 1.907F;
            this.line19.LineWeight = 1F;
            this.line19.Name = "line19";
            this.line19.Top = 78.77406F;
            this.line19.Width = 0F;
            this.line19.X1 = 1.907F;
            this.line19.X2 = 1.907F;
            this.line19.Y1 = 78.77406F;
            this.line19.Y2 = 83.80504F;
            // 
            // line20
            // 
            this.line20.Height = 5.030975F;
            this.line20.Left = 7.666F;
            this.line20.LineWeight = 1F;
            this.line20.Name = "line20";
            this.line20.Top = 78.77406F;
            this.line20.Width = 0F;
            this.line20.X1 = 7.666F;
            this.line20.X2 = 7.666F;
            this.line20.Y1 = 78.77406F;
            this.line20.Y2 = 83.80504F;
            // 
            // label112
            // 
            this.label112.Height = 0.2100097F;
            this.label112.HyperLink = null;
            this.label112.Left = 0F;
            this.label112.Name = "label112";
            this.label112.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label112.Text = "PAO";
            this.label112.Top = 79.34407F;
            this.label112.Width = 1.906F;
            // 
            // label113
            // 
            this.label113.Height = 0.2099982F;
            this.label113.HyperLink = null;
            this.label113.Left = 1.907F;
            this.label113.Name = "label113";
            this.label113.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label113.Text = "Public Affairs Office";
            this.label113.Top = 79.34407F;
            this.label113.Width = 5.739F;
            // 
            // line22
            // 
            this.line22.Height = 0.0009002686F;
            this.line22.Left = 0F;
            this.line22.LineWeight = 1F;
            this.line22.Name = "line22";
            this.line22.Top = 79.6131F;
            this.line22.Width = 7.666F;
            this.line22.X1 = 0F;
            this.line22.X2 = 7.666F;
            this.line22.Y1 = 79.6131F;
            this.line22.Y2 = 79.614F;
            // 
            // label114
            // 
            this.label114.Height = 0.2100097F;
            this.label114.HyperLink = null;
            this.label114.Left = 0F;
            this.label114.Name = "label114";
            this.label114.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label114.Text = "POA&M";
            this.label114.Top = 79.67705F;
            this.label114.Width = 1.906F;
            // 
            // label115
            // 
            this.label115.Height = 0.2099982F;
            this.label115.HyperLink = null;
            this.label115.Left = 1.907F;
            this.label115.Name = "label115";
            this.label115.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label115.Text = "Plan of Actions and Milestones";
            this.label115.Top = 79.67705F;
            this.label115.Width = 5.739F;
            // 
            // line23
            // 
            this.line23.Height = 0.0009689331F;
            this.line23.Left = 0F;
            this.line23.LineWeight = 1F;
            this.line23.Name = "line23";
            this.line23.Top = 79.94608F;
            this.line23.Width = 7.666F;
            this.line23.X1 = 0F;
            this.line23.X2 = 7.666F;
            this.line23.Y1 = 79.94608F;
            this.line23.Y2 = 79.94705F;
            // 
            // label116
            // 
            this.label116.Height = 0.2100097F;
            this.label116.HyperLink = null;
            this.label116.Left = 0F;
            this.label116.Name = "label116";
            this.label116.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label116.Text = "Practice";
            this.label116.Top = 80.01706F;
            this.label116.Width = 1.906F;
            // 
            // label117
            // 
            this.label117.Height = 1.002997F;
            this.label117.HyperLink = null;
            this.label117.Left = 1.907F;
            this.label117.Name = "label117";
            this.label117.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label117.Text = resources.GetString("label117.Text");
            this.label117.Top = 80.01706F;
            this.label117.Width = 5.739F;
            // 
            // line24
            // 
            this.line24.Height = 0.0009765625F;
            this.line24.Left = 0F;
            this.line24.LineWeight = 1F;
            this.line24.Name = "line24";
            this.line24.Top = 81.07907F;
            this.line24.Width = 7.666F;
            this.line24.X1 = 0F;
            this.line24.X2 = 7.666F;
            this.line24.Y1 = 81.07907F;
            this.line24.Y2 = 81.08005F;
            // 
            // label118
            // 
            this.label118.Height = 0.2100097F;
            this.label118.HyperLink = null;
            this.label118.Left = 0F;
            this.label118.Name = "label118";
            this.label118.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label118.Text = "Practice Owner";
            this.label118.Top = 81.13907F;
            this.label118.Width = 1.906F;
            // 
            // label119
            // 
            this.label119.Height = 0.2099982F;
            this.label119.HyperLink = null;
            this.label119.Left = 1.907F;
            this.label119.Name = "label119";
            this.label119.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label119.Text = "The person, office, or department responsible for day-to-day operation of a pract" +
    "ice.";
            this.label119.Top = 81.13907F;
            this.label119.Width = 5.739F;
            // 
            // line25
            // 
            this.line25.Height = 0.0009536743F;
            this.line25.Left = 0F;
            this.line25.LineWeight = 1F;
            this.line25.Name = "line25";
            this.line25.Top = 81.40907F;
            this.line25.Width = 7.666F;
            this.line25.X1 = 0F;
            this.line25.X2 = 7.666F;
            this.line25.Y1 = 81.40907F;
            this.line25.Y2 = 81.41003F;
            // 
            // label120
            // 
            this.label120.Height = 0.2100097F;
            this.label120.HyperLink = null;
            this.label120.Left = 0F;
            this.label120.Name = "label120";
            this.label120.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label120.Text = "P2";
            this.label120.Top = 81.47003F;
            this.label120.Width = 1.906F;
            // 
            // label121
            // 
            this.label121.Height = 0.2099982F;
            this.label121.HyperLink = null;
            this.label121.Left = 1.907F;
            this.label121.Name = "label121";
            this.label121.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label121.Text = "Pollution Prevention";
            this.label121.Top = 81.47003F;
            this.label121.Width = 5.739F;
            // 
            // line26
            // 
            this.line26.Height = 0.0009765625F;
            this.line26.Left = 0F;
            this.line26.LineWeight = 1F;
            this.line26.Name = "line26";
            this.line26.Top = 81.73907F;
            this.line26.Width = 7.666F;
            this.line26.X1 = 0F;
            this.line26.X2 = 7.666F;
            this.line26.Y1 = 81.73907F;
            this.line26.Y2 = 81.74004F;
            // 
            // label122
            // 
            this.label122.Height = 0.2100097F;
            this.label122.HyperLink = null;
            this.label122.Left = 0F;
            this.label122.Name = "label122";
            this.label122.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label122.Text = "PWO";
            this.label122.Top = 81.81004F;
            this.label122.Width = 1.906F;
            // 
            // label123
            // 
            this.label123.Height = 0.2099982F;
            this.label123.HyperLink = null;
            this.label123.Left = 1.907F;
            this.label123.Name = "label123";
            this.label123.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label123.Text = "Public Works Officer";
            this.label123.Top = 81.81004F;
            this.label123.Width = 5.739F;
            // 
            // line46
            // 
            this.line46.Height = 0.0009765625F;
            this.line46.Left = 0F;
            this.line46.LineWeight = 1F;
            this.line46.Name = "line46";
            this.line46.Top = 82.07907F;
            this.line46.Width = 7.666F;
            this.line46.X1 = 0F;
            this.line46.X2 = 7.666F;
            this.line46.Y1 = 82.07907F;
            this.line46.Y2 = 82.08005F;
            // 
            // label124
            // 
            this.label124.Height = 0.2100097F;
            this.label124.HyperLink = null;
            this.label124.Left = 0F;
            this.label124.Name = "label124";
            this.label124.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label124.Text = "ROC";
            this.label124.Top = 82.14302F;
            this.label124.Width = 1.906F;
            // 
            // label125
            // 
            this.label125.Height = 0.2099982F;
            this.label125.HyperLink = null;
            this.label125.Left = 1.907F;
            this.label125.Name = "label125";
            this.label125.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label125.Text = "Region Operations Center";
            this.label125.Top = 82.14302F;
            this.label125.Width = 5.739F;
            // 
            // line47
            // 
            this.line47.Height = 0.0009613037F;
            this.line47.Left = 0F;
            this.line47.LineWeight = 1F;
            this.line47.Name = "line47";
            this.line47.Top = 82.42206F;
            this.line47.Width = 7.666F;
            this.line47.X1 = 0F;
            this.line47.X2 = 7.666F;
            this.line47.Y1 = 82.42206F;
            this.line47.Y2 = 82.42302F;
            // 
            // label126
            // 
            this.label126.Height = 0.2100097F;
            this.label126.HyperLink = null;
            this.label126.Left = 0F;
            this.label126.Name = "label126";
            this.label126.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label126.Text = "SEA";
            this.label126.Top = 82.49205F;
            this.label126.Width = 1.906F;
            // 
            // label127
            // 
            this.label127.Height = 0.2099982F;
            this.label127.HyperLink = null;
            this.label127.Left = 1.907F;
            this.label127.Name = "label127";
            this.label127.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label127.Text = "Significant Environmental Aspect";
            this.label127.Top = 82.49205F;
            this.label127.Width = 5.739F;
            // 
            // line48
            // 
            this.line48.Height = 0.0009613037F;
            this.line48.Left = 0F;
            this.line48.LineWeight = 1F;
            this.line48.Name = "line48";
            this.line48.Top = 82.76108F;
            this.line48.Width = 7.666F;
            this.line48.X1 = 0F;
            this.line48.X2 = 7.666F;
            this.line48.Y1 = 82.76108F;
            this.line48.Y2 = 82.76204F;
            // 
            // label128
            // 
            this.label128.Height = 0.2100097F;
            this.label128.HyperLink = null;
            this.label128.Left = 0F;
            this.label128.Name = "label128";
            this.label128.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label128.Text = "SMT";
            this.label128.Top = 82.83203F;
            this.label128.Width = 1.906F;
            // 
            // label129
            // 
            this.label129.Height = 0.2099982F;
            this.label129.HyperLink = null;
            this.label129.Left = 1.907F;
            this.label129.Name = "label129";
            this.label129.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label129.Text = "Spill Management Team";
            this.label129.Top = 82.83203F;
            this.label129.Width = 5.739F;
            // 
            // line49
            // 
            this.line49.Height = 0.0009765625F;
            this.line49.Left = 0F;
            this.line49.LineWeight = 1F;
            this.line49.Name = "line49";
            this.line49.Top = 83.11108F;
            this.line49.Width = 7.666F;
            this.line49.X1 = 0F;
            this.line49.X2 = 7.666F;
            this.line49.Y1 = 83.11108F;
            this.line49.Y2 = 83.11205F;
            // 
            // label137
            // 
            this.label137.Height = 0.2100097F;
            this.label137.HyperLink = null;
            this.label137.Left = 0F;
            this.label137.Name = "label137";
            this.label137.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label137.Text = "SPCC Plan";
            this.label137.Top = 83.18106F;
            this.label137.Width = 1.906F;
            // 
            // label138
            // 
            this.label138.Height = 0.2099982F;
            this.label138.HyperLink = null;
            this.label138.Left = 1.907F;
            this.label138.Name = "label138";
            this.label138.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label138.Text = "Spill Prevention, Control, and Countermeasure Plan";
            this.label138.Top = 83.18106F;
            this.label138.Width = 5.739F;
            // 
            // line50
            // 
            this.line50.Height = 0.001037598F;
            this.line50.Left = 0F;
            this.line50.LineWeight = 1F;
            this.line50.Name = "line50";
            this.line50.Top = 83.45004F;
            this.line50.Width = 7.666F;
            this.line50.X1 = 0F;
            this.line50.X2 = 7.666F;
            this.line50.Y1 = 83.45004F;
            this.line50.Y2 = 83.45108F;
            // 
            // label139
            // 
            this.label139.Height = 0.2100097F;
            this.label139.HyperLink = null;
            this.label139.Left = 0F;
            this.label139.Name = "label139";
            this.label139.Style = "font-size: 9.75pt; font-weight: bold; text-align: left; ddo-char-set: 0";
            this.label139.Text = "SOP";
            this.label139.Top = 83.51108F;
            this.label139.Width = 1.906F;
            // 
            // label140
            // 
            this.label140.Height = 0.2099982F;
            this.label140.HyperLink = null;
            this.label140.Left = 1.907F;
            this.label140.Name = "label140";
            this.label140.Style = "font-size: 9.75pt; font-weight: normal; text-align: left; ddo-char-set: 0";
            this.label140.Text = "Standard Operating Procedure";
            this.label140.Top = 83.51108F;
            this.label140.Width = 5.739F;
            // 
            // line51
            // 
            this.line51.Height = 0.0009765625F;
            this.line51.Left = 0F;
            this.line51.LineWeight = 1F;
            this.line51.Name = "line51";
            this.line51.Top = 83.804F;
            this.line51.Width = 7.666F;
            this.line51.X1 = 0F;
            this.line51.X2 = 7.666F;
            this.line51.Y1 = 83.804F;
            this.line51.Y2 = 83.80498F;
            // 
            // chart_internal_audit_compliance_findings
            // 
            this.chart_internal_audit_compliance_findings.AutoRefresh = true;
            this.chart_internal_audit_compliance_findings.Backdrop = new GrapeCity.ActiveReports.Chart.BackdropItem(GrapeCity.ActiveReports.Chart.Graphics.GradientType.Vertical, System.Drawing.Color.White, System.Drawing.Color.SteelBlue);
            chartArea2.AntiAliasMode = GrapeCity.ActiveReports.Chart.Graphics.AntiAliasMode.Graphics;
            axis6.AxisType = GrapeCity.ActiveReports.Chart.AxisType.Categorical;
            axis6.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis6.LabelsVisible = false;
            axis6.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 1D, 0F, false);
            axis6.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis6.Title = "Axis X";
            axis6.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis7.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis7.LabelsGap = 0;
            axis7.LabelsVisible = false;
            axis7.Line = new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None);
            axis7.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis7.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis7.Position = 0D;
            axis7.TickOffset = 0D;
            axis7.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis7.Visible = false;
            axis8.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis8.LabelsVisible = false;
            axis8.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis8.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis8.Position = 0D;
            axis8.Title = "Axis Y";
            axis8.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F), -90F);
            axis9.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis9.LabelsVisible = false;
            axis9.Line = new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None);
            axis9.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis9.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis9.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis9.Visible = false;
            axis10.LabelFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis10.LabelsGap = 0;
            axis10.LabelsVisible = false;
            axis10.Line = new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None);
            axis10.MajorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis10.MinorTick = new GrapeCity.ActiveReports.Chart.Tick(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0D, 0F, false);
            axis10.Position = 0D;
            axis10.TickOffset = 0D;
            axis10.TitleFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            axis10.Visible = false;
            chartArea2.Axes.AddRange(new GrapeCity.ActiveReports.Chart.AxisBase[] {
            axis6,
            axis7,
            axis8,
            axis9,
            axis10});
            chartArea2.Backdrop = new GrapeCity.ActiveReports.Chart.BackdropItem(GrapeCity.ActiveReports.Chart.Graphics.BackdropStyle.Transparent, System.Drawing.Color.White, System.Drawing.Color.White, GrapeCity.ActiveReports.Chart.Graphics.GradientType.Vertical, System.Drawing.Drawing2D.HatchStyle.DottedGrid, null, GrapeCity.ActiveReports.Chart.Graphics.PicturePutStyle.Stretched);
            chartArea2.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            chartArea2.Light = new GrapeCity.ActiveReports.Chart.Light(new GrapeCity.ActiveReports.Chart.Graphics.Point3d(10F, 40F, 20F), GrapeCity.ActiveReports.Chart.LightType.InfiniteDirectional, 0.3F);
            chartArea2.Name = "defaultArea";
            this.chart_internal_audit_compliance_findings.ChartAreas.AddRange(new GrapeCity.ActiveReports.Chart.ChartArea[] {
            chartArea2});
            this.chart_internal_audit_compliance_findings.ChartBorder = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            this.chart_internal_audit_compliance_findings.ColorPalette = GrapeCity.ActiveReports.Chart.ColorPalette.Iceberg;
            this.chart_internal_audit_compliance_findings.Height = 5.073002F;
            this.chart_internal_audit_compliance_findings.Left = 0F;
            legend1.Alignment = GrapeCity.ActiveReports.Chart.Alignment.Right;
            legend1.Backdrop = new GrapeCity.ActiveReports.Chart.BackdropItem(System.Drawing.Color.White, ((byte)(128)));
            legend1.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(), 0, System.Drawing.Color.Black);
            legend1.DockArea = chartArea2;
            title3.Backdrop = new GrapeCity.ActiveReports.Chart.Graphics.Backdrop(GrapeCity.ActiveReports.Chart.Graphics.BackdropStyle.Transparent, System.Drawing.Color.White, System.Drawing.Color.White, GrapeCity.ActiveReports.Chart.Graphics.GradientType.Vertical, System.Drawing.Drawing2D.HatchStyle.DottedGrid, null, GrapeCity.ActiveReports.Chart.Graphics.PicturePutStyle.Stretched);
            title3.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            title3.DockArea = null;
            title3.Font = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            title3.Name = "";
            title3.Text = "";
            legend1.Footer = title3;
            legend1.GridLayout = new GrapeCity.ActiveReports.Chart.GridLayout(0, 1);
            title4.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.White, 2), 0, System.Drawing.Color.Black);
            title4.DockArea = null;
            title4.Font = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            title4.Name = "";
            title4.Text = "Legend";
            legend1.Header = title4;
            legend1.LabelsFont = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            legend1.Name = "defaultLegend";
            legend1.Visible = false;
            this.chart_internal_audit_compliance_findings.Legends.AddRange(new GrapeCity.ActiveReports.Chart.Legend[] {
            legend1});
            this.chart_internal_audit_compliance_findings.Name = "chart_internal_audit_compliance_findings";
            title5.Alignment = GrapeCity.ActiveReports.Chart.Alignment.Center;
            title5.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            title5.DockArea = null;
            title5.Font = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Arial", 15.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0))));
            title5.Name = "header";
            title5.Text = "Internal Audit Compliance Findings";
            title6.Border = new GrapeCity.ActiveReports.Chart.Border(new GrapeCity.ActiveReports.Chart.Graphics.Line(System.Drawing.Color.Transparent, 0, GrapeCity.ActiveReports.Chart.Graphics.LineStyle.None), 0, System.Drawing.Color.Black);
            title6.DockArea = null;
            title6.Docking = GrapeCity.ActiveReports.Chart.DockType.Bottom;
            title6.Font = new GrapeCity.ActiveReports.Chart.FontInfo(System.Drawing.Color.Black, new System.Drawing.Font("Microsoft Sans Serif", 8F));
            title6.Name = "footer";
            title6.Text = "Chart Footer";
            title6.Visible = false;
            this.chart_internal_audit_compliance_findings.Titles.AddRange(new GrapeCity.ActiveReports.Chart.Title[] {
            title5,
            title6});
            this.chart_internal_audit_compliance_findings.Top = 40.704F;
            this.chart_internal_audit_compliance_findings.UIOptions = GrapeCity.ActiveReports.Chart.UIOptions.ForceHitTesting;
            this.chart_internal_audit_compliance_findings.Width = 7.708F;
            // 
            // pageBreak13
            // 
            this.pageBreak13.Height = 0.01F;
            this.pageBreak13.Left = 0F;
            this.pageBreak13.Name = "pageBreak13";
            this.pageBreak13.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak13.Top = 39.3065F;
            this.pageBreak13.Width = 6.5F;
            // 
            // subReport4
            // 
            this.subReport4.CloseBorder = false;
            this.subReport4.Height = 0.6460004F;
            this.subReport4.Left = 0F;
            this.subReport4.Name = "subReport4";
            this.subReport4.Report = null;
            this.subReport4.ReportName = "subReport4";
            this.subReport4.Top = 3.552F;
            this.subReport4.Width = 7.708F;
            // 
            // subReport6
            // 
            this.subReport6.CloseBorder = false;
            this.subReport6.Height = 0.6039999F;
            this.subReport6.Left = 0F;
            this.subReport6.Name = "subReport6";
            this.subReport6.Report = null;
            this.subReport6.ReportName = "subReport6";
            this.subReport6.Top = 4.668F;
            this.subReport6.Width = 7.708F;
            // 
            // label141
            // 
            this.label141.Height = 0.2F;
            this.label141.HyperLink = null;
            this.label141.Left = 0F;
            this.label141.Name = "label141";
            this.label141.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label141.Text = "Figure ES-1. Summary of Findings by Media";
            this.label141.Top = 3.251F;
            this.label141.Width = 3.041F;
            // 
            // label142
            // 
            this.label142.Height = 0.2F;
            this.label142.HyperLink = null;
            this.label142.Left = 0F;
            this.label142.Name = "label142";
            this.label142.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label142.Text = "Figure ES-2. Summary of Non-Comformance Findings";
            this.label142.Top = 4.368F;
            this.label142.Width = 4.333F;
            // 
            // pageBreak15
            // 
            this.pageBreak15.Height = 0.2F;
            this.pageBreak15.Left = 0F;
            this.pageBreak15.Name = "pageBreak15";
            this.pageBreak15.Size = new System.Drawing.SizeF(6.5F, 0.2F);
            this.pageBreak15.Top = 3.177F;
            this.pageBreak15.Width = 6.5F;
            // 
            // pageBreak17
            // 
            this.pageBreak17.Height = 0.01F;
            this.pageBreak17.Left = 0F;
            this.pageBreak17.Name = "pageBreak17";
            this.pageBreak17.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak17.Top = 4.278F;
            this.pageBreak17.Width = 6.5F;
            // 
            // pageBreak18
            // 
            this.pageBreak18.Height = 0.01F;
            this.pageBreak18.Left = 0F;
            this.pageBreak18.Name = "pageBreak18";
            this.pageBreak18.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak18.Top = 5.332001F;
            this.pageBreak18.Width = 6.5F;
            // 
            // subReport7
            // 
            this.subReport7.CloseBorder = false;
            this.subReport7.Height = 2.722F;
            this.subReport7.Left = 0F;
            this.subReport7.Name = "subReport7";
            this.subReport7.Report = null;
            this.subReport7.ReportName = "subReport7";
            this.subReport7.Top = 34.868F;
            this.subReport7.Width = 7.710001F;
            // 
            // pageBreak19
            // 
            this.pageBreak19.Height = 0.01F;
            this.pageBreak19.Left = 0F;
            this.pageBreak19.Name = "pageBreak19";
            this.pageBreak19.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak19.Top = 37.59F;
            this.pageBreak19.Width = 6.5F;
            // 
            // pageBreak20
            // 
            this.pageBreak20.Height = 0.01F;
            this.pageBreak20.Left = 0F;
            this.pageBreak20.Name = "pageBreak20";
            this.pageBreak20.Size = new System.Drawing.SizeF(6.5F, 0.01F);
            this.pageBreak20.Top = 34.805F;
            this.pageBreak20.Width = 6.5F;
            // 
            // textBox1
            // 
            this.textBox1.Height = 1.572001F;
            this.textBox1.Left = 0.198F;
            this.textBox1.Name = "textBox1";
            this.textBox1.Style = "font-family: Times New Roman; font-size: 12pt";
            this.textBox1.Text = resources.GetString("textBox1.Text");
            this.textBox1.Top = 10.74F;
            this.textBox1.Width = 7.239F;
            // 
            // textBox2
            // 
            this.textBox2.Height = 1.355998F;
            this.textBox2.Left = 0.648F;
            this.textBox2.Name = "textBox2";
            this.textBox2.Style = "font-family: Times New Roman; font-size: 12pt; ddo-char-set: 0";
            this.textBox2.Text = "(a) ODUSD Memorandum of 5 April 2002, DOD EMS\r\r\n(b) ODUSD Memorandum of 16 July 2" +
    "004, DOD EMS Self-Declaration Policy\r\r\n(c) Executive Order 13834 of 17 May 2018\r" +
    "\r\n(d) OPNAV M-5090.1";
            this.textBox2.Top = 24.392F;
            this.textBox2.Width = 6.845001F;
            // 
            // customControl1
            // 
            this.customControl1.Height = 0.1666667F;
            this.customControl1.Left = 0F;
            this.customControl1.Name = "customControl1";
            this.customControl1.Top = 0F;
            this.customControl1.Type = typeof(System.Windows.Forms.TextBox);
            this.customControl1.Width = 1.958333F;
            // 
            // reportHeader1
            // 
            this.reportHeader1.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.label1,
            this.line1});
            this.reportHeader1.Height = 0.8124999F;
            this.reportHeader1.Name = "reportHeader1";
            // 
            // label1
            // 
            this.label1.Height = 0.522F;
            this.label1.HyperLink = null;
            this.label1.Left = 0F;
            this.label1.Name = "label1";
            this.label1.Style = "font-size: 16pt; font-weight: bold";
            this.label1.Text = "EMS and Environmental Compliance Assessment: {Organization}, {Date}";
            this.label1.Top = 0.05F;
            this.label1.Width = 7.47F;
            // 
            // line1
            // 
            this.line1.Height = 0F;
            this.line1.Left = 0F;
            this.line1.LineWeight = 3F;
            this.line1.Name = "line1";
            this.line1.Top = 0.572F;
            this.line1.Width = 7.51F;
            this.line1.X1 = 0F;
            this.line1.X2 = 7.51F;
            this.line1.Y1 = 0.572F;
            this.line1.Y2 = 0.572F;
            // 
            // reportFooter1
            // 
            this.reportFooter1.Height = 0F;
            this.reportFooter1.Name = "reportFooter1";
            // 
            // InternalAuditReport
            // 
            this.MasterReport = false;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 7.710001F;
            this.Sections.Add(this.reportHeader1);
            this.Sections.Add(this.detail);
            this.Sections.Add(this.reportFooter1);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Arial; font-style: normal; text-decoration: none; font-weight: norma" +
            "l; font-size: 10pt; color: Black", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Times New Roman; font-size: 14pt; font-weight: bold; font-style: ita" +
            "lic", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.label2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label131)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label132)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label133)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label134)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label135)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label136)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lbl_41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label_4_1_Normal)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label33)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label34)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label35)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label36)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label37)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label38)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label39)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label40)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label130)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label45)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label46)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label42)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label47)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label49)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label50)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label48)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label51)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label52)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label53)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label54)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label55)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label56)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label57)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label58)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label59)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label60)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label61)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label62)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label63)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label64)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label65)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label66)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label67)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label68)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label69)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label70)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label71)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label72)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label73)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label74)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label75)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label76)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label77)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label78)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label79)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label80)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label81)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label43)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label44)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label82)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label83)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label84)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label85)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label86)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label87)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label88)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label89)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label90)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label91)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label92)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label93)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label94)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label95)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label96)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label97)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label98)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label99)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label100)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label101)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label102)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label103)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label104)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label105)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label106)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label107)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label108)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label109)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label110)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label111)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label112)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label113)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label114)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label115)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label116)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label117)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label118)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label119)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label120)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label121)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label122)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label123)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label124)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label125)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label126)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label127)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label128)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label129)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label137)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label138)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label139)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label140)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chart_internal_audit_compliance_findings)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label141)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label142)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.customControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion






































































































































































































































        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;
        private GrapeCity.ActiveReports.SectionReportModel.ReportHeader reportHeader1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label1;
        private GrapeCity.ActiveReports.SectionReportModel.ReportFooter reportFooter1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label3;
        private GrapeCity.ActiveReports.SectionReportModel.Line line1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label18;
        private GrapeCity.ActiveReports.SectionReportModel.Label label27;
        private GrapeCity.ActiveReports.SectionReportModel.Label label28;
        private GrapeCity.ActiveReports.SectionReportModel.Line line29;
        private GrapeCity.ActiveReports.SectionReportModel.Line line2;
        private GrapeCity.ActiveReports.SectionReportModel.Line line21;
        private GrapeCity.ActiveReports.SectionReportModel.Label label19;
        private GrapeCity.ActiveReports.SectionReportModel.Label label131;
        private GrapeCity.ActiveReports.SectionReportModel.Label label30;
        private GrapeCity.ActiveReports.SectionReportModel.Label label132;
        private GrapeCity.ActiveReports.SectionReportModel.Label label133;
        private GrapeCity.ActiveReports.SectionReportModel.Label label134;
        private GrapeCity.ActiveReports.SectionReportModel.Label label135;
        private GrapeCity.ActiveReports.SectionReportModel.Label label136;
        private GrapeCity.ActiveReports.SectionReportModel.Label label4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label5;
        private GrapeCity.ActiveReports.SectionReportModel.Label label6;
        private GrapeCity.ActiveReports.SectionReportModel.Label label7;
        private GrapeCity.ActiveReports.SectionReportModel.Label label8;
        private GrapeCity.ActiveReports.SectionReportModel.Label label9;
        private GrapeCity.ActiveReports.SectionReportModel.Label label10;
        private GrapeCity.ActiveReports.SectionReportModel.Label label11;
        private GrapeCity.ActiveReports.SectionReportModel.Label label12;
        private GrapeCity.ActiveReports.SectionReportModel.Label label13;
        private GrapeCity.ActiveReports.SectionReportModel.Label label14;
        private GrapeCity.ActiveReports.SectionReportModel.Label label15;
        private GrapeCity.ActiveReports.SectionReportModel.Label label16;
        private GrapeCity.ActiveReports.SectionReportModel.Label label17;
        private GrapeCity.ActiveReports.SectionReportModel.Label label20;
        private GrapeCity.ActiveReports.SectionReportModel.Label label22;
        private GrapeCity.ActiveReports.SectionReportModel.Label label23;
        private GrapeCity.ActiveReports.SectionReportModel.ChartControl chartControl3;
        private GrapeCity.ActiveReports.SectionReportModel.Label label24;
        private GrapeCity.ActiveReports.SectionReportModel.Label label25;
        private GrapeCity.ActiveReports.SectionReportModel.Label label26;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subComplianceMediaFindings;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label33;
        private GrapeCity.ActiveReports.SectionReportModel.Label label34;
        private GrapeCity.ActiveReports.SectionReportModel.Label label35;
        private GrapeCity.ActiveReports.SectionReportModel.Label label36;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak3;
        private GrapeCity.ActiveReports.SectionReportModel.Label label37;
        private GrapeCity.ActiveReports.SectionReportModel.Label label38;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label39;
        private GrapeCity.ActiveReports.SectionReportModel.Label label40;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak6;
        private GrapeCity.ActiveReports.SectionReportModel.Label label41;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subComplianceChecklists;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak11;
        private GrapeCity.ActiveReports.SectionReportModel.Label label130;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport5;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak7;
        private GrapeCity.ActiveReports.SectionReportModel.Label label45;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport3;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak9;
        private GrapeCity.ActiveReports.SectionReportModel.Label label46;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport1;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak10;
        private GrapeCity.ActiveReports.SectionReportModel.Label label42;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport2;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak12;
        private GrapeCity.ActiveReports.SectionReportModel.Label label47;
        private GrapeCity.ActiveReports.SectionReportModel.Shape shape4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label49;
        private GrapeCity.ActiveReports.SectionReportModel.Label label50;
        private GrapeCity.ActiveReports.SectionReportModel.Line line27;
        private GrapeCity.ActiveReports.SectionReportModel.Line line30;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak14;
        private GrapeCity.ActiveReports.SectionReportModel.Label label48;
        private GrapeCity.ActiveReports.SectionReportModel.Label label51;
        private GrapeCity.ActiveReports.SectionReportModel.Label label52;
        private GrapeCity.ActiveReports.SectionReportModel.Label label53;
        private GrapeCity.ActiveReports.SectionReportModel.Label label54;
        private GrapeCity.ActiveReports.SectionReportModel.Label label55;
        private GrapeCity.ActiveReports.SectionReportModel.Label label56;
        private GrapeCity.ActiveReports.SectionReportModel.Label label57;
        private GrapeCity.ActiveReports.SectionReportModel.Label label58;
        private GrapeCity.ActiveReports.SectionReportModel.Label label59;
        private GrapeCity.ActiveReports.SectionReportModel.Label label60;
        private GrapeCity.ActiveReports.SectionReportModel.Label label61;
        private GrapeCity.ActiveReports.SectionReportModel.Label label62;
        private GrapeCity.ActiveReports.SectionReportModel.Label label63;
        private GrapeCity.ActiveReports.SectionReportModel.Label label64;
        private GrapeCity.ActiveReports.SectionReportModel.Label label65;
        private GrapeCity.ActiveReports.SectionReportModel.Label label66;
        private GrapeCity.ActiveReports.SectionReportModel.Label label67;
        private GrapeCity.ActiveReports.SectionReportModel.Label label68;
        private GrapeCity.ActiveReports.SectionReportModel.Label label69;
        private GrapeCity.ActiveReports.SectionReportModel.Label label70;
        private GrapeCity.ActiveReports.SectionReportModel.Label label71;
        private GrapeCity.ActiveReports.SectionReportModel.Label label72;
        private GrapeCity.ActiveReports.SectionReportModel.Label label73;
        private GrapeCity.ActiveReports.SectionReportModel.Label label74;
        private GrapeCity.ActiveReports.SectionReportModel.Label label75;
        private GrapeCity.ActiveReports.SectionReportModel.Label label76;
        private GrapeCity.ActiveReports.SectionReportModel.Label label77;
        private GrapeCity.ActiveReports.SectionReportModel.Label label78;
        private GrapeCity.ActiveReports.SectionReportModel.Label label79;
        private GrapeCity.ActiveReports.SectionReportModel.Label label80;
        private GrapeCity.ActiveReports.SectionReportModel.Label label81;
        private GrapeCity.ActiveReports.SectionReportModel.Line line44;
        private GrapeCity.ActiveReports.SectionReportModel.Line line28;
        private GrapeCity.ActiveReports.SectionReportModel.Line line31;
        private GrapeCity.ActiveReports.SectionReportModel.Line line32;
        private GrapeCity.ActiveReports.SectionReportModel.Line line33;
        private GrapeCity.ActiveReports.SectionReportModel.Line line45;
        private GrapeCity.ActiveReports.SectionReportModel.Line line34;
        private GrapeCity.ActiveReports.SectionReportModel.Line line35;
        private GrapeCity.ActiveReports.SectionReportModel.Line line36;
        private GrapeCity.ActiveReports.SectionReportModel.Line line37;
        private GrapeCity.ActiveReports.SectionReportModel.Line line38;
        private GrapeCity.ActiveReports.SectionReportModel.Line line39;
        private GrapeCity.ActiveReports.SectionReportModel.Line line40;
        private GrapeCity.ActiveReports.SectionReportModel.Line line41;
        private GrapeCity.ActiveReports.SectionReportModel.Line line42;
        private GrapeCity.ActiveReports.SectionReportModel.Line line43;
        private GrapeCity.ActiveReports.SectionReportModel.Shape shape1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label43;
        private GrapeCity.ActiveReports.SectionReportModel.Label label44;
        private GrapeCity.ActiveReports.SectionReportModel.Line line3;
        private GrapeCity.ActiveReports.SectionReportModel.Line line4;
        private GrapeCity.ActiveReports.SectionReportModel.Line line5;
        private GrapeCity.ActiveReports.SectionReportModel.Label label82;
        private GrapeCity.ActiveReports.SectionReportModel.Label label83;
        private GrapeCity.ActiveReports.SectionReportModel.Label label84;
        private GrapeCity.ActiveReports.SectionReportModel.Label label85;
        private GrapeCity.ActiveReports.SectionReportModel.Line line6;
        private GrapeCity.ActiveReports.SectionReportModel.Label label86;
        private GrapeCity.ActiveReports.SectionReportModel.Label label87;
        private GrapeCity.ActiveReports.SectionReportModel.Line line7;
        private GrapeCity.ActiveReports.SectionReportModel.Label label88;
        private GrapeCity.ActiveReports.SectionReportModel.Label label89;
        private GrapeCity.ActiveReports.SectionReportModel.Line line8;
        private GrapeCity.ActiveReports.SectionReportModel.Label label90;
        private GrapeCity.ActiveReports.SectionReportModel.Label label91;
        private GrapeCity.ActiveReports.SectionReportModel.Line line9;
        private GrapeCity.ActiveReports.SectionReportModel.Label label92;
        private GrapeCity.ActiveReports.SectionReportModel.Label label93;
        private GrapeCity.ActiveReports.SectionReportModel.Line line10;
        private GrapeCity.ActiveReports.SectionReportModel.Label label94;
        private GrapeCity.ActiveReports.SectionReportModel.Label label95;
        private GrapeCity.ActiveReports.SectionReportModel.Line line11;
        private GrapeCity.ActiveReports.SectionReportModel.Label label96;
        private GrapeCity.ActiveReports.SectionReportModel.Label label97;
        private GrapeCity.ActiveReports.SectionReportModel.Line line12;
        private GrapeCity.ActiveReports.SectionReportModel.Label label98;
        private GrapeCity.ActiveReports.SectionReportModel.Label label99;
        private GrapeCity.ActiveReports.SectionReportModel.Line line13;
        private GrapeCity.ActiveReports.SectionReportModel.Label label100;
        private GrapeCity.ActiveReports.SectionReportModel.Label label101;
        private GrapeCity.ActiveReports.SectionReportModel.Line line14;
        private GrapeCity.ActiveReports.SectionReportModel.Label label102;
        private GrapeCity.ActiveReports.SectionReportModel.Label label103;
        private GrapeCity.ActiveReports.SectionReportModel.Line line15;
        private GrapeCity.ActiveReports.SectionReportModel.Label label104;
        private GrapeCity.ActiveReports.SectionReportModel.Label label105;
        private GrapeCity.ActiveReports.SectionReportModel.Line line16;
        private GrapeCity.ActiveReports.SectionReportModel.Label label106;
        private GrapeCity.ActiveReports.SectionReportModel.Label label107;
        private GrapeCity.ActiveReports.SectionReportModel.Line line17;
        private GrapeCity.ActiveReports.SectionReportModel.Label label108;
        private GrapeCity.ActiveReports.SectionReportModel.Label label109;
        private GrapeCity.ActiveReports.SectionReportModel.Line line18;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak16;
        private GrapeCity.ActiveReports.SectionReportModel.Shape shape2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label110;
        private GrapeCity.ActiveReports.SectionReportModel.Label label111;
        private GrapeCity.ActiveReports.SectionReportModel.Line line19;
        private GrapeCity.ActiveReports.SectionReportModel.Line line20;
        private GrapeCity.ActiveReports.SectionReportModel.Label label112;
        private GrapeCity.ActiveReports.SectionReportModel.Label label113;
        private GrapeCity.ActiveReports.SectionReportModel.Line line22;
        private GrapeCity.ActiveReports.SectionReportModel.Label label114;
        private GrapeCity.ActiveReports.SectionReportModel.Label label115;
        private GrapeCity.ActiveReports.SectionReportModel.Line line23;
        private GrapeCity.ActiveReports.SectionReportModel.Label label116;
        private GrapeCity.ActiveReports.SectionReportModel.Label label117;
        private GrapeCity.ActiveReports.SectionReportModel.Line line24;
        private GrapeCity.ActiveReports.SectionReportModel.Label label118;
        private GrapeCity.ActiveReports.SectionReportModel.Label label119;
        private GrapeCity.ActiveReports.SectionReportModel.Line line25;
        private GrapeCity.ActiveReports.SectionReportModel.Label label120;
        private GrapeCity.ActiveReports.SectionReportModel.Label label121;
        private GrapeCity.ActiveReports.SectionReportModel.Line line26;
        private GrapeCity.ActiveReports.SectionReportModel.Label label122;
        private GrapeCity.ActiveReports.SectionReportModel.Label label123;
        private GrapeCity.ActiveReports.SectionReportModel.Line line46;
        private GrapeCity.ActiveReports.SectionReportModel.Label label124;
        private GrapeCity.ActiveReports.SectionReportModel.Label label125;
        private GrapeCity.ActiveReports.SectionReportModel.Line line47;
        private GrapeCity.ActiveReports.SectionReportModel.Label label126;
        private GrapeCity.ActiveReports.SectionReportModel.Label label127;
        private GrapeCity.ActiveReports.SectionReportModel.Line line48;
        private GrapeCity.ActiveReports.SectionReportModel.Label label128;
        private GrapeCity.ActiveReports.SectionReportModel.Label label129;
        private GrapeCity.ActiveReports.SectionReportModel.Line line49;
        private GrapeCity.ActiveReports.SectionReportModel.Label label137;
        private GrapeCity.ActiveReports.SectionReportModel.Label label138;
        private GrapeCity.ActiveReports.SectionReportModel.Line line50;
        private GrapeCity.ActiveReports.SectionReportModel.Label label139;
        private GrapeCity.ActiveReports.SectionReportModel.Label label140;
        private GrapeCity.ActiveReports.SectionReportModel.Line line51;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport4;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport6;
        private GrapeCity.ActiveReports.SectionReportModel.Label label141;
        private GrapeCity.ActiveReports.SectionReportModel.Label label142;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak15;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak17;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak18;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport7;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak19;
        private GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak20;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox2;
        private GrapeCity.ActiveReports.SectionReportModel.CustomControl customControl1;
        protected GrapeCity.ActiveReports.SectionReportModel.Label label_4_1_Normal;
        protected GrapeCity.ActiveReports.SectionReportModel.ChartControl chart_internal_audit_compliance_findings;
        protected GrapeCity.ActiveReports.SectionReportModel.SubReport sr_41_alt;
        protected GrapeCity.ActiveReports.SectionReportModel.Label lbl_41;
        protected GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak1;
        protected GrapeCity.ActiveReports.SectionReportModel.PageBreak pageBreak13;

    }
}
