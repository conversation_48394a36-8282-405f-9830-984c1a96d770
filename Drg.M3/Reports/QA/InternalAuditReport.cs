using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.Linq;
using GrapeCity.ActiveReports.Chart.Graphics;
using Drg.M3.Bll.Reports;
using Drg.M3.Client;
using Drg.M3.Dao;
using Drg.M3.Dao.QA;
using Drg.M3.Domain;
using Drg.M3.Domain.QA;
using GrapeCity.ActiveReports.SectionReportModel;
using GrapeCity.ActiveReports.Chart;
using Border = GrapeCity.ActiveReports.Chart.Border;
using Label = GrapeCity.ActiveReports.SectionReportModel.Label;
using Line = GrapeCity.ActiveReports.Chart.Graphics.Line;
using LineStyle = GrapeCity.ActiveReports.Chart.Graphics.LineStyle;
using System.Collections.Generic;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Reports.QA
{
    /// <summary>
    /// Summary description for InternalAuditReport.
    /// </summary>
    [Report("Internal Audit Report", "Default", typeof (Audit), typeof (Modules.QA), ExportFormat.Rtf)]
    public partial class InternalAuditReport : GrapeCity.ActiveReports.SectionReport, INeedsSession
    {
        public IM3Session Session { get; set; }
        private int idx;

        public InternalAuditReport()
        {
            //
            // Required for Windows Form Designer support
            //
            InitializeComponent();

            reportHeader1.Format += reportHeader1_Format;
            //detail.Format += new EventHandler(detail_Format);
        }

        private void reportHeader1_Format(object sender, EventArgs e)
        {
            var a = this.ListDataSource()[0] as Audit;
            label1.Text = label1.Text.Replace("{Organization}", a.Organization.Name);
            label1.Text = label1.Text.Replace("{Date}", a.StartDate.ToShortDateString() + " - " + a.EndDate.ToShortDateString());
        }

        protected virtual IEnumerable<Finding> GetFindings(Audit audit)
        {
            return new FindingDao(Session).GetAll(audit);
        }

        protected IEnumerable<Finding> findings { get; set; }

        protected virtual void detail_Format(object sender, EventArgs ea)
        {
            if (this.ListDataSource().Count > 0)
            {
                var a = this.ListDataSource()[idx] as Audit;

                findings = GetFindings(a);

                var medias = a.AuditTemplates.ToList().ConvertAll(c => c.Media).Distinct();

                var countFindings = findings.Count().ToString();

                //var countEMSObservations_A = findings.Count(w => w.Category != null && 
                //                                                w.Category.ToString() == "Observation" &&
                //                                                w.FindingType != null &&
                //                                                w.FindingType.ToString() == "EMS"
                //                                            ).ToString(CultureInfo.InvariantCulture);

                var countEMSObservations_B = findings.Count(w => w.IsObservation &&
                                                                w.Category != null && w.Category.ToString() == "Positive Observation"
                                                            ).ToString(CultureInfo.InvariantCulture);

                var countEMSRecommendations = findings.Count(w => w.IsObservation &&
                                                                w.Category != null && w.Category.ToString() == "Recommendation"
                                                            //&& w.FindingType != null && w.FindingType.ToString() == "EMS"     //Task #54528
                                                            ).ToString(CultureInfo.InvariantCulture);

                var countEMSNoteworthyPractice = findings.Count(w => w.IsObservation &&
                                                                    w.Category != null && w.Category.ToString() == "Noteworthy Practice"
                                                                //&& w.FindingType != null && w.FindingType.ToString() == "EMS" //Task #54528
                                                                ).ToString(CultureInfo.InvariantCulture);

                var countRegulatory = findings.Count(w => w.FindingType != null &&
                                                        w.FindingType.ToString() == "Compliance" &&
                                                        w.Category != null &&
                                                        w.Category.ToString().EndsWith("Regulatory")).ToString(CultureInfo.InvariantCulture);


                var countPolicy = findings.Count(w => w.FindingType != null &&
                                                    w.FindingType.ToString() == "Compliance" &&
                                                    w.Category != null &&
                                                    w.Category.ToString() == "Policy").ToString(CultureInfo.InvariantCulture);

                var countComplianceRecommendations = findings.Count(w => w.IsObservation &&
                                                                        w.Category != null &&
                                                                        w.Category.ToString() == "Recommendation" &&
                                                                        w.FindingType != null &&
                                                                        w.FindingType.ToString() == "Compliance").ToString(CultureInfo.InvariantCulture);

                var countCompliancePositiveObservations = findings.Count(w => w.IsObservation &&
                                                                            w.Category != null &&
                                                                            w.Category.ToString() == "Positive Observation" &&
                                                                            w.FindingType != null &&
                                                                            w.FindingType.ToString() == "Compliance").ToString(CultureInfo.InvariantCulture);

                var countComplianceNoteworthyPractices = findings.Count(w => w.IsObservation &&
                                                                            w.Category != null &&
                                                                            w.Category.ToString() == "Noteworthy Practice" &&
                                                                            w.FindingType != null &&
                                                                            w.FindingType.ToString() == "Compliance").ToString(CultureInfo.InvariantCulture);

                var countComplianceObservation = findings.Count(w => w.Category != null &&
                                                                    w.Category.ToString() == "Observation" &&
                                                                    w.FindingType != null &&
                                                                    w.FindingType.ToString() == "Compliance").ToString(CultureInfo.InvariantCulture);

                var countOpportunity = findings.Count(w => w.IsObservation).ToString();


                var countMinor = findings.Count(w => w.Category != null &&
                                                   w.Category.ToString() == "Minor" &&
                                                   w.FindingType != null &&
                                                   w.FindingType.ToString() == "EMS").ToString(CultureInfo.InvariantCulture);


                var countMajor = findings.Count(w => w.Category != null &&
                                                   w.Category.ToString() == "Major" &&
                                                   w.FindingType != null &&
                                                   w.FindingType.ToString() == "EMS").ToString(CultureInfo.InvariantCulture);

                foreach (Section s in Sections)
                {
                    foreach (ARControl c in s.Controls)
                    {
                        if (c is Label)
                        {
                            var lbl = c as Label;

                            /* Note: EMSObservations was defined twice differently, and replaced twice in this code, so it should have just been using the 2nd implementation.
                             * However; we do need to figure out which one is correct.
                             */
                            lbl.Text = lbl.Text
                                .Replace("{Date}", a.StartDate.ToShortDateString() + " - " + a.EndDate.ToShortDateString())
                                .Replace("{Organization}", a.Organization.Name)
                                .Replace("{Findings}", countFindings)
                                .Replace("{EMSObservation}", countEMSObservations_B)
                                .Replace("{Regulatory}", countRegulatory)
                                .Replace("{Policy}", countPolicy)
                                .Replace("{EMSRecommendation}", countEMSRecommendations)
                                .Replace("{EMSObservation}", countEMSObservations_B)
                                .Replace("{EMSNoteworthyPractice}", countEMSNoteworthyPractice)
                                .Replace("{ComplianceRecommendations}", countComplianceRecommendations)
                                .Replace("{CompliancePositiveObservations}", countCompliancePositiveObservations)
                                .Replace("{ComplianceNoteworthyPractices}", countComplianceNoteworthyPractices)
                                .Replace("{ComplianceObservation}", countComplianceObservation)
                                .Replace("{Opportunity}", countOpportunity)
                                .Replace("{Minor}", countMinor)
                                //.Replace("{Major}", findings.Where(w => w.Category != null && w.Category.ToString() == "Major" && w.FindingType != null && w.FindingType.ToString() == "EMS").Count().ToString() );
                                .Replace("{Major}", countMajor);
                        }
                    }
                }

                //chartControl1.Titles["Compliance Findings"].Text = DateTime.Now.Year + " Internal Audit Compliance Findings";

                var series1 = new Series();
                series1.AxisX = chart_internal_audit_compliance_findings.ChartAreas[0].Axes["AxisX"];
                series1.AxisY = chart_internal_audit_compliance_findings.ChartAreas[0].Axes["AxisY"];
                series1.ChartArea = chart_internal_audit_compliance_findings.ChartAreas[0];
                series1.Name = "Compliance Findings";

                series1.Properties = new CustomProperties(new[]
                {
                    new KeyValuePair("BorderLine", new Line()),
                    new KeyValuePair("ExplodeFactor", 0.03F, new NumericRangeValidator(0, 1, true)),
                    new KeyValuePair("HoleSize", 0.3F, new NumericRangeValidator(0, 1, true)),
                    new KeyValuePair("Marker",
                        new Marker(10, MarkerStyle.None,
                            new Backdrop(BackdropStyle.Transparent, Color.White, Color.Black, GradientType.Vertical,
                                HatchStyle.DottedGrid, null, PicturePutStyle.Stretched),
                            new Line(),
                            new LabelInfo(new Line(Color.Black,
                                LineStyle.None), new Backdrop(BackdropStyle.Transparent,
                                    Color.White, Color.Black, GradientType.Vertical, HatchStyle.DottedGrid,
                                    null, PicturePutStyle.Stretched), new FontInfo(), "{Value}",
                                Alignment.Bottom)))
                });
                series1.Type = ChartType.Doughnut;

                DataPoint point = null;
                Color color;
                var r = new Random(a.Id);

                foreach (var media in medias.OrderBy(o => o.Abbreviation))
                {
                    double compliance =
                        findings.Where(
                            w =>
                                w.Media.Contains(media) && w.FindingType != null &&
                                w.FindingType.ToString() == "Compliance").Count();

                    if (compliance > 0)
                    {
                        point = new DataPoint();
                        point.XValue = media.Name;
                        var m = new Marker();
                        m.Label = new LabelInfo();
                        m.Label.Format = media.Name + ": " + compliance;
                        point.Marker = m;
                        point.LegendText = media.Name;
                        point.YValues = new DoubleArray(new[] { compliance });
                        color = Color.FromArgb(r.Next(0, 255), r.Next(0, 255), r.Next(0, 255));
                        point.Backdrop = new BackdropItem(color);
                        series1.Points.Add(point);
                    }
                }

                chart_internal_audit_compliance_findings.Series.Clear();
                chart_internal_audit_compliance_findings.Series.AddRange(new[] { series1 });

                // Compliance Findings by ISO 14001
                double maxValue = 5;

                var l2 = new Legend();
                l2.Alignment = Alignment.Bottom;
                l2.Backdrop = new BackdropItem(BackdropStyle.Solid, Color.LightGray, Color.White, GradientType.Vertical, HatchStyle.DottedGrid, null, PicturePutStyle.Stretched);
                l2.Border = new Border(new Line(Color.Black, 2), 0, Color.Black);
                l2.DockArea = chartControl3.ChartAreas[0];
                l2.LabelsFont = new FontInfo(Color.Black, new Font("Arial", 9F));

                var references = new ReferenceDao(Session).GetAll(ReferenceType.Iso14001, M3Context.Current.Organization, M3Context.Current.User.CurrentRecursive);

                foreach (var iso in references)
                {
                    double emsFindings = findings.Count(w => w.FindingType != null && w.Iso14001 == iso);

                    //if (emsFindings > 0)
                    //{
                    var datapoint = new DataPoint();
                    var xvalue = iso.ToString();

                    if (!string.IsNullOrEmpty(iso.Abbreviation))
                        xvalue = iso.Abbreviation;
                    else if (iso.ToString().Length > 5)
                        xvalue = iso.ToString().Substring(0, iso.ToString().IndexOf(' '));

                    datapoint.XValue = xvalue;
                    datapoint.YValues = new DoubleArray(new[] { emsFindings });

                    chartControl3.Series["Findings"].Points.Add(datapoint);

                    var li = new LegendItem();
                    li.Backdrop = new BackdropItem(BackdropStyle.Transparent, Color.Transparent, Color.Transparent, GradientType.Vertical, HatchStyle.DottedGrid, null, PicturePutStyle.Stretched);
                    li.Text = iso.ToString();
                    li.Border = new Border(new Line(Color.Transparent));

                    l2.LegendItems.Add(li);

                    if (emsFindings > maxValue)
                        maxValue = emsFindings + 1;
                    //}
                }

                chartControl3.Legends.Add(l2);
                chartControl3.Series["Findings"].Legend = l2;
                chartControl3.ChartAreas[0].Axes["AxisY"].Min = 0;
                chartControl3.ChartAreas[0].Axes["AxisY"].Max = maxValue;
                chartControl3.ChartAreas[0].Axes["AxisY"].MajorTick.Step = 1;

                var rpt1 = new ExternalAuditNonConformanceForm();
                rpt1.DataSource = rpt1.FilterDataSource(findings.OrderByDescending(o => o.Iso14001).ToList(), "");
                subReport1.Report = rpt1;

                var rpt2 = new ExternalComplianceDeficiencyForm();
                rpt2.DataSource = rpt2.FilterDataSource(findings.OrderBy(o => o.Media.ConcatToString()).ToList(), "");
                subReport2.Report = rpt2;

                var rpt3 = new ExternalAuditNoteworthyForm();
                rpt3.DataSource = rpt3.FilterDataSource(findings.OrderBy(o => o.Media.ConcatToString()).ToList(), "");
                subReport3.Report = rpt3;

                var rpt4 = new ExternalAuditComplianceChecklist();
                rpt4.DataSource = rpt4.FilterDataSource(a.AuditTemplates.ToList(), "");
                subComplianceChecklists.Report = rpt4;

                var rpt5 = new ExternalAuditFindingSummary();
                rpt5.DataSource = rpt5.FilterDataSource(findings.ToList(), "");
                subReport5.Report = rpt5;

                var rptMediaFindings = new InternalAuditReport_FindingsCountMedia(findings);
                rptMediaFindings.DataSource = rptMediaFindings.FilterDataSource(medias.ToList(), "");
                subComplianceMediaFindings.Report = rptMediaFindings;

                var rptFindingsMediaCount = new InternalAuditReport_FindingsCountMedia(findings);
                rptFindingsMediaCount.DataSource = rptFindingsMediaCount.FilterDataSource(medias.ToList(), "");
                subReport4.Report = rptFindingsMediaCount;

                //var isos = findings.Select(s => s.Iso14001).Distinct();
                var isos = new ReferenceDao(Session).GetAll(ReferenceType.Iso14001, a.Organization, true).OrderBy(x => x.Abbreviation);
                var rptFindingsMediaISO = new InternalAuditReport_FindingsCountISO(findings);
                rptFindingsMediaISO.DataSource = rptFindingsMediaISO.FilterDataSource(isos.ToList(), "");
                rptFindingsMediaISO.Audit = a;
                subReport6.Report = rptFindingsMediaISO;

                var rptFindingsMediaISOCategory = new InternalAuditReport_FindingsCountISOCategory(findings);
                rptFindingsMediaISOCategory.DataSource = rptFindingsMediaISOCategory.FilterDataSource(isos.ToList(), "");
                rptFindingsMediaISOCategory.Audit = a;
                subReport7.Report = rptFindingsMediaISOCategory;

                //var rpt3 = new ExternalAuditFindingOutbrief2();
                //rpt3.DataSource = rpt3.FilterDataSource(new List<Audit>() { a }, "");
                //subReport3.Report = rpt3;

                idx++;
            }
        }
    }
}