using System;
using System.Drawing;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Drg.M3.Domain.QA;
using Drg.M3.Bll.Reports;
using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Controls;
using GrapeCity.ActiveReports.SectionReportModel;
using GrapeCity.ActiveReports.Document.Section;
using GrapeCity.ActiveReports.Document;

namespace Drg.M3.Reports.QA
{
    /// <summary>
    /// Summary description for ExternalAuditFindingOutbrief.
    /// </summary>
    [Report("External Audit Summary of Findings for Outbrief", "Default", typeof(Domain.QA.Finding), typeof(Modules.QA))]
    public partial class ExternalAuditFindingOutbrief : GrapeCity.ActiveReports.SectionReport, IFilterableReport
    {

        public ExternalAuditFindingOutbrief()
        {
            //
            // Required for Windows Form Designer support
            //
            InitializeComponent();
        }

        int idx = 0;
        private void detail_Format(object sender, EventArgs e)
        {
            if (this.ListDataSource().Count > 0)
            {
                var f = this.ListDataSource()[idx] as Domain.QA.Finding;
                Media.Text = f.Media.ConcatToString();

                lblSummary.Visible = Summary.Visible = !string.IsNullOrWhiteSpace(f.OtherComments);

                idx++;
            }
        }

        public IList FilterDataSource(IList input, string filter)
        {
            return input.OfType<Domain.QA.Finding>().Where(f => !f.IsObservation)
                .OrderByDescending(f => f.FindingType)
                .ThenBy(f => f.Media.FirstOrDefault())
                .ThenBy(f => f.Category)
                .ToList();
        }

    }
}
