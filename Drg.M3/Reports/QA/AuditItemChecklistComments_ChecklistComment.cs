using System;
using System.Drawing;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using Drg.M3.Bll.Reports;
using Drg.M3.Domain.QA;
using System.Linq;
using Drg.M3.Bll.QA;
using GrapeCity.ActiveReports;
using GrapeCity.ActiveReports.Controls;
using GrapeCity.ActiveReports.SectionReportModel;
using GrapeCity.ActiveReports.Document.Section;
using GrapeCity.ActiveReports.Document;

namespace Drg.M3.Reports.QA
{
    /// <summary>
    /// Summary description for ExternalComplianceDeficiencyForm.
    /// </summary>
    [Report("Comments from this Checklist - Checklist Subreport", "Default", typeof(Domain.QA.Finding), typeof(Modules.QA))]
    public partial class AuditItemChecklistComments_ChecklistComment : GrapeCity.ActiveReports.SectionReport
    {
        int idx = 0;

        public AuditItemChecklistComments_ChecklistComment()
        {
            //
            // Required for Windows Form Designer support
            //
            InitializeComponent();

            detail.Format += new EventHandler(detail_Format);
        }

        void detail_Format(object sender, EventArgs e)
        {
            if (this.ListDataSource().Count > 0)
            {
                var comments = this.ListDataSource()[idx] as Tuple<Inspection, ChecklistQuestion, string>;
                answer.Text = ChecklistBll.GetAnswer(comments.Item1, comments.Item1.Answers.GetSafe(comments.Item2.Id, ChecklistAnswer.NA));

                idx++;
            }
        }
    }
}
