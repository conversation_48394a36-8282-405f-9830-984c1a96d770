using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Collections;
using GrapeCity.ActiveReports;

namespace Drg.M3.Reports
{
    public static class Extensions
    {
        public static IList ListDataSource(this SectionReport report)
        {
            if (report.DataSource == null)
                return new List<object>();

            if (report.DataSource is IList)
                return report.DataSource as IList;

            if (report.DataSource is ArrayList)
                return (report.DataSource as ArrayList).ToArray().ToList();

            if (report.DataSource is object[])
                return (report.DataSource as object[]).ToList();

            //if (report.DataSource is IEnumerable)
            //    return (report.DataSource as IEnumerable).ToList();

            return new List<object>() { report.DataSource };
        }
    }
}
