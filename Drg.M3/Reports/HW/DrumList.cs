using System;
using System.Drawing;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using GrapeCity.ActiveReports;
using System.Linq;
using Drg.M3.Domain.HW;
using Drg.M3.Bll.Reports;

namespace Drg.M3.Reports.HW
{
    /// <summary>
    /// Summary description for PickupReceipt.
    /// </summary>
    [Report("Drum List", typeof(Drg.M3.Domain.HW.Form1348), typeof(Modules.HW))]
    public partial class DrumList : GrapeCity.ActiveReports.SectionReport, IFilterableReport
    {

        public DrumList()
        {
            //
            // Required for Windows Form Designer support
            //
            InitializeComponent();

            PageSettings.Margins.Top =
                PageSettings.Margins.Bottom =
                PageSettings.Margins.Left =
                PageSettings.Margins.Right = .5f;

        }

        int idx = 0;
        private void detail_Format(object sender, EventArgs e)
        {
            idx++;
        }

        public IList FilterDataSource(IList input, string filter)
        {
            return input.Cast<Domain.HW.Form1348>().Select(x => new Model()
            {
                Record = x,
                DrumNumbers = x.ContainerID == null ? null : x.ContainerID.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()).ToList()
            }).ToList();
        }

        class Model
        {
            public Domain.HW.Form1348 Record { get; set; }
            public List<string> DrumNumbers { get; set; }
            public string DrumNumbersStr
            {
                get
                {
                    return string.Join("   ", DrumNumbers);
                }
            }
            public int DrumCount { get { return DrumNumbers.Count; } }
            public string TechnicalContact
            {
                get
                {
                    if (Record.Organization == null)
                        return "";
                    
                    var p = Record.Organization.Company.PrimaryPerson;
                    if (p == null) return null;
                    return p.FullName + (p.DefaultPersonCompany == null ? null : ", " + p.DefaultPersonCompany.Title)
                        + (p.w_DefaultPhone == null ? null : " - " + p.w_DefaultPhone);
                }
            }
        }

        private void groupHeader1_Format(object sender, EventArgs e)
        {
            idx = 0;
        }
    }
}
