namespace Drg.M3.Reports.HW
{
    /// <summary>
    /// Summary description for PickupReceipt.
    /// </summary>
    partial class P2ADS
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Resources.ResourceManager resources = new System.Resources.ResourceManager(typeof(P2ADS));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.textBox8 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox3 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox4 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox5 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox6 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox9 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox10 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox1 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox7 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox11 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox12 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label6 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label8 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label9 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label10 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.groupHeader1 = new GrapeCity.ActiveReports.SectionReportModel.GroupHeader();
            this.textBox23 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox2 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label2 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label3 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label4 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label5 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label13 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label14 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label15 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label16 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label17 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label11 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line1 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.textBox25 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label12 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label18 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label19 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox26 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label7 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.reportInfo2 = new GrapeCity.ActiveReports.SectionReportModel.ReportInfo();
            this.textBox27 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox28 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label1 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox29 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.groupFooter1 = new GrapeCity.ActiveReports.SectionReportModel.GroupFooter();
            this.line2 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.textBox13 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox14 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox15 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox16 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox17 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox18 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox19 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox20 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox21 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox22 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox24 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.line3 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.pageHeader1 = new GrapeCity.ActiveReports.SectionReportModel.PageHeader();
            this.pageFooter1 = new GrapeCity.ActiveReports.SectionReportModel.PageFooter();
            this.reportInfo1 = new GrapeCity.ActiveReports.SectionReportModel.ReportInfo();
            this.groupHeader2 = new GrapeCity.ActiveReports.SectionReportModel.GroupHeader();
            this.label20 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox30 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.groupFooter2 = new GrapeCity.ActiveReports.SectionReportModel.GroupFooter();
            this.label21 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox31 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.textBox32 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            ((System.ComponentModel.ISupportInitialize)(this.textBox8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.reportInfo2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox28)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.reportInfo1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox31)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox32)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.CanShrink = true;
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.textBox8,
            this.textBox3,
            this.textBox4,
            this.textBox5,
            this.textBox6,
            this.textBox9,
            this.textBox10,
            this.textBox1,
            this.textBox7,
            this.textBox11,
            this.textBox12,
            this.textBox31});
            this.detail.Height = 0.2F;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.detail_Format);
            // 
            // textBox8
            // 
            this.textBox8.CanGrow = false;
            this.textBox8.DataField = "SourceCode";
            this.textBox8.Height = 0.2F;
            this.textBox8.Left = 0F;
            this.textBox8.Name = "textBox8";
            this.textBox8.OutputFormat = resources.GetString("textBox8.OutputFormat");
            this.textBox8.ShrinkToFit = true;
            this.textBox8.Style = "text-align: center; ddo-shrink-to-fit: true";
            this.textBox8.Text = "SourceCode";
            this.textBox8.Top = 0F;
            this.textBox8.Width = 0.5F;
            // 
            // textBox3
            // 
            this.textBox3.CanGrow = false;
            this.textBox3.DataField = "Backlog";
            this.textBox3.Height = 0.2F;
            this.textBox3.Left = 0.5F;
            this.textBox3.Name = "textBox3";
            this.textBox3.OutputFormat = resources.GetString("textBox3.OutputFormat");
            this.textBox3.ShrinkToFit = true;
            this.textBox3.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox3.Text = "Backlog";
            this.textBox3.Top = 0F;
            this.textBox3.Width = 0.9450001F;
            // 
            // textBox4
            // 
            this.textBox4.CanGrow = false;
            this.textBox4.DataField = "Stored";
            this.textBox4.Height = 0.2F;
            this.textBox4.Left = 1.445F;
            this.textBox4.Name = "textBox4";
            this.textBox4.OutputFormat = resources.GetString("textBox4.OutputFormat");
            this.textBox4.ShrinkToFit = true;
            this.textBox4.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox4.Text = "Stored";
            this.textBox4.Top = 0F;
            this.textBox4.Width = 0.9450001F;
            // 
            // textBox5
            // 
            this.textBox5.CanGrow = false;
            this.textBox5.DataField = "LbsRecycledOnsite";
            this.textBox5.Height = 0.2F;
            this.textBox5.Left = 2.39F;
            this.textBox5.Name = "textBox5";
            this.textBox5.OutputFormat = resources.GetString("textBox5.OutputFormat");
            this.textBox5.ShrinkToFit = true;
            this.textBox5.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox5.Text = "LbsRecycledOnsite";
            this.textBox5.Top = 0F;
            this.textBox5.Width = 0.8820002F;
            // 
            // textBox6
            // 
            this.textBox6.CanGrow = false;
            this.textBox6.DataField = "LbsRecycledOffsite";
            this.textBox6.Height = 0.2F;
            this.textBox6.Left = 3.272F;
            this.textBox6.Name = "textBox6";
            this.textBox6.OutputFormat = resources.GetString("textBox6.OutputFormat");
            this.textBox6.ShrinkToFit = true;
            this.textBox6.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox6.Text = "LbsRecycledOffsite";
            this.textBox6.Top = 0F;
            this.textBox6.Width = 0.882F;
            // 
            // textBox9
            // 
            this.textBox9.CanGrow = false;
            this.textBox9.DataField = "LbsTreatedOnsite";
            this.textBox9.Height = 0.2F;
            this.textBox9.Left = 4.154F;
            this.textBox9.Name = "textBox9";
            this.textBox9.OutputFormat = resources.GetString("textBox9.OutputFormat");
            this.textBox9.ShrinkToFit = true;
            this.textBox9.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox9.Text = "LbsTreatedOnsite";
            this.textBox9.Top = 0F;
            this.textBox9.Width = 0.882F;
            // 
            // textBox10
            // 
            this.textBox10.CanGrow = false;
            this.textBox10.DataField = "LbsTreatedOffsite";
            this.textBox10.Height = 0.2F;
            this.textBox10.Left = 5.036F;
            this.textBox10.Name = "textBox10";
            this.textBox10.OutputFormat = resources.GetString("textBox10.OutputFormat");
            this.textBox10.ShrinkToFit = true;
            this.textBox10.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox10.Text = "LbsTreatedOffsite";
            this.textBox10.Top = 0F;
            this.textBox10.Width = 0.882F;
            // 
            // textBox1
            // 
            this.textBox1.CanGrow = false;
            this.textBox1.DataField = "LbsDisposedOnsite";
            this.textBox1.Height = 0.2F;
            this.textBox1.Left = 5.918F;
            this.textBox1.Name = "textBox1";
            this.textBox1.OutputFormat = resources.GetString("textBox1.OutputFormat");
            this.textBox1.ShrinkToFit = true;
            this.textBox1.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox1.Text = "LbsDisposedOnsite";
            this.textBox1.Top = 0F;
            this.textBox1.Width = 0.882F;
            // 
            // textBox7
            // 
            this.textBox7.CanGrow = false;
            this.textBox7.DataField = "LbsDisposedOffsite";
            this.textBox7.Height = 0.2F;
            this.textBox7.Left = 6.805F;
            this.textBox7.Name = "textBox7";
            this.textBox7.OutputFormat = resources.GetString("textBox7.OutputFormat");
            this.textBox7.ShrinkToFit = true;
            this.textBox7.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox7.Text = "LbsDisposedOffsite";
            this.textBox7.Top = 0F;
            this.textBox7.Width = 0.882F;
            // 
            // textBox11
            // 
            this.textBox11.CanGrow = false;
            this.textBox11.CurrencyCulture = new System.Globalization.CultureInfo("en-US");
            this.textBox11.DataField = "TotalCost";
            this.textBox11.Height = 0.2F;
            this.textBox11.Left = 8.516001F;
            this.textBox11.Name = "textBox11";
            this.textBox11.OutputFormat = resources.GetString("textBox11.OutputFormat");
            this.textBox11.ShrinkToFit = true;
            this.textBox11.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox11.Text = "TotalCost";
            this.textBox11.Top = 0F;
            this.textBox11.Width = 0.9899998F;
            // 
            // textBox12
            // 
            this.textBox12.CanGrow = false;
            this.textBox12.DataField = "Generated";
            this.textBox12.Height = 0.2F;
            this.textBox12.Left = 9.502001F;
            this.textBox12.Name = "textBox12";
            this.textBox12.OutputFormat = resources.GetString("textBox12.OutputFormat");
            this.textBox12.ShrinkToFit = true;
            this.textBox12.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox12.Text = "Generated";
            this.textBox12.Top = 0F;
            this.textBox12.Width = 0.9979992F;
            // 
            // label6
            // 
            this.label6.Height = 0.4F;
            this.label6.HyperLink = null;
            this.label6.Left = 0.006000001F;
            this.label6.Name = "label6";
            this.label6.Style = "font-weight: bold; text-align: center; vertical-align: bottom";
            this.label6.Text = "Source Code";
            this.label6.Top = 0.8770001F;
            this.label6.Width = 0.5F;
            // 
            // label8
            // 
            this.label8.Height = 0.4F;
            this.label8.HyperLink = null;
            this.label8.Left = 0.506F;
            this.label8.Name = "label8";
            this.label8.Style = "font-weight: bold; text-align: right; vertical-align: bottom";
            this.label8.Text = "Backlog";
            this.label8.Top = 0.8770001F;
            this.label8.Width = 0.9450045F;
            // 
            // label9
            // 
            this.label9.Height = 0.4F;
            this.label9.HyperLink = null;
            this.label9.Left = 1.451005F;
            this.label9.Name = "label9";
            this.label9.Style = "font-weight: bold; text-align: right; vertical-align: bottom";
            this.label9.Text = "Stored";
            this.label9.Top = 0.8770001F;
            this.label9.Width = 0.9450045F;
            // 
            // label10
            // 
            this.label10.Height = 0.397F;
            this.label10.HyperLink = null;
            this.label10.Left = 8.522F;
            this.label10.Name = "label10";
            this.label10.Style = "font-weight: bold; text-align: right; vertical-align: bottom";
            this.label10.Text = "Total Cost";
            this.label10.Top = 0.8770001F;
            this.label10.Width = 0.9920416F;
            // 
            // groupHeader1
            // 
            this.groupHeader1.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.label6,
            this.label8,
            this.label9,
            this.label10,
            this.textBox23,
            this.textBox2,
            this.label2,
            this.label3,
            this.label4,
            this.label5,
            this.label13,
            this.label14,
            this.label15,
            this.label16,
            this.label17,
            this.label11,
            this.line1,
            this.textBox25,
            this.label12,
            this.label18,
            this.label19,
            this.textBox26,
            this.label7,
            this.reportInfo2,
            this.textBox27,
            this.textBox28,
            this.label1,
            this.textBox29,
            this.label21});
            this.groupHeader1.DataField = "GroupKey";
            this.groupHeader1.Height = 1.274F;
            this.groupHeader1.KeepTogether = true;
            this.groupHeader1.Name = "groupHeader1";
            this.groupHeader1.NewPage = GrapeCity.ActiveReports.SectionReportModel.NewPage.Before;
            this.groupHeader1.Format += new System.EventHandler(this.groupHeader1_Format);
            // 
            // textBox23
            // 
            this.textBox23.DataField = "OrganizationName";
            this.textBox23.Height = 0.207F;
            this.textBox23.Left = 2.501F;
            this.textBox23.Name = "textBox23";
            this.textBox23.Style = "font-size: 12pt; font-weight: bold; text-decoration: none; ddo-char-set: 1";
            this.textBox23.Text = "OrganizationName";
            this.textBox23.Top = 0.473F;
            this.textBox23.Width = 5.562F;
            // 
            // textBox2
            // 
            this.textBox2.DataField = "OrganizationUIC";
            this.textBox2.Height = 0.207F;
            this.textBox2.Left = 1.126F;
            this.textBox2.Name = "textBox2";
            this.textBox2.Style = "font-size: 12pt; font-weight: bold; text-decoration: none; ddo-char-set: 1";
            this.textBox2.Text = "OrganizationUIC";
            this.textBox2.Top = 0.473F;
            this.textBox2.Width = 1.375F;
            // 
            // label2
            // 
            this.label2.Height = 0.2F;
            this.label2.HyperLink = null;
            this.label2.Left = 2.818F;
            this.label2.Name = "label2";
            this.label2.Style = "font-weight: bold; text-align: center";
            this.label2.Text = "Recycled";
            this.label2.Top = 0.8800001F;
            this.label2.Width = 1.342F;
            // 
            // label3
            // 
            this.label3.Height = 0.2F;
            this.label3.HyperLink = null;
            this.label3.Left = 2.396009F;
            this.label3.Name = "label3";
            this.label3.Style = "font-weight: bold; text-align: right";
            this.label3.Text = "Onsite";
            this.label3.Top = 1.077F;
            this.label3.Width = 0.8820044F;
            // 
            // label4
            // 
            this.label4.Height = 0.2F;
            this.label4.HyperLink = null;
            this.label4.Left = 3.278013F;
            this.label4.Name = "label4";
            this.label4.Style = "font-weight: bold; text-align: right";
            this.label4.Text = "Offsite";
            this.label4.Top = 1.074F;
            this.label4.Width = 0.8820044F;
            // 
            // label5
            // 
            this.label5.Height = 0.2F;
            this.label5.HyperLink = null;
            this.label5.Left = 4.582F;
            this.label5.Name = "label5";
            this.label5.Style = "font-weight: bold; text-align: center";
            this.label5.Text = "Treated";
            this.label5.Top = 0.8800001F;
            this.label5.Width = 1.342F;
            // 
            // label13
            // 
            this.label13.Height = 0.2F;
            this.label13.HyperLink = null;
            this.label13.Left = 4.160018F;
            this.label13.Name = "label13";
            this.label13.Style = "font-weight: bold; text-align: right";
            this.label13.Text = "Onsite";
            this.label13.Top = 1.08F;
            this.label13.Width = 0.8820045F;
            // 
            // label14
            // 
            this.label14.Height = 0.2F;
            this.label14.HyperLink = null;
            this.label14.Left = 5.042022F;
            this.label14.Name = "label14";
            this.label14.Style = "font-weight: bold; text-align: right";
            this.label14.Text = "Offsite";
            this.label14.Top = 1.077F;
            this.label14.Width = 0.8820045F;
            // 
            // label15
            // 
            this.label15.Height = 0.2F;
            this.label15.HyperLink = null;
            this.label15.Left = 6.346F;
            this.label15.Name = "label15";
            this.label15.Style = "font-weight: bold; text-align: center";
            this.label15.Text = "Disposed";
            this.label15.Top = 0.8770001F;
            this.label15.Width = 1.342F;
            // 
            // label16
            // 
            this.label16.Height = 0.2F;
            this.label16.HyperLink = null;
            this.label16.Left = 5.924027F;
            this.label16.Name = "label16";
            this.label16.Style = "font-weight: bold; text-align: right";
            this.label16.Text = "Onsite";
            this.label16.Top = 1.08F;
            this.label16.Width = 0.8820045F;
            // 
            // label17
            // 
            this.label17.Height = 0.2F;
            this.label17.HyperLink = null;
            this.label17.Left = 6.806032F;
            this.label17.Name = "label17";
            this.label17.Style = "font-weight: bold; text-align: right";
            this.label17.Text = "Offsite";
            this.label17.Top = 1.077F;
            this.label17.Width = 0.8820045F;
            // 
            // label11
            // 
            this.label11.Height = 0.397F;
            this.label11.HyperLink = null;
            this.label11.Left = 9.506001F;
            this.label11.Name = "label11";
            this.label11.Style = "font-weight: bold; text-align: right; vertical-align: bottom";
            this.label11.Text = "Generated";
            this.label11.Top = 0.8770001F;
            this.label11.Width = 1.000005F;
            // 
            // line1
            // 
            this.line1.Height = 0F;
            this.line1.Left = 0.006000001F;
            this.line1.LineWeight = 1F;
            this.line1.Name = "line1";
            this.line1.Top = 1.274F;
            this.line1.Width = 10.494F;
            this.line1.X1 = 0.006000001F;
            this.line1.X2 = 10.5F;
            this.line1.Y1 = 1.274F;
            this.line1.Y2 = 1.274F;
            // 
            // textBox25
            // 
            this.textBox25.DataField = "DodScopeStr";
            this.textBox25.Height = 0.2F;
            this.textBox25.Left = 0.562F;
            this.textBox25.Name = "textBox25";
            this.textBox25.Style = "font-weight: normal";
            this.textBox25.Text = "DodScopeStr";
            this.textBox25.Top = 0.68F;
            this.textBox25.Width = 1.125F;
            // 
            // label12
            // 
            this.label12.Height = 0.207F;
            this.label12.HyperLink = null;
            this.label12.Left = 0.001F;
            this.label12.Name = "label12";
            this.label12.Style = "font-size: 12pt; font-weight: bold";
            this.label12.Text = "Organization:";
            this.label12.Top = 0.473F;
            this.label12.Width = 1.125F;
            // 
            // label18
            // 
            this.label18.Height = 0.2F;
            this.label18.HyperLink = null;
            this.label18.Left = 0.001F;
            this.label18.Name = "label18";
            this.label18.Style = "";
            this.label18.Text = "Service:";
            this.label18.Top = 0.6800001F;
            this.label18.Width = 0.561F;
            // 
            // label19
            // 
            this.label19.Height = 0.2F;
            this.label19.HyperLink = null;
            this.label19.Left = 1.73F;
            this.label19.Name = "label19";
            this.label19.Style = "text-align: right";
            this.label19.Text = "Generated:";
            this.label19.Top = 0.68F;
            this.label19.Width = 0.7709999F;
            // 
            // textBox26
            // 
            this.textBox26.DataField = "OffsiteStr";
            this.textBox26.Height = 0.2F;
            this.textBox26.Left = 2.501F;
            this.textBox26.Name = "textBox26";
            this.textBox26.Style = "font-weight: normal";
            this.textBox26.Text = "OffsiteStr";
            this.textBox26.Top = 0.68F;
            this.textBox26.Width = 0.687F;
            // 
            // label7
            // 
            this.label7.Height = 0.273F;
            this.label7.HyperLink = null;
            this.label7.Left = 0F;
            this.label7.Name = "label7";
            this.label7.Style = "font-family: Times New Roman; font-size: 15.75pt; font-weight: bold; text-align: " +
    "center; ddo-char-set: 1";
            this.label7.Text = "HW Operations Summary";
            this.label7.Top = 0F;
            this.label7.Width = 10F;
            // 
            // reportInfo2
            // 
            this.reportInfo2.FormatString = "{RunDateTime:M/d/yyyy}";
            this.reportInfo2.Height = 0.2F;
            this.reportInfo2.Left = 8.125F;
            this.reportInfo2.Name = "reportInfo2";
            this.reportInfo2.Style = "text-align: right";
            this.reportInfo2.Top = 0F;
            this.reportInfo2.Width = 2.375F;
            // 
            // textBox27
            // 
            this.textBox27.DataField = "GeneratorEPAID";
            this.textBox27.Height = 0.2F;
            this.textBox27.Left = 3.188F;
            this.textBox27.Name = "textBox27";
            this.textBox27.Style = "font-weight: normal";
            this.textBox27.Text = "GeneratorEPAID";
            this.textBox27.Top = 0.68F;
            this.textBox27.Width = 1.333F;
            // 
            // textBox28
            // 
            this.textBox28.DataField = "GeneratorName";
            this.textBox28.Height = 0.2F;
            this.textBox28.Left = 4.521F;
            this.textBox28.Name = "textBox28";
            this.textBox28.Style = "font-weight: normal";
            this.textBox28.Text = "GeneratorName";
            this.textBox28.Top = 0.68F;
            this.textBox28.Width = 3.541999F;
            // 
            // label1
            // 
            this.label1.Height = 0.2F;
            this.label1.HyperLink = null;
            this.label1.Left = 0.006F;
            this.label1.Name = "label1";
            this.label1.Style = "";
            this.label1.Text = "Date Range:";
            this.label1.Top = 0.273F;
            this.label1.Width = 1F;
            // 
            // textBox29
            // 
            this.textBox29.Height = 0.2F;
            this.textBox29.Left = 1.006F;
            this.textBox29.Name = "textBox29";
            this.textBox29.Text = "textBox29";
            this.textBox29.Top = 0.273F;
            this.textBox29.Width = 2.369F;
            // 
            // groupFooter1
            // 
            this.groupFooter1.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.textBox32,
            this.textBox13,
            this.textBox14,
            this.textBox15,
            this.textBox16,
            this.textBox17,
            this.textBox18,
            this.textBox19,
            this.textBox20,
            this.textBox21,
            this.textBox22,
            this.textBox24,
            this.line3,
            this.line2});
            this.groupFooter1.Height = 0.2916666F;
            this.groupFooter1.Name = "groupFooter1";
            // 
            // line2
            // 
            this.line2.Height = 0F;
            this.line2.Left = 0F;
            this.line2.LineWeight = 1F;
            this.line2.Name = "line2";
            this.line2.Top = 0F;
            this.line2.Width = 10.506F;
            this.line2.X1 = 0F;
            this.line2.X2 = 10.506F;
            this.line2.Y1 = 0F;
            this.line2.Y2 = 0F;
            // 
            // textBox13
            // 
            this.textBox13.CanGrow = false;
            this.textBox13.Height = 0.2F;
            this.textBox13.Left = 0F;
            this.textBox13.Name = "textBox13";
            this.textBox13.OutputFormat = resources.GetString("textBox13.OutputFormat");
            this.textBox13.ShrinkToFit = true;
            this.textBox13.Style = "font-weight: bold; text-align: center; ddo-shrink-to-fit: true";
            this.textBox13.Text = "Total:";
            this.textBox13.Top = 0F;
            this.textBox13.Width = 0.5F;
            // 
            // textBox14
            // 
            this.textBox14.CanGrow = false;
            this.textBox14.DataField = "Backlog";
            this.textBox14.Height = 0.2F;
            this.textBox14.Left = 0.5F;
            this.textBox14.Name = "textBox14";
            this.textBox14.OutputFormat = resources.GetString("textBox14.OutputFormat");
            this.textBox14.ShrinkToFit = true;
            this.textBox14.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox14.SummaryGroup = "groupHeader1";
            this.textBox14.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox14.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox14.Text = "Backlog";
            this.textBox14.Top = 0F;
            this.textBox14.Width = 0.9450001F;
            // 
            // textBox15
            // 
            this.textBox15.CanGrow = false;
            this.textBox15.DataField = "Stored";
            this.textBox15.Height = 0.2F;
            this.textBox15.Left = 1.445F;
            this.textBox15.Name = "textBox15";
            this.textBox15.OutputFormat = resources.GetString("textBox15.OutputFormat");
            this.textBox15.ShrinkToFit = true;
            this.textBox15.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox15.SummaryGroup = "groupHeader1";
            this.textBox15.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox15.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox15.Text = "Stored";
            this.textBox15.Top = 0F;
            this.textBox15.Width = 0.9450001F;
            // 
            // textBox16
            // 
            this.textBox16.CanGrow = false;
            this.textBox16.DataField = "LbsRecycledOnsite";
            this.textBox16.Height = 0.2F;
            this.textBox16.Left = 2.39F;
            this.textBox16.Name = "textBox16";
            this.textBox16.OutputFormat = resources.GetString("textBox16.OutputFormat");
            this.textBox16.ShrinkToFit = true;
            this.textBox16.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox16.SummaryGroup = "groupHeader1";
            this.textBox16.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox16.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox16.Text = "LbsRecycledOnsite";
            this.textBox16.Top = 0F;
            this.textBox16.Width = 0.882F;
            // 
            // textBox17
            // 
            this.textBox17.CanGrow = false;
            this.textBox17.DataField = "LbsRecycledOffsite";
            this.textBox17.Height = 0.2F;
            this.textBox17.Left = 3.272F;
            this.textBox17.Name = "textBox17";
            this.textBox17.OutputFormat = resources.GetString("textBox17.OutputFormat");
            this.textBox17.ShrinkToFit = true;
            this.textBox17.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox17.SummaryGroup = "groupHeader1";
            this.textBox17.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox17.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox17.Text = "LbsRecycledOffsite";
            this.textBox17.Top = 0F;
            this.textBox17.Width = 0.882F;
            // 
            // textBox18
            // 
            this.textBox18.CanGrow = false;
            this.textBox18.DataField = "LbsTreatedOnsite";
            this.textBox18.Height = 0.2F;
            this.textBox18.Left = 4.154F;
            this.textBox18.Name = "textBox18";
            this.textBox18.OutputFormat = resources.GetString("textBox18.OutputFormat");
            this.textBox18.ShrinkToFit = true;
            this.textBox18.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox18.SummaryGroup = "groupHeader1";
            this.textBox18.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox18.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox18.Text = "LbsTreatedOnsite";
            this.textBox18.Top = 0F;
            this.textBox18.Width = 0.882F;
            // 
            // textBox19
            // 
            this.textBox19.CanGrow = false;
            this.textBox19.DataField = "LbsTreatedOffsite";
            this.textBox19.Height = 0.2F;
            this.textBox19.Left = 5.036F;
            this.textBox19.Name = "textBox19";
            this.textBox19.OutputFormat = resources.GetString("textBox19.OutputFormat");
            this.textBox19.ShrinkToFit = true;
            this.textBox19.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox19.SummaryGroup = "groupHeader1";
            this.textBox19.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox19.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox19.Text = "LbsTreatedOffsite";
            this.textBox19.Top = 0F;
            this.textBox19.Width = 0.882F;
            // 
            // textBox20
            // 
            this.textBox20.CanGrow = false;
            this.textBox20.DataField = "LbsDisposedOnsite";
            this.textBox20.Height = 0.2F;
            this.textBox20.Left = 5.918F;
            this.textBox20.Name = "textBox20";
            this.textBox20.OutputFormat = resources.GetString("textBox20.OutputFormat");
            this.textBox20.ShrinkToFit = true;
            this.textBox20.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox20.SummaryGroup = "groupHeader1";
            this.textBox20.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox20.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox20.Text = "LbsDisposedOnsite";
            this.textBox20.Top = 0F;
            this.textBox20.Width = 0.882F;
            // 
            // textBox21
            // 
            this.textBox21.CanGrow = false;
            this.textBox21.DataField = "LbsDisposedOffsite";
            this.textBox21.Height = 0.2F;
            this.textBox21.Left = 6.805F;
            this.textBox21.Name = "textBox21";
            this.textBox21.OutputFormat = resources.GetString("textBox21.OutputFormat");
            this.textBox21.ShrinkToFit = true;
            this.textBox21.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox21.SummaryGroup = "groupHeader1";
            this.textBox21.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox21.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox21.Text = "LbsDisposedOffsite";
            this.textBox21.Top = 0F;
            this.textBox21.Width = 0.882F;
            // 
            // textBox22
            // 
            this.textBox22.CanGrow = false;
            this.textBox22.CurrencyCulture = new System.Globalization.CultureInfo("en-US");
            this.textBox22.DataField = "TotalCost";
            this.textBox22.Height = 0.2F;
            this.textBox22.Left = 8.516001F;
            this.textBox22.Name = "textBox22";
            this.textBox22.OutputFormat = resources.GetString("textBox22.OutputFormat");
            this.textBox22.ShrinkToFit = true;
            this.textBox22.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox22.SummaryGroup = "groupHeader1";
            this.textBox22.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox22.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox22.Text = "TotalCost";
            this.textBox22.Top = 0F;
            this.textBox22.Width = 0.9899998F;
            // 
            // textBox24
            // 
            this.textBox24.CanGrow = false;
            this.textBox24.DataField = "Generated";
            this.textBox24.Height = 0.2F;
            this.textBox24.Left = 9.502F;
            this.textBox24.Name = "textBox24";
            this.textBox24.OutputFormat = resources.GetString("textBox24.OutputFormat");
            this.textBox24.ShrinkToFit = true;
            this.textBox24.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox24.SummaryGroup = "groupHeader1";
            this.textBox24.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox24.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox24.Text = "Generated";
            this.textBox24.Top = 0F;
            this.textBox24.Width = 0.9979993F;
            // 
            // line3
            // 
            this.line3.Height = 0F;
            this.line3.Left = 0F;
            this.line3.LineWeight = 1F;
            this.line3.Name = "line3";
            this.line3.Top = 0.2F;
            this.line3.Width = 10.5F;
            this.line3.X1 = 0F;
            this.line3.X2 = 10.5F;
            this.line3.Y1 = 0.2F;
            this.line3.Y2 = 0.2F;
            // 
            // pageHeader1
            // 
            this.pageHeader1.Height = 0F;
            this.pageHeader1.Name = "pageHeader1";
            // 
            // pageFooter1
            // 
            this.pageFooter1.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.reportInfo1});
            this.pageFooter1.Name = "pageFooter1";
            // 
            // reportInfo1
            // 
            this.reportInfo1.FormatString = "Page {PageNumber} of {PageCount}";
            this.reportInfo1.Height = 0.2F;
            this.reportInfo1.Left = 0F;
            this.reportInfo1.Name = "reportInfo1";
            this.reportInfo1.Style = "text-align: center";
            this.reportInfo1.Top = 0F;
            this.reportInfo1.Width = 10.5F;
            // 
            // groupHeader2
            // 
            this.groupHeader2.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.label20,
            this.textBox30});
            this.groupHeader2.DataField = "ResponsibleParty";
            this.groupHeader2.Name = "groupHeader2";
            this.groupHeader2.Visible = false;
            // 
            // label20
            // 
            this.label20.Height = 0.2F;
            this.label20.HyperLink = null;
            this.label20.Left = 0.006F;
            this.label20.Name = "label20";
            this.label20.Style = "font-weight: bold";
            this.label20.Text = "Responsible Party:";
            this.label20.Top = 0F;
            this.label20.Width = 1.309F;
            // 
            // textBox30
            // 
            this.textBox30.DataField = "ResponsibleParty";
            this.textBox30.Height = 0.2F;
            this.textBox30.Left = 1.315F;
            this.textBox30.Name = "textBox30";
            this.textBox30.Style = "font-weight: bold";
            this.textBox30.Text = "ResponsibleParty";
            this.textBox30.Top = 0F;
            this.textBox30.Width = 9.191F;
            // 
            // groupFooter2
            // 
            this.groupFooter2.Height = 0F;
            this.groupFooter2.Name = "groupFooter2";
            // 
            // label21
            // 
            this.label21.Height = 0.2F;
            this.label21.HyperLink = null;
            this.label21.Left = 7.688F;
            this.label21.Name = "label21";
            this.label21.Style = "font-weight: bold; text-align: right";
            this.label21.Text = "Unknown";
            this.label21.Top = 1.074F;
            this.label21.Width = 0.8280044F;
            // 
            // textBox31
            // 
            this.textBox31.CanGrow = false;
            this.textBox31.DataField = "LbsUnknown";
            this.textBox31.Height = 0.2F;
            this.textBox31.Left = 7.688F;
            this.textBox31.Name = "textBox31";
            this.textBox31.OutputFormat = resources.GetString("textBox31.OutputFormat");
            this.textBox31.ShrinkToFit = true;
            this.textBox31.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox31.Text = "LbsUnknown";
            this.textBox31.Top = 0F;
            this.textBox31.Width = 0.8280005F;
            // 
            // textBox32
            // 
            this.textBox32.CanGrow = false;
            this.textBox32.DataField = "LbsUnknown";
            this.textBox32.Height = 0.2F;
            this.textBox32.Left = 7.688F;
            this.textBox32.Name = "textBox32";
            this.textBox32.OutputFormat = resources.GetString("textBox32.OutputFormat");
            this.textBox32.ShrinkToFit = true;
            this.textBox32.Style = "text-align: right; ddo-shrink-to-fit: true";
            this.textBox32.SummaryGroup = "groupHeader1";
            this.textBox32.SummaryRunning = GrapeCity.ActiveReports.SectionReportModel.SummaryRunning.Group;
            this.textBox32.SummaryType = GrapeCity.ActiveReports.SectionReportModel.SummaryType.SubTotal;
            this.textBox32.Text = "LbsUnknown";
            this.textBox32.Top = 0F;
            this.textBox32.Width = 0.8280005F;
            // 
            // P2ADS
            // 
            this.MasterReport = false;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 10.5F;
            this.Sections.Add(this.pageHeader1);
            this.Sections.Add(this.groupHeader1);
            this.Sections.Add(this.groupHeader2);
            this.Sections.Add(this.detail);
            this.Sections.Add(this.groupFooter2);
            this.Sections.Add(this.groupFooter1);
            this.Sections.Add(this.pageFooter1);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Arial; font-style: normal; text-decoration: none; font-weight: norma" +
            "l; font-size: 10pt; color: Black", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Times New Roman; font-size: 14pt; font-weight: bold; font-style: ita" +
            "lic", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.textBox8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.reportInfo2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.reportInfo1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox31)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox32)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox8;
        private GrapeCity.ActiveReports.SectionReportModel.Label label6;
        private GrapeCity.ActiveReports.SectionReportModel.Label label8;
        private GrapeCity.ActiveReports.SectionReportModel.Label label9;
        private GrapeCity.ActiveReports.SectionReportModel.Label label10;
        private GrapeCity.ActiveReports.SectionReportModel.GroupHeader groupHeader1;
        private GrapeCity.ActiveReports.SectionReportModel.GroupFooter groupFooter1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label7;
        private GrapeCity.ActiveReports.SectionReportModel.Line line1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox23;
        private GrapeCity.ActiveReports.SectionReportModel.Line line2;
        private GrapeCity.ActiveReports.SectionReportModel.ReportInfo reportInfo2;
        private GrapeCity.ActiveReports.SectionReportModel.PageHeader pageHeader1;
        private GrapeCity.ActiveReports.SectionReportModel.PageFooter pageFooter1;
        private GrapeCity.ActiveReports.SectionReportModel.ReportInfo reportInfo1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label3;
        private GrapeCity.ActiveReports.SectionReportModel.Label label4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label5;
        private GrapeCity.ActiveReports.SectionReportModel.Label label13;
        private GrapeCity.ActiveReports.SectionReportModel.Label label14;
        private GrapeCity.ActiveReports.SectionReportModel.Label label15;
        private GrapeCity.ActiveReports.SectionReportModel.Label label16;
        private GrapeCity.ActiveReports.SectionReportModel.Label label17;
        private GrapeCity.ActiveReports.SectionReportModel.Label label11;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox3;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox4;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox5;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox6;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox9;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox10;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox7;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox11;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox12;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox13;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox14;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox15;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox16;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox17;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox18;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox19;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox20;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox21;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox22;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox24;
        private GrapeCity.ActiveReports.SectionReportModel.Line line3;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox25;
        private GrapeCity.ActiveReports.SectionReportModel.Label label12;
        private GrapeCity.ActiveReports.SectionReportModel.Label label18;
        private GrapeCity.ActiveReports.SectionReportModel.Label label19;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox26;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox27;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox28;
        private GrapeCity.ActiveReports.SectionReportModel.Label label1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox29;
        private GrapeCity.ActiveReports.SectionReportModel.GroupHeader groupHeader2;
        private GrapeCity.ActiveReports.SectionReportModel.GroupFooter groupFooter2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label20;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox30;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox31;
        private GrapeCity.ActiveReports.SectionReportModel.Label label21;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox32;
    }
}
