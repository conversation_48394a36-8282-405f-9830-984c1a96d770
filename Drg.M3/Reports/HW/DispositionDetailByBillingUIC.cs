using System;
using System.Drawing;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using GrapeCity.ActiveReports;
using System.Linq;
using Drg.M3.Domain.HW;
using Drg.M3.Bll.Reports;

namespace Drg.M3.Reports.HW
{
    /// <summary>
    /// Summary description for PickupReceipt.
    /// </summary>
    [Report("Disposition Detail By Billing UIC", typeof(Drg.M3.Domain.HW.WasteRecord), typeof(Modules.HW))]
    public partial class DispositionDetailByBillingUIC : GrapeCity.ActiveReports.SectionReport, IHasDateRange, IFilterableReport
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        public DispositionDetailByBillingUIC()
        {
            //
            // Required for Windows Form Designer support
            //
            InitializeComponent();

            PageSettings.Margins.Top =
                PageSettings.Margins.Bottom =
                PageSettings.Margins.Left =
                PageSettings.Margins.Right = .5f;

        }

        bool alt = true;
        int idx = 0;
        private void detail_Format(object sender, EventArgs e)
        {
            //if (this.ListDataSource().Count > idx)
            //{
            //    var record = this.ListDataSource()[idx] as Model;
               
            //}

            detail.BackColor = alt ? Color.Gainsboro : Color.White;
            idx++;
            alt = !alt;
        }

        public class Model
        {
            public string ResponsibleParty { get; set; }
            public string Organization { get; set; }
            public DateTime RemovalDate { get; set; }
            public string ManifestOrBolNumber { get; set; }
            public string DTID { get; set; }
            public string WasteName { get; set; }
            public decimal TotalLbs { get; set; }
            public decimal TotalCost { get; set; }
        }

        private void groupHeader1_Format(object sender, EventArgs e)
        {
            textBox2.Text =
                (StartDate == null ? "" : StartDate.Value.ToShortDateString()) + " to " +
                (EndDate == null ? "" : EndDate.Value.ToShortDateString());
        }

        private void reportHeader1_Format(object sender, EventArgs e)
        {
            
        }

        private void groupHeader2_Format(object sender, EventArgs e)
        {
            alt = true;
        }

        public IList FilterDataSource(IList input, string filter)
        {
            //This whole group-by is simply to concat multiple DTID's to one row
            return input.OfType<Model>()
                .GroupBy(x => new { x.Organization, x.ResponsibleParty, x.RemovalDate, x.ManifestOrBolNumber, x.WasteName })
                .Select(x => new Model()
                {
                    Organization = x.Key.Organization,
                    ResponsibleParty = x.Key.ResponsibleParty,
                    RemovalDate = x.Key.RemovalDate,
                    ManifestOrBolNumber = x.Key.ManifestOrBolNumber,
                    DTID = string.Join(", ", x.Select(r => r.DTID)),
                    WasteName = x.Key.WasteName,
                    TotalLbs = x.Sum(r => r.TotalLbs),
                    TotalCost = x.Sum(r => r.TotalCost),
                }).OrderBy(x => x.Organization).ThenBy(x => x.ResponsibleParty).ThenBy(x => x.RemovalDate).ThenBy(x => x.WasteName).ToList();
        }
    }
}
