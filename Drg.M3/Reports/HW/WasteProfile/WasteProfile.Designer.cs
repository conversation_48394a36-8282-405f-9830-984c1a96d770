namespace Drg.M3.Reports.HW.WasteProfile
{
    /// <summary>
    /// Summary description for WasteProfile.
    /// </summary>
    partial class WasteProfile
    {
        private GrapeCity.ActiveReports.SectionReportModel.Detail detail;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
            }
            base.Dispose(disposing);
        }

        #region ActiveReport Designer generated code
        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(WasteProfile));
            this.detail = new GrapeCity.ActiveReports.SectionReportModel.Detail();
            this.subReport1 = new GrapeCity.ActiveReports.SectionReportModel.SubReport();
            this.shape1 = new GrapeCity.ActiveReports.SectionReportModel.Shape();
            this.label1 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label2 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label3 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label4 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label5 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.shape2 = new GrapeCity.ActiveReports.SectionReportModel.Shape();
            this.label6 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtWasteCategory = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtSubCategory = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtTypeService = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtDisposition = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.line1 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line2 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line3 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line4 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label7 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line5 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label8 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtGeneratorName = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label9 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line6 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line7 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line8 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line9 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line10 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label10 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtFacilityAddress = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label11 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label12 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtWasteProfileNo = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtGeneratorUsepaId = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtGeneratorStateId = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label13 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label14 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label15 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label16 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtTechnicalContact = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtTitle = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtPhone = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label17 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtZipCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label18 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtNameOfWaste = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label19 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtUsaEpaWasteCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label20 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtStateLocalHostNationWasteCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label21 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtProcessGeneratingWaste = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label22 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtWasteStream = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label23 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtSourceCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label24 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtFormCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label25 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtMgmtCode = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label26 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtProjectedAnnualVolume = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label27 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtModeOfCollection = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label28 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label29 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label30 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label31 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label32 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtReferenceStandards = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label33 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label34 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line11 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label35 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label36 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label37 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label38 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtColor = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtBtuLb = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtDensity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtTotalSolids = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label39 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label40 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkMultiLayered = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkBilayered = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkSinglePhased = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.txtAshContent = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label41 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label42 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtPhysicalStateOther = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label43 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkIgnitable = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkCorrosive = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkReactive = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkToxicityCharacteristic = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label44 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label45 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkCorrosiveSteel = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkHighToc = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.txtFlashPoint = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.chkLowToc = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.txtCorrosivePh = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.chkWaterReactive = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkCyanideReactive = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkSulfideReactive = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.line12 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label46 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label47 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line13 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line14 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label48 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkSolid = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkLiquid = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkSemiSolid = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkGas = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkOther = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label49 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkHazardousMaterialYes = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkHazardousMaterialNo = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label50 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label51 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkStateDesignationEhw = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkStateDesignationDw = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label52 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtUnNaNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label53 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtHazardClass = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label54 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtSubRisk = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label55 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtPackingGroup = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label56 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtProperShippingName = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label57 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtAdditionalDescription = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label58 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkMethodOfShipmentBulk = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkMethodOfShipmentDrum = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkMethodOfShipmentOther = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label59 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtEmergencyResponseNumber = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtMethodOfShipmentOther = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label60 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtCerclaReportableQuantity = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label61 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkWasteRestrictedYes = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkExemptionGrantedYes = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkApplicableTreatmentStandardsYes = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkWasteRestrictedNo = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkExemptionGrantedNo = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkApplicableTreatmentStandardsNo = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label62 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtErGuidePage = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label63 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtErGuideEdition = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label64 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtBaseClins = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label65 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtSpecialHandlingInfo = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.line15 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label66 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label67 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.line16 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label68 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtLab = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtAnalysis = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtMsds = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.line17 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.label69 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label70 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.lblSigningBlock = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label74 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtSignature = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label75 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtDate = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.shape3 = new GrapeCity.ActiveReports.SectionReportModel.Shape();
            this.label76 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.label77 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtFscLsn = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label78 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.chkOdorNone = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkOdorMild = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkOdorStrong = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label79 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtOtherNotes = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.chkDioxinYes = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkDioxinNo = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.line18 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line20 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line19 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.txtFacilityAddress2 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.line22 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.line23 = new GrapeCity.ActiveReports.SectionReportModel.Line();
            this.txtProperShippingNamePrefix = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.txtProperShippingNameSuffix = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label87 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.txtNSN = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label88 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.checkBox1 = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.txtSubRisk2 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.label89 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            this.textBox1 = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.chkCharacteristicOther = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.checkBox2 = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.checkBox3 = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.txtTreatmentGroup = new GrapeCity.ActiveReports.SectionReportModel.TextBox();
            this.chkObodYes = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.chkObodNo = new GrapeCity.ActiveReports.SectionReportModel.CheckBox();
            this.label71 = new GrapeCity.ActiveReports.SectionReportModel.Label();
            ((System.ComponentModel.ISupportInitialize)(this.label1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWasteCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTypeService)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDisposition)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGeneratorName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFacilityAddress)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWasteProfileNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGeneratorUsepaId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGeneratorStateId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTechnicalContact)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTitle)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhone)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZipCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNameOfWaste)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUsaEpaWasteCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStateLocalHostNationWasteCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label21)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProcessGeneratingWaste)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label22)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWasteStream)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label23)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSourceCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label24)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFormCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label25)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMgmtCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label26)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProjectedAnnualVolume)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label27)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtModeOfCollection)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label28)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label29)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label30)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label31)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label32)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReferenceStandards)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label33)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label34)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label35)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label36)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label37)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label38)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtColor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBtuLb)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDensity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotalSolids)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label39)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label40)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMultiLayered)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkBilayered)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSinglePhased)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAshContent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label41)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label42)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhysicalStateOther)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label43)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIgnitable)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCorrosive)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkReactive)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkToxicityCharacteristic)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label44)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label45)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCorrosiveSteel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHighToc)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFlashPoint)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLowToc)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCorrosivePh)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkWaterReactive)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCyanideReactive)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSulfideReactive)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label46)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label47)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label48)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSolid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLiquid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSemiSolid)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkGas)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOther)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label49)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHazardousMaterialYes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHazardousMaterialNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label50)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label51)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkStateDesignationEhw)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkStateDesignationDw)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label52)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUnNaNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label53)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHazardClass)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label54)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubRisk)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label55)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPackingGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label56)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProperShippingName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label57)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAdditionalDescription)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label58)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMethodOfShipmentBulk)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMethodOfShipmentDrum)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMethodOfShipmentOther)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label59)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmergencyResponseNumber)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMethodOfShipmentOther)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label60)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCerclaReportableQuantity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label61)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkWasteRestrictedYes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkExemptionGrantedYes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplicableTreatmentStandardsYes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkWasteRestrictedNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkExemptionGrantedNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplicableTreatmentStandardsNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label62)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtErGuidePage)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label63)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtErGuideEdition)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label64)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBaseClins)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label65)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSpecialHandlingInfo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label66)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label67)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label68)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLab)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAnalysis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMsds)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label69)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label70)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblSigningBlock)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label74)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSignature)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label75)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label76)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label77)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFscLsn)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label78)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOdorNone)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOdorMild)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOdorStrong)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label79)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOtherNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDioxinYes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDioxinNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFacilityAddress2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProperShippingNamePrefix)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProperShippingNameSuffix)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label87)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNSN)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label88)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubRisk2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label89)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCharacteristicOther)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBox2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBox3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTreatmentGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkObodYes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkObodNo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.label71)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // detail
            // 
            this.detail.Controls.AddRange(new GrapeCity.ActiveReports.SectionReportModel.ARControl[] {
            this.subReport1,
            this.shape1,
            this.label1,
            this.label2,
            this.label3,
            this.label4,
            this.label5,
            this.shape2,
            this.label6,
            this.txtWasteCategory,
            this.txtSubCategory,
            this.txtTypeService,
            this.txtDisposition,
            this.line1,
            this.line2,
            this.line3,
            this.line4,
            this.label7,
            this.line5,
            this.label8,
            this.txtGeneratorName,
            this.label9,
            this.line6,
            this.line7,
            this.line8,
            this.line9,
            this.line10,
            this.label10,
            this.txtFacilityAddress,
            this.label11,
            this.label12,
            this.txtWasteProfileNo,
            this.txtGeneratorUsepaId,
            this.txtGeneratorStateId,
            this.label13,
            this.label14,
            this.label15,
            this.label16,
            this.txtTechnicalContact,
            this.txtTitle,
            this.txtPhone,
            this.label17,
            this.txtZipCode,
            this.label18,
            this.txtNameOfWaste,
            this.label19,
            this.txtUsaEpaWasteCode,
            this.label20,
            this.txtStateLocalHostNationWasteCode,
            this.label21,
            this.txtProcessGeneratingWaste,
            this.label22,
            this.txtWasteStream,
            this.label23,
            this.txtSourceCode,
            this.label24,
            this.txtFormCode,
            this.label25,
            this.txtMgmtCode,
            this.label26,
            this.txtProjectedAnnualVolume,
            this.label27,
            this.txtModeOfCollection,
            this.label28,
            this.label29,
            this.label30,
            this.label31,
            this.label32,
            this.txtReferenceStandards,
            this.label33,
            this.label34,
            this.line11,
            this.label35,
            this.label36,
            this.label37,
            this.label38,
            this.txtColor,
            this.txtBtuLb,
            this.txtDensity,
            this.txtTotalSolids,
            this.label39,
            this.label40,
            this.chkMultiLayered,
            this.chkBilayered,
            this.chkSinglePhased,
            this.txtAshContent,
            this.label41,
            this.label42,
            this.txtPhysicalStateOther,
            this.label43,
            this.chkIgnitable,
            this.chkCorrosive,
            this.chkReactive,
            this.chkToxicityCharacteristic,
            this.label44,
            this.label45,
            this.chkCorrosiveSteel,
            this.chkHighToc,
            this.txtFlashPoint,
            this.chkLowToc,
            this.txtCorrosivePh,
            this.chkWaterReactive,
            this.chkCyanideReactive,
            this.chkSulfideReactive,
            this.line12,
            this.label46,
            this.label47,
            this.line13,
            this.line14,
            this.label48,
            this.chkSolid,
            this.chkLiquid,
            this.chkSemiSolid,
            this.chkGas,
            this.chkOther,
            this.label49,
            this.chkHazardousMaterialYes,
            this.chkHazardousMaterialNo,
            this.label50,
            this.label51,
            this.chkStateDesignationEhw,
            this.chkStateDesignationDw,
            this.label52,
            this.txtUnNaNumber,
            this.label53,
            this.txtHazardClass,
            this.label54,
            this.txtSubRisk,
            this.label55,
            this.txtPackingGroup,
            this.label56,
            this.txtProperShippingName,
            this.label57,
            this.txtAdditionalDescription,
            this.label58,
            this.chkMethodOfShipmentBulk,
            this.chkMethodOfShipmentDrum,
            this.chkMethodOfShipmentOther,
            this.label59,
            this.txtEmergencyResponseNumber,
            this.txtMethodOfShipmentOther,
            this.label60,
            this.txtCerclaReportableQuantity,
            this.label61,
            this.chkWasteRestrictedYes,
            this.chkExemptionGrantedYes,
            this.chkApplicableTreatmentStandardsYes,
            this.chkWasteRestrictedNo,
            this.chkExemptionGrantedNo,
            this.chkApplicableTreatmentStandardsNo,
            this.label62,
            this.txtErGuidePage,
            this.label63,
            this.txtErGuideEdition,
            this.label64,
            this.txtBaseClins,
            this.label65,
            this.txtSpecialHandlingInfo,
            this.line15,
            this.label66,
            this.label67,
            this.line16,
            this.label68,
            this.txtLab,
            this.txtAnalysis,
            this.txtMsds,
            this.line17,
            this.label69,
            this.label70,
            this.lblSigningBlock,
            this.label74,
            this.txtSignature,
            this.label75,
            this.txtDate,
            this.shape3,
            this.label76,
            this.label77,
            this.txtFscLsn,
            this.label78,
            this.chkOdorNone,
            this.chkOdorMild,
            this.chkOdorStrong,
            this.label79,
            this.txtOtherNotes,
            this.chkDioxinYes,
            this.chkDioxinNo,
            this.line18,
            this.line20,
            this.line19,
            this.txtFacilityAddress2,
            this.line22,
            this.line23,
            this.txtProperShippingNamePrefix,
            this.txtProperShippingNameSuffix,
            this.label87,
            this.txtNSN,
            this.label88,
            this.checkBox1,
            this.txtSubRisk2,
            this.label89,
            this.textBox1,
            this.chkCharacteristicOther,
            this.checkBox2,
            this.checkBox3,
            this.txtTreatmentGroup,
            this.chkObodYes,
            this.chkObodNo,
            this.label71});
            this.detail.Height = 13.80741F;
            this.detail.Name = "detail";
            this.detail.Format += new System.EventHandler(this.detail_Format);
            this.detail.BeforePrint += new System.EventHandler(this.detail_BeforePrint);
            // 
            // subReport1
            // 
            this.subReport1.CloseBorder = false;
            this.subReport1.Height = 0.4469995F;
            this.subReport1.Left = 0F;
            this.subReport1.Name = "subReport1";
            this.subReport1.Report = null;
            this.subReport1.ReportName = "subReport1";
            this.subReport1.Top = 8.207001F;
            this.subReport1.Width = 8F;
            // 
            // shape1
            // 
            this.shape1.BackColor = System.Drawing.Color.LightGray;
            this.shape1.Height = 0.25F;
            this.shape1.Left = 0F;
            this.shape1.Name = "shape1";
            this.shape1.RoundingRadius = 9.999999F;
            this.shape1.Top = 0F;
            this.shape1.Width = 8F;
            // 
            // label1
            // 
            this.label1.Height = 0.188F;
            this.label1.HyperLink = null;
            this.label1.Left = 0.02F;
            this.label1.Name = "label1";
            this.label1.Style = "font-size: 9.75pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label1.Text = "PART I";
            this.label1.Top = 0.042F;
            this.label1.Width = 7.938F;
            // 
            // label2
            // 
            this.label2.Height = 0.2F;
            this.label2.HyperLink = null;
            this.label2.Left = 0.06F;
            this.label2.Name = "label2";
            this.label2.Style = "";
            this.label2.Text = "Waste category";
            this.label2.Top = 0.27F;
            this.label2.Width = 1F;
            // 
            // label3
            // 
            this.label3.Height = 0.2F;
            this.label3.HyperLink = null;
            this.label3.Left = 2.661F;
            this.label3.Name = "label3";
            this.label3.Style = "";
            this.label3.Text = "P2 Code";
            this.label3.Top = 0.27F;
            this.label3.Width = 0.601F;
            // 
            // label4
            // 
            this.label4.Height = 0.2F;
            this.label4.HyperLink = null;
            this.label4.Left = 4.328F;
            this.label4.Name = "label4";
            this.label4.Style = "";
            this.label4.Text = "Type service";
            this.label4.Top = 0.27F;
            this.label4.Width = 0.8160002F;
            // 
            // label5
            // 
            this.label5.Height = 0.2F;
            this.label5.HyperLink = null;
            this.label5.Left = 5.697F;
            this.label5.Name = "label5";
            this.label5.Style = "";
            this.label5.Text = "Disposition";
            this.label5.Top = 0.27F;
            this.label5.Width = 0.772999F;
            // 
            // shape2
            // 
            this.shape2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(211)))), ((int)(((byte)(211)))));
            this.shape2.Height = 0.25F;
            this.shape2.Left = 0F;
            this.shape2.Name = "shape2";
            this.shape2.RoundingRadius = 9.999999F;
            this.shape2.Top = 4.825F;
            this.shape2.Width = 8F;
            // 
            // label6
            // 
            this.label6.Height = 0.188F;
            this.label6.HyperLink = null;
            this.label6.Left = 0.06F;
            this.label6.Name = "label6";
            this.label6.Style = "font-size: 9.75pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label6.Text = "PART II";
            this.label6.Top = 4.845F;
            this.label6.Width = 7.913001F;
            // 
            // txtWasteCategory
            // 
            this.txtWasteCategory.CanGrow = false;
            this.txtWasteCategory.Height = 0.2F;
            this.txtWasteCategory.Left = 1.07F;
            this.txtWasteCategory.Name = "txtWasteCategory";
            this.txtWasteCategory.ShrinkToFit = true;
            this.txtWasteCategory.Style = "background-color: Gainsboro; white-space: nowrap; ddo-shrink-to-fit: true; ddo-wr" +
    "ap-mode: nowrap";
            this.txtWasteCategory.Text = null;
            this.txtWasteCategory.Top = 0.27F;
            this.txtWasteCategory.Width = 1.549F;
            // 
            // txtSubCategory
            // 
            this.txtSubCategory.CanGrow = false;
            this.txtSubCategory.Height = 0.2F;
            this.txtSubCategory.Left = 3.262F;
            this.txtSubCategory.Name = "txtSubCategory";
            this.txtSubCategory.ShrinkToFit = true;
            this.txtSubCategory.Style = "background-color: Gainsboro; white-space: nowrap; ddo-shrink-to-fit: true; ddo-wr" +
    "ap-mode: nowrap";
            this.txtSubCategory.Text = null;
            this.txtSubCategory.Top = 0.27F;
            this.txtSubCategory.Width = 1.007F;
            // 
            // txtTypeService
            // 
            this.txtTypeService.CanGrow = false;
            this.txtTypeService.Height = 0.2F;
            this.txtTypeService.Left = 5.144F;
            this.txtTypeService.Name = "txtTypeService";
            this.txtTypeService.ShrinkToFit = true;
            this.txtTypeService.Style = "background-color: Gainsboro; white-space: nowrap; ddo-shrink-to-fit: true; ddo-wr" +
    "ap-mode: nowrap";
            this.txtTypeService.Text = null;
            this.txtTypeService.Top = 0.27F;
            this.txtTypeService.Width = 0.507F;
            // 
            // txtDisposition
            // 
            this.txtDisposition.CanGrow = false;
            this.txtDisposition.Height = 0.2F;
            this.txtDisposition.Left = 6.47F;
            this.txtDisposition.Name = "txtDisposition";
            this.txtDisposition.ShrinkToFit = true;
            this.txtDisposition.Style = "background-color: Gainsboro; white-space: nowrap; ddo-shrink-to-fit: true; ddo-wr" +
    "ap-mode: nowrap";
            this.txtDisposition.Text = null;
            this.txtDisposition.Top = 0.27F;
            this.txtDisposition.Width = 1.488F;
            // 
            // line1
            // 
            this.line1.Height = 7.937F;
            this.line1.Left = 0F;
            this.line1.LineWeight = 1F;
            this.line1.Name = "line1";
            this.line1.Top = 0.25F;
            this.line1.Width = 0.002F;
            this.line1.X1 = 0F;
            this.line1.X2 = 0.002F;
            this.line1.Y1 = 0.25F;
            this.line1.Y2 = 8.187F;
            // 
            // line2
            // 
            this.line2.Height = 7.937F;
            this.line2.Left = 8F;
            this.line2.LineWeight = 1F;
            this.line2.Name = "line2";
            this.line2.Top = 0.25F;
            this.line2.Width = 0F;
            this.line2.X1 = 8F;
            this.line2.X2 = 8F;
            this.line2.Y1 = 0.25F;
            this.line2.Y2 = 8.187F;
            // 
            // line3
            // 
            this.line3.Height = 0F;
            this.line3.Left = 0F;
            this.line3.LineWeight = 1F;
            this.line3.Name = "line3";
            this.line3.Top = 0.5100001F;
            this.line3.Width = 8F;
            this.line3.X1 = 0F;
            this.line3.X2 = 8F;
            this.line3.Y1 = 0.5100001F;
            this.line3.Y2 = 0.5100001F;
            // 
            // line4
            // 
            this.line4.Height = 0F;
            this.line4.Left = 0F;
            this.line4.LineWeight = 1F;
            this.line4.Name = "line4";
            this.line4.Top = 1.02F;
            this.line4.Width = 8F;
            this.line4.X1 = 0F;
            this.line4.X2 = 8F;
            this.line4.Y1 = 1.02F;
            this.line4.Y2 = 1.02F;
            // 
            // label7
            // 
            this.label7.Height = 0.2F;
            this.label7.HyperLink = null;
            this.label7.Left = 0.06F;
            this.label7.Name = "label7";
            this.label7.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label7.Text = "A. GENERAL INFORMATION";
            this.label7.Top = 0.56F;
            this.label7.Width = 3.25F;
            // 
            // line5
            // 
            this.line5.Height = 1.49F;
            this.line5.Left = 4.32F;
            this.line5.LineWeight = 1F;
            this.line5.Name = "line5";
            this.line5.Top = 0.5100001F;
            this.line5.Width = 0F;
            this.line5.X1 = 4.32F;
            this.line5.X2 = 4.32F;
            this.line5.Y1 = 0.5100001F;
            this.line5.Y2 = 2F;
            // 
            // label8
            // 
            this.label8.Height = 0.2F;
            this.label8.HyperLink = null;
            this.label8.Left = 0.06F;
            this.label8.Name = "label8";
            this.label8.Style = "";
            this.label8.Text = "1. Generator Name:";
            this.label8.Top = 0.79F;
            this.label8.Width = 1.281F;
            // 
            // txtGeneratorName
            // 
            this.txtGeneratorName.Height = 0.2F;
            this.txtGeneratorName.Left = 1.341F;
            this.txtGeneratorName.Name = "txtGeneratorName";
            this.txtGeneratorName.ShrinkToFit = true;
            this.txtGeneratorName.Style = "ddo-shrink-to-fit: true";
            this.txtGeneratorName.Text = null;
            this.txtGeneratorName.Top = 0.79F;
            this.txtGeneratorName.Width = 1.969F;
            // 
            // label9
            // 
            this.label9.Height = 0.2F;
            this.label9.HyperLink = null;
            this.label9.Left = 4.823F;
            this.label9.Name = "label9";
            this.label9.Style = "";
            this.label9.Text = "Waste Profile No";
            this.label9.Top = 0.56F;
            this.label9.Width = 1.101F;
            // 
            // line6
            // 
            this.line6.Height = 0F;
            this.line6.Left = 3.31F;
            this.line6.LineWeight = 1F;
            this.line6.Name = "line6";
            this.line6.Top = 1.51F;
            this.line6.Width = 4.69F;
            this.line6.X1 = 3.31F;
            this.line6.X2 = 8F;
            this.line6.Y1 = 1.51F;
            this.line6.Y2 = 1.51F;
            // 
            // line7
            // 
            this.line7.Height = 0.7450001F;
            this.line7.Left = 3.31F;
            this.line7.LineWeight = 1F;
            this.line7.Name = "line7";
            this.line7.Top = 1.51F;
            this.line7.Width = 0F;
            this.line7.X1 = 3.31F;
            this.line7.X2 = 3.31F;
            this.line7.Y1 = 2.255F;
            this.line7.Y2 = 1.51F;
            // 
            // line8
            // 
            this.line8.Height = 0F;
            this.line8.Left = 0F;
            this.line8.LineWeight = 1F;
            this.line8.Name = "line8";
            this.line8.Top = 2F;
            this.line8.Width = 8F;
            this.line8.X1 = 0F;
            this.line8.X2 = 8F;
            this.line8.Y1 = 2F;
            this.line8.Y2 = 2F;
            // 
            // line9
            // 
            this.line9.Height = 0F;
            this.line9.Left = 0F;
            this.line9.LineWeight = 1F;
            this.line9.Name = "line9";
            this.line9.Top = 2.255F;
            this.line9.Width = 8F;
            this.line9.X1 = 0F;
            this.line9.X2 = 8F;
            this.line9.Y1 = 2.255F;
            this.line9.Y2 = 2.255F;
            // 
            // line10
            // 
            this.line10.Height = 0.2539999F;
            this.line10.Left = 5.69F;
            this.line10.LineWeight = 1F;
            this.line10.Name = "line10";
            this.line10.Top = 2F;
            this.line10.Width = 0F;
            this.line10.X1 = 5.69F;
            this.line10.X2 = 5.69F;
            this.line10.Y1 = 2F;
            this.line10.Y2 = 2.254F;
            // 
            // label10
            // 
            this.label10.Height = 0.2F;
            this.label10.HyperLink = null;
            this.label10.Left = 0.06F;
            this.label10.Name = "label10";
            this.label10.Style = "";
            this.label10.Text = "2. Facility Address:";
            this.label10.Top = 1.05F;
            this.label10.Width = 1.88F;
            // 
            // txtFacilityAddress
            // 
            this.txtFacilityAddress.Height = 0.2F;
            this.txtFacilityAddress.Left = 0.06F;
            this.txtFacilityAddress.Name = "txtFacilityAddress";
            this.txtFacilityAddress.ShrinkToFit = true;
            this.txtFacilityAddress.Style = "ddo-shrink-to-fit: true";
            this.txtFacilityAddress.Text = null;
            this.txtFacilityAddress.Top = 1.28F;
            this.txtFacilityAddress.Width = 2.942F;
            // 
            // label11
            // 
            this.label11.Height = 0.2F;
            this.label11.HyperLink = null;
            this.label11.Left = 4.409F;
            this.label11.Name = "label11";
            this.label11.Style = "";
            this.label11.Text = "4. Generator USEPA ID";
            this.label11.Top = 1.05F;
            this.label11.Width = 1.88F;
            // 
            // label12
            // 
            this.label12.Height = 0.2F;
            this.label12.HyperLink = null;
            this.label12.Left = 4.409F;
            this.label12.Name = "label12";
            this.label12.Style = "";
            this.label12.Text = "5. Generator State ID";
            this.label12.Top = 1.54F;
            this.label12.Width = 1.88F;
            // 
            // txtWasteProfileNo
            // 
            this.txtWasteProfileNo.Height = 0.2F;
            this.txtWasteProfileNo.Left = 6.078F;
            this.txtWasteProfileNo.Name = "txtWasteProfileNo";
            this.txtWasteProfileNo.ShrinkToFit = true;
            this.txtWasteProfileNo.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtWasteProfileNo.Text = null;
            this.txtWasteProfileNo.Top = 0.56F;
            this.txtWasteProfileNo.Width = 1.88F;
            // 
            // txtGeneratorUsepaId
            // 
            this.txtGeneratorUsepaId.Height = 0.2F;
            this.txtGeneratorUsepaId.Left = 4.409F;
            this.txtGeneratorUsepaId.Name = "txtGeneratorUsepaId";
            this.txtGeneratorUsepaId.ShrinkToFit = true;
            this.txtGeneratorUsepaId.Style = "ddo-shrink-to-fit: true";
            this.txtGeneratorUsepaId.Text = null;
            this.txtGeneratorUsepaId.Top = 1.28F;
            this.txtGeneratorUsepaId.Width = 3.549F;
            // 
            // txtGeneratorStateId
            // 
            this.txtGeneratorStateId.Height = 0.2F;
            this.txtGeneratorStateId.Left = 4.409F;
            this.txtGeneratorStateId.Name = "txtGeneratorStateId";
            this.txtGeneratorStateId.ShrinkToFit = true;
            this.txtGeneratorStateId.Style = "ddo-shrink-to-fit: true";
            this.txtGeneratorStateId.Text = null;
            this.txtGeneratorStateId.Top = 1.77F;
            this.txtGeneratorStateId.Width = 3.549F;
            // 
            // label13
            // 
            this.label13.Height = 0.2F;
            this.label13.HyperLink = null;
            this.label13.Left = 0.06F;
            this.label13.Name = "label13";
            this.label13.Style = "";
            this.label13.Text = "6. Technical Contact";
            this.label13.Top = 2.03F;
            this.label13.Width = 1.281F;
            // 
            // label14
            // 
            this.label14.Height = 0.2F;
            this.label14.HyperLink = null;
            this.label14.Left = 3.35F;
            this.label14.Name = "label14";
            this.label14.Style = "";
            this.label14.Text = "3. Zip Code";
            this.label14.Top = 1.54F;
            this.label14.Width = 0.7500001F;
            // 
            // label15
            // 
            this.label15.Height = 0.2F;
            this.label15.HyperLink = null;
            this.label15.Left = 3.35F;
            this.label15.Name = "label15";
            this.label15.Style = "";
            this.label15.Text = "7. Title";
            this.label15.Top = 2.03F;
            this.label15.Width = 0.4600002F;
            // 
            // label16
            // 
            this.label16.Height = 0.2F;
            this.label16.HyperLink = null;
            this.label16.Left = 5.729F;
            this.label16.Name = "label16";
            this.label16.Style = "";
            this.label16.Text = "8. Phone";
            this.label16.Top = 2.03F;
            this.label16.Width = 0.6230003F;
            // 
            // txtTechnicalContact
            // 
            this.txtTechnicalContact.Height = 0.2F;
            this.txtTechnicalContact.Left = 1.393F;
            this.txtTechnicalContact.Name = "txtTechnicalContact";
            this.txtTechnicalContact.ShrinkToFit = true;
            this.txtTechnicalContact.Style = "ddo-shrink-to-fit: true";
            this.txtTechnicalContact.Text = null;
            this.txtTechnicalContact.Top = 2.03F;
            this.txtTechnicalContact.Width = 1.827999F;
            // 
            // txtTitle
            // 
            this.txtTitle.Height = 0.2F;
            this.txtTitle.Left = 3.883F;
            this.txtTitle.Name = "txtTitle";
            this.txtTitle.ShrinkToFit = true;
            this.txtTitle.Style = "ddo-shrink-to-fit: true";
            this.txtTitle.Text = null;
            this.txtTitle.Top = 2.03F;
            this.txtTitle.Width = 1.742F;
            // 
            // txtPhone
            // 
            this.txtPhone.Height = 0.2F;
            this.txtPhone.Left = 6.412F;
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.ShrinkToFit = true;
            this.txtPhone.Style = "ddo-shrink-to-fit: true";
            this.txtPhone.Text = null;
            this.txtPhone.Top = 2.03F;
            this.txtPhone.Width = 1.546F;
            // 
            // label17
            // 
            this.label17.Height = 0.2F;
            this.label17.HyperLink = null;
            this.label17.Left = 0.06F;
            this.label17.Name = "label17";
            this.label17.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label17.Text = "B. WASTE INFORMATION";
            this.label17.Top = 2.285F;
            this.label17.Width = 3.25F;
            // 
            // txtZipCode
            // 
            this.txtZipCode.Height = 0.2F;
            this.txtZipCode.Left = 3.35F;
            this.txtZipCode.Name = "txtZipCode";
            this.txtZipCode.ShrinkToFit = true;
            this.txtZipCode.Style = "ddo-shrink-to-fit: true";
            this.txtZipCode.Text = null;
            this.txtZipCode.Top = 1.77F;
            this.txtZipCode.Width = 0.89F;
            // 
            // label18
            // 
            this.label18.Height = 0.2F;
            this.label18.HyperLink = null;
            this.label18.Left = 0.06F;
            this.label18.Name = "label18";
            this.label18.Style = "";
            this.label18.Text = "1. Name of waste (description)";
            this.label18.Top = 2.515F;
            this.label18.Width = 2.279F;
            // 
            // txtNameOfWaste
            // 
            this.txtNameOfWaste.Height = 0.2F;
            this.txtNameOfWaste.Left = 2.422F;
            this.txtNameOfWaste.Name = "txtNameOfWaste";
            this.txtNameOfWaste.ShrinkToFit = true;
            this.txtNameOfWaste.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtNameOfWaste.Text = null;
            this.txtNameOfWaste.Top = 2.515F;
            this.txtNameOfWaste.Width = 5.536F;
            // 
            // label19
            // 
            this.label19.Height = 0.2000001F;
            this.label19.HyperLink = null;
            this.label19.Left = 0.06F;
            this.label19.Name = "label19";
            this.label19.Style = "";
            this.label19.Text = "2A. USA EPA waste code(s)";
            this.label19.Top = 2.745F;
            this.label19.Width = 2.279F;
            // 
            // txtUsaEpaWasteCode
            // 
            this.txtUsaEpaWasteCode.Height = 0.2F;
            this.txtUsaEpaWasteCode.Left = 2.437F;
            this.txtUsaEpaWasteCode.Name = "txtUsaEpaWasteCode";
            this.txtUsaEpaWasteCode.ShrinkToFit = true;
            this.txtUsaEpaWasteCode.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtUsaEpaWasteCode.Text = null;
            this.txtUsaEpaWasteCode.Top = 2.745F;
            this.txtUsaEpaWasteCode.Width = 5.536F;
            // 
            // label20
            // 
            this.label20.Height = 0.2000001F;
            this.label20.HyperLink = null;
            this.label20.Left = 0.06F;
            this.label20.Name = "label20";
            this.label20.Style = "";
            this.label20.Text = "2B. State/Local/Host Nation waste code(s)";
            this.label20.Top = 2.975F;
            this.label20.Width = 2.862F;
            // 
            // txtStateLocalHostNationWasteCode
            // 
            this.txtStateLocalHostNationWasteCode.Height = 0.2F;
            this.txtStateLocalHostNationWasteCode.Left = 3.02F;
            this.txtStateLocalHostNationWasteCode.Name = "txtStateLocalHostNationWasteCode";
            this.txtStateLocalHostNationWasteCode.ShrinkToFit = true;
            this.txtStateLocalHostNationWasteCode.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtStateLocalHostNationWasteCode.Text = null;
            this.txtStateLocalHostNationWasteCode.Top = 2.975F;
            this.txtStateLocalHostNationWasteCode.Width = 4.953F;
            // 
            // label21
            // 
            this.label21.Height = 0.2000001F;
            this.label21.HyperLink = null;
            this.label21.Left = 0.06F;
            this.label21.Name = "label21";
            this.label21.Style = "";
            this.label21.Text = "3.Process generating waste";
            this.label21.Top = 3.205F;
            this.label21.Width = 2.279F;
            // 
            // txtProcessGeneratingWaste
            // 
            this.txtProcessGeneratingWaste.Height = 0.2F;
            this.txtProcessGeneratingWaste.Left = 2.422F;
            this.txtProcessGeneratingWaste.Name = "txtProcessGeneratingWaste";
            this.txtProcessGeneratingWaste.ShrinkToFit = true;
            this.txtProcessGeneratingWaste.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtProcessGeneratingWaste.Text = null;
            this.txtProcessGeneratingWaste.Top = 3.205F;
            this.txtProcessGeneratingWaste.Width = 5.536F;
            // 
            // label22
            // 
            this.label22.Height = 0.2000002F;
            this.label22.HyperLink = null;
            this.label22.Left = 0.06F;
            this.label22.Name = "label22";
            this.label22.Style = "";
            this.label22.Text = "Waste stream";
            this.label22.Top = 3.435F;
            this.label22.Width = 0.896F;
            // 
            // txtWasteStream
            // 
            this.txtWasteStream.Height = 0.2F;
            this.txtWasteStream.Left = 1.017F;
            this.txtWasteStream.Name = "txtWasteStream";
            this.txtWasteStream.ShrinkToFit = true;
            this.txtWasteStream.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtWasteStream.Text = null;
            this.txtWasteStream.Top = 3.435F;
            this.txtWasteStream.Width = 1.905F;
            // 
            // label23
            // 
            this.label23.Height = 0.2000002F;
            this.label23.HyperLink = null;
            this.label23.Left = 3.019F;
            this.label23.Name = "label23";
            this.label23.Style = "";
            this.label23.Text = "Source code";
            this.label23.Top = 3.435F;
            this.label23.Width = 0.8640003F;
            // 
            // txtSourceCode
            // 
            this.txtSourceCode.Height = 0.2F;
            this.txtSourceCode.Left = 3.941F;
            this.txtSourceCode.Name = "txtSourceCode";
            this.txtSourceCode.ShrinkToFit = true;
            this.txtSourceCode.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtSourceCode.Text = null;
            this.txtSourceCode.Top = 3.435F;
            this.txtSourceCode.Width = 0.735F;
            // 
            // label24
            // 
            this.label24.Height = 0.2099999F;
            this.label24.HyperLink = null;
            this.label24.Left = 4.729F;
            this.label24.Name = "label24";
            this.label24.Style = "";
            this.label24.Text = "Form code";
            this.label24.Top = 3.435F;
            this.label24.Width = 0.7600002F;
            // 
            // txtFormCode
            // 
            this.txtFormCode.Height = 0.2F;
            this.txtFormCode.Left = 5.54F;
            this.txtFormCode.Name = "txtFormCode";
            this.txtFormCode.ShrinkToFit = true;
            this.txtFormCode.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtFormCode.Text = null;
            this.txtFormCode.Top = 3.435F;
            this.txtFormCode.Width = 0.6409997F;
            // 
            // label25
            // 
            this.label25.Height = 0.2099999F;
            this.label25.HyperLink = null;
            this.label25.Left = 6.25F;
            this.label25.Name = "label25";
            this.label25.Style = "";
            this.label25.Text = "Mgmt code";
            this.label25.Top = 3.435F;
            this.label25.Width = 0.76F;
            // 
            // txtMgmtCode
            // 
            this.txtMgmtCode.Height = 0.2F;
            this.txtMgmtCode.Left = 7.054F;
            this.txtMgmtCode.Name = "txtMgmtCode";
            this.txtMgmtCode.ShrinkToFit = true;
            this.txtMgmtCode.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtMgmtCode.Text = null;
            this.txtMgmtCode.Top = 3.445F;
            this.txtMgmtCode.Width = 0.9040003F;
            // 
            // label26
            // 
            this.label26.Height = 0.1999999F;
            this.label26.HyperLink = null;
            this.label26.Left = 0.06F;
            this.label26.Name = "label26";
            this.label26.Style = "";
            this.label26.Text = "4. Projected annual quantity";
            this.label26.Top = 3.675F;
            this.label26.Width = 1.794F;
            // 
            // txtProjectedAnnualVolume
            // 
            this.txtProjectedAnnualVolume.Height = 0.2F;
            this.txtProjectedAnnualVolume.Left = 1.854F;
            this.txtProjectedAnnualVolume.Name = "txtProjectedAnnualVolume";
            this.txtProjectedAnnualVolume.ShrinkToFit = true;
            this.txtProjectedAnnualVolume.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtProjectedAnnualVolume.Text = null;
            this.txtProjectedAnnualVolume.Top = 3.675F;
            this.txtProjectedAnnualVolume.Width = 1.583F;
            // 
            // label27
            // 
            this.label27.Height = 0.1999999F;
            this.label27.HyperLink = null;
            this.label27.Left = 4.046F;
            this.label27.Name = "label27";
            this.label27.Style = "";
            this.label27.Text = "5. Nomenclature";
            this.label27.Top = 3.675F;
            this.label27.Width = 1.363001F;
            // 
            // txtModeOfCollection
            // 
            this.txtModeOfCollection.Height = 0.2F;
            this.txtModeOfCollection.Left = 5.489F;
            this.txtModeOfCollection.Name = "txtModeOfCollection";
            this.txtModeOfCollection.ShrinkToFit = true;
            this.txtModeOfCollection.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtModeOfCollection.Text = null;
            this.txtModeOfCollection.Top = 3.675F;
            this.txtModeOfCollection.Width = 2.469F;
            // 
            // label28
            // 
            this.label28.Height = 0.1999997F;
            this.label28.HyperLink = null;
            this.label28.Left = 0.06F;
            this.label28.Name = "label28";
            this.label28.Style = "font-size: 9pt; ddo-char-set: 0";
            this.label28.Text = "6. Is this waste a dioxin listed in waste as defined in 40 CFR 261.31 (e.g. F020," +
    " F021, F023, F026, F027, or F028)?";
            this.label28.Top = 3.905F;
            this.label28.Width = 6.460001F;
            // 
            // label29
            // 
            this.label29.Height = 0.1999997F;
            this.label29.HyperLink = null;
            this.label29.Left = 0.06F;
            this.label29.Name = "label29";
            this.label29.Style = "";
            this.label29.Text = "7. Is this waste restricted from land disposal (40 CFR 268)?";
            this.label29.Top = 4.135F;
            this.label29.Width = 3.75F;
            // 
            // label30
            // 
            this.label30.Height = 0.2000001F;
            this.label30.HyperLink = null;
            this.label30.Left = 0.06F;
            this.label30.Name = "label30";
            this.label30.Style = "";
            this.label30.Text = "Has an exemption been granted?";
            this.label30.Top = 4.365F;
            this.label30.Width = 3.75F;
            // 
            // label31
            // 
            this.label31.Height = 0.2000002F;
            this.label31.HyperLink = null;
            this.label31.Left = 0.06F;
            this.label31.Name = "label31";
            this.label31.Style = "";
            this.label31.Text = "Does the waste meet applicable treatment standards?";
            this.label31.Top = 4.595F;
            this.label31.Width = 3.75F;
            // 
            // label32
            // 
            this.label32.Height = 0.2000002F;
            this.label32.HyperLink = null;
            this.label32.Left = 5.114F;
            this.label32.Name = "label32";
            this.label32.Style = "";
            this.label32.Text = "Reference Standards";
            this.label32.Top = 4.365F;
            this.label32.Width = 1.406F;
            // 
            // txtReferenceStandards
            // 
            this.txtReferenceStandards.Height = 0.2F;
            this.txtReferenceStandards.Left = 5.114F;
            this.txtReferenceStandards.Name = "txtReferenceStandards";
            this.txtReferenceStandards.ShrinkToFit = true;
            this.txtReferenceStandards.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtReferenceStandards.Text = null;
            this.txtReferenceStandards.Top = 4.595F;
            this.txtReferenceStandards.Width = 2.844F;
            // 
            // label33
            // 
            this.label33.Height = 0.2F;
            this.label33.HyperLink = null;
            this.label33.Left = 0.06F;
            this.label33.Name = "label33";
            this.label33.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label33.Text = "1. MATERIAL CHARACTERIZATION";
            this.label33.Top = 5.105F;
            this.label33.Width = 2.377F;
            // 
            // label34
            // 
            this.label34.Height = 0.2F;
            this.label34.HyperLink = null;
            this.label34.Left = 2.437F;
            this.label34.Name = "label34";
            this.label34.Style = "font-size: 9.75pt; font-style: italic; ddo-char-set: 0";
            this.label34.Text = "(Optional - unless otherwise indicated)";
            this.label34.Top = 5.105F;
            this.label34.Width = 4.083F;
            // 
            // line11
            // 
            this.line11.Height = 0F;
            this.line11.Left = 0F;
            this.line11.LineWeight = 1F;
            this.line11.Name = "line11";
            this.line11.Top = 5.805F;
            this.line11.Width = 8F;
            this.line11.X1 = 0F;
            this.line11.X2 = 8F;
            this.line11.Y1 = 5.805F;
            this.line11.Y2 = 5.805F;
            // 
            // label35
            // 
            this.label35.Height = 0.2F;
            this.label35.HyperLink = null;
            this.label35.Left = 0.06F;
            this.label35.Name = "label35";
            this.label35.Style = "";
            this.label35.Text = "Color";
            this.label35.Top = 5.335F;
            this.label35.Width = 0.5830001F;
            // 
            // label36
            // 
            this.label36.Height = 0.2F;
            this.label36.HyperLink = null;
            this.label36.Left = 0.06F;
            this.label36.Name = "label36";
            this.label36.Style = "";
            this.label36.Text = "BTU/LB";
            this.label36.Top = 5.574999F;
            this.label36.Width = 0.5830001F;
            // 
            // label37
            // 
            this.label37.Height = 0.2F;
            this.label37.HyperLink = null;
            this.label37.Left = 2.032F;
            this.label37.Name = "label37";
            this.label37.Style = "";
            this.label37.Text = "Density";
            this.label37.Top = 5.335F;
            this.label37.Width = 0.7499999F;
            // 
            // label38
            // 
            this.label38.Height = 0.2F;
            this.label38.HyperLink = null;
            this.label38.Left = 2.032F;
            this.label38.Name = "label38";
            this.label38.Style = "";
            this.label38.Text = "Total solids";
            this.label38.Top = 5.574999F;
            this.label38.Width = 0.7499999F;
            // 
            // txtColor
            // 
            this.txtColor.Height = 0.2F;
            this.txtColor.Left = 0.6470001F;
            this.txtColor.Name = "txtColor";
            this.txtColor.ShrinkToFit = true;
            this.txtColor.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtColor.Text = null;
            this.txtColor.Top = 5.335F;
            this.txtColor.Width = 1.293F;
            // 
            // txtBtuLb
            // 
            this.txtBtuLb.Height = 0.2F;
            this.txtBtuLb.Left = 0.6470001F;
            this.txtBtuLb.Name = "txtBtuLb";
            this.txtBtuLb.ShrinkToFit = true;
            this.txtBtuLb.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtBtuLb.Text = null;
            this.txtBtuLb.Top = 5.574999F;
            this.txtBtuLb.Width = 1.293F;
            // 
            // txtDensity
            // 
            this.txtDensity.Height = 0.2F;
            this.txtDensity.Left = 2.832F;
            this.txtDensity.Name = "txtDensity";
            this.txtDensity.ShrinkToFit = true;
            this.txtDensity.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtDensity.Text = null;
            this.txtDensity.Top = 5.335F;
            this.txtDensity.Width = 1.234F;
            // 
            // txtTotalSolids
            // 
            this.txtTotalSolids.Height = 0.2F;
            this.txtTotalSolids.Left = 2.832F;
            this.txtTotalSolids.Name = "txtTotalSolids";
            this.txtTotalSolids.ShrinkToFit = true;
            this.txtTotalSolids.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtTotalSolids.Text = null;
            this.txtTotalSolids.Top = 5.574999F;
            this.txtTotalSolids.Width = 1.234F;
            // 
            // label39
            // 
            this.label39.Height = 0.2F;
            this.label39.HyperLink = null;
            this.label39.Left = 4.175F;
            this.label39.Name = "label39";
            this.label39.Style = "";
            this.label39.Text = "Ash content";
            this.label39.Top = 5.575F;
            this.label39.Width = 0.8440003F;
            // 
            // label40
            // 
            this.label40.Height = 0.2F;
            this.label40.HyperLink = null;
            this.label40.Left = 4.175F;
            this.label40.Name = "label40";
            this.label40.Style = "";
            this.label40.Text = "Layering";
            this.label40.Top = 5.335001F;
            this.label40.Width = 0.7090002F;
            // 
            // chkMultiLayered
            // 
            this.chkMultiLayered.Height = 0.2F;
            this.chkMultiLayered.Left = 4.884F;
            this.chkMultiLayered.Name = "chkMultiLayered";
            this.chkMultiLayered.Style = "";
            this.chkMultiLayered.Text = "Multi-layered";
            this.chkMultiLayered.Top = 5.335001F;
            this.chkMultiLayered.Width = 1F;
            // 
            // chkBilayered
            // 
            this.chkBilayered.Height = 0.2F;
            this.chkBilayered.Left = 5.964F;
            this.chkBilayered.Name = "chkBilayered";
            this.chkBilayered.Style = "";
            this.chkBilayered.Text = "Bilayered";
            this.chkBilayered.Top = 5.335001F;
            this.chkBilayered.Width = 0.823F;
            // 
            // chkSinglePhased
            // 
            this.chkSinglePhased.Height = 0.2F;
            this.chkSinglePhased.Left = 6.875F;
            this.chkSinglePhased.Name = "chkSinglePhased";
            this.chkSinglePhased.Style = "";
            this.chkSinglePhased.Text = "Single phased";
            this.chkSinglePhased.Top = 5.335001F;
            this.chkSinglePhased.Width = 1.083F;
            // 
            // txtAshContent
            // 
            this.txtAshContent.Height = 0.2F;
            this.txtAshContent.Left = 5.019001F;
            this.txtAshContent.Name = "txtAshContent";
            this.txtAshContent.ShrinkToFit = true;
            this.txtAshContent.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtAshContent.Text = null;
            this.txtAshContent.Top = 5.575F;
            this.txtAshContent.Width = 1.768F;
            // 
            // label41
            // 
            this.label41.Height = 0.2F;
            this.label41.HyperLink = null;
            this.label41.Left = 0.06F;
            this.label41.Name = "label41";
            this.label41.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label41.Text = "2. RCRA CHARACTERISTICS";
            this.label41.Top = 5.835001F;
            this.label41.Width = 2.377F;
            // 
            // label42
            // 
            this.label42.Height = 0.2F;
            this.label42.HyperLink = null;
            this.label42.Left = 0.06F;
            this.label42.Name = "label42";
            this.label42.Style = "";
            this.label42.Text = "Physical state (mandatory)";
            this.label42.Top = 6.065001F;
            this.label42.Width = 1.764F;
            // 
            // txtPhysicalStateOther
            // 
            this.txtPhysicalStateOther.Height = 0.2F;
            this.txtPhysicalStateOther.Left = 5.858001F;
            this.txtPhysicalStateOther.Name = "txtPhysicalStateOther";
            this.txtPhysicalStateOther.ShrinkToFit = true;
            this.txtPhysicalStateOther.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtPhysicalStateOther.Text = null;
            this.txtPhysicalStateOther.Top = 6.061999F;
            this.txtPhysicalStateOther.Width = 0.9200001F;
            // 
            // label43
            // 
            this.label43.Height = 0.2F;
            this.label43.HyperLink = null;
            this.label43.Left = 0.06F;
            this.label43.Name = "label43";
            this.label43.Style = "";
            this.label43.Text = "Treatment category";
            this.label43.Top = 6.295F;
            this.label43.Width = 1.333F;
            // 
            // chkIgnitable
            // 
            this.chkIgnitable.Height = 0.2F;
            this.chkIgnitable.Left = 1.509F;
            this.chkIgnitable.Name = "chkIgnitable";
            this.chkIgnitable.Style = "";
            this.chkIgnitable.Text = "Ignitable (D001)";
            this.chkIgnitable.Top = 6.295F;
            this.chkIgnitable.Width = 1.304F;
            // 
            // chkCorrosive
            // 
            this.chkCorrosive.Height = 0.2F;
            this.chkCorrosive.Left = 3.265F;
            this.chkCorrosive.Name = "chkCorrosive";
            this.chkCorrosive.Style = "";
            this.chkCorrosive.Text = "Corrosive (D002)";
            this.chkCorrosive.Top = 6.295F;
            this.chkCorrosive.Width = 1.398F;
            // 
            // chkReactive
            // 
            this.chkReactive.Height = 0.2F;
            this.chkReactive.Left = 4.737F;
            this.chkReactive.Name = "chkReactive";
            this.chkReactive.Style = "";
            this.chkReactive.Text = "Reactive (D003)";
            this.chkReactive.Top = 6.295F;
            this.chkReactive.Width = 1.398F;
            // 
            // chkToxicityCharacteristic
            // 
            this.chkToxicityCharacteristic.Height = 0.2F;
            this.chkToxicityCharacteristic.Left = 6.272001F;
            this.chkToxicityCharacteristic.Name = "chkToxicityCharacteristic";
            this.chkToxicityCharacteristic.Style = "";
            this.chkToxicityCharacteristic.Text = "Toxicity Characteristic";
            this.chkToxicityCharacteristic.Top = 6.295F;
            this.chkToxicityCharacteristic.Width = 1.577F;
            // 
            // label44
            // 
            this.label44.Height = 0.2F;
            this.label44.HyperLink = null;
            this.label44.Left = 1.618F;
            this.label44.Name = "label44";
            this.label44.Style = "";
            this.label44.Text = "Flash Point (F)";
            this.label44.Top = 6.525F;
            this.label44.Width = 1F;
            // 
            // label45
            // 
            this.label45.Height = 0.2F;
            this.label45.HyperLink = null;
            this.label45.Left = 3.35F;
            this.label45.Name = "label45";
            this.label45.Style = "";
            this.label45.Text = "pH";
            this.label45.Top = 6.525F;
            this.label45.Width = 0.27F;
            // 
            // chkCorrosiveSteel
            // 
            this.chkCorrosiveSteel.Height = 0.2F;
            this.chkCorrosiveSteel.Left = 3.35F;
            this.chkCorrosiveSteel.Name = "chkCorrosiveSteel";
            this.chkCorrosiveSteel.Style = "";
            this.chkCorrosiveSteel.Text = "Corrosive steel";
            this.chkCorrosiveSteel.Top = 6.755F;
            this.chkCorrosiveSteel.Width = 1.178F;
            // 
            // chkHighToc
            // 
            this.chkHighToc.Height = 0.2F;
            this.chkHighToc.Left = 1.618F;
            this.chkHighToc.Name = "chkHighToc";
            this.chkHighToc.Style = "";
            this.chkHighToc.Text = "High TOC ( >10% )";
            this.chkHighToc.Top = 6.985F;
            this.chkHighToc.Width = 1.42F;
            // 
            // txtFlashPoint
            // 
            this.txtFlashPoint.Height = 0.2F;
            this.txtFlashPoint.Left = 2.619F;
            this.txtFlashPoint.Name = "txtFlashPoint";
            this.txtFlashPoint.ShrinkToFit = true;
            this.txtFlashPoint.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtFlashPoint.Text = null;
            this.txtFlashPoint.Top = 6.525F;
            this.txtFlashPoint.Width = 0.5279999F;
            // 
            // chkLowToc
            // 
            this.chkLowToc.Height = 0.2F;
            this.chkLowToc.Left = 1.618F;
            this.chkLowToc.Name = "chkLowToc";
            this.chkLowToc.Style = "";
            this.chkLowToc.Text = "Low TOC ( <10% )";
            this.chkLowToc.Top = 6.755F;
            this.chkLowToc.Width = 1.42F;
            // 
            // txtCorrosivePh
            // 
            this.txtCorrosivePh.Height = 0.2F;
            this.txtCorrosivePh.Left = 3.62F;
            this.txtCorrosivePh.Name = "txtCorrosivePh";
            this.txtCorrosivePh.ShrinkToFit = true;
            this.txtCorrosivePh.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtCorrosivePh.Text = null;
            this.txtCorrosivePh.Top = 6.525F;
            this.txtCorrosivePh.Width = 0.738F;
            // 
            // chkWaterReactive
            // 
            this.chkWaterReactive.Height = 0.2F;
            this.chkWaterReactive.Left = 4.823F;
            this.chkWaterReactive.Name = "chkWaterReactive";
            this.chkWaterReactive.Style = "";
            this.chkWaterReactive.Text = "Water reactive";
            this.chkWaterReactive.Top = 6.525F;
            this.chkWaterReactive.Width = 1.312F;
            // 
            // chkCyanideReactive
            // 
            this.chkCyanideReactive.Height = 0.2F;
            this.chkCyanideReactive.Left = 4.823F;
            this.chkCyanideReactive.Name = "chkCyanideReactive";
            this.chkCyanideReactive.Style = "";
            this.chkCyanideReactive.Text = "Cyanide reactive";
            this.chkCyanideReactive.Top = 6.755F;
            this.chkCyanideReactive.Width = 1.312F;
            // 
            // chkSulfideReactive
            // 
            this.chkSulfideReactive.Height = 0.2F;
            this.chkSulfideReactive.Left = 4.823F;
            this.chkSulfideReactive.Name = "chkSulfideReactive";
            this.chkSulfideReactive.Style = "";
            this.chkSulfideReactive.Text = "Sulfide reactive";
            this.chkSulfideReactive.Top = 6.985F;
            this.chkSulfideReactive.Width = 1.312F;
            // 
            // line12
            // 
            this.line12.Height = 0F;
            this.line12.Left = 0F;
            this.line12.LineWeight = 1F;
            this.line12.Name = "line12";
            this.line12.Top = 7.254F;
            this.line12.Width = 8F;
            this.line12.X1 = 0F;
            this.line12.X2 = 8F;
            this.line12.Y1 = 7.254F;
            this.line12.Y2 = 7.254F;
            // 
            // label46
            // 
            this.label46.Height = 0.2F;
            this.label46.HyperLink = null;
            this.label46.Left = 0.06F;
            this.label46.Name = "label46";
            this.label46.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label46.Text = "3. CHEMICAL COMPOSITION";
            this.label46.Top = 7.298F;
            this.label46.Width = 2.377F;
            // 
            // label47
            // 
            this.label47.Height = 0.6590006F;
            this.label47.HyperLink = null;
            this.label47.Left = 0.06F;
            this.label47.Name = "label47";
            this.label47.Style = "font-size: 9.75pt; font-style: italic; ddo-char-set: 0";
            this.label47.Text = resources.GetString("label47.Text");
            this.label47.Top = 7.528F;
            this.label47.Width = 7.898F;
            // 
            // line13
            // 
            this.line13.Height = 0F;
            this.line13.Left = -1.862645E-09F;
            this.line13.LineWeight = 1F;
            this.line13.Name = "line13";
            this.line13.Top = 8.187F;
            this.line13.Width = 8F;
            this.line13.X1 = -1.862645E-09F;
            this.line13.X2 = 8F;
            this.line13.Y1 = 8.187F;
            this.line13.Y2 = 8.187F;
            // 
            // line14
            // 
            this.line14.Height = 0F;
            this.line14.Left = 0F;
            this.line14.LineWeight = 1F;
            this.line14.Name = "line14";
            this.line14.Top = 8.685999F;
            this.line14.Width = 8F;
            this.line14.X1 = 0F;
            this.line14.X2 = 8F;
            this.line14.Y1 = 8.685999F;
            this.line14.Y2 = 8.685999F;
            // 
            // label48
            // 
            this.label48.Height = 0.2F;
            this.label48.HyperLink = null;
            this.label48.Left = 0.06F;
            this.label48.Name = "label48";
            this.label48.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label48.Text = "4. SHIPPING INFORMATION";
            this.label48.Top = 8.705999F;
            this.label48.Width = 2.377F;
            // 
            // chkSolid
            // 
            this.chkSolid.Height = 0.2F;
            this.chkSolid.Left = 1.884F;
            this.chkSolid.Name = "chkSolid";
            this.chkSolid.Style = "";
            this.chkSolid.Text = "Solid";
            this.chkSolid.Top = 6.065001F;
            this.chkSolid.Width = 0.553F;
            // 
            // chkLiquid
            // 
            this.chkLiquid.Height = 0.2F;
            this.chkLiquid.Left = 2.508F;
            this.chkLiquid.Name = "chkLiquid";
            this.chkLiquid.Style = "";
            this.chkLiquid.Text = "Liquid";
            this.chkLiquid.Top = 6.065001F;
            this.chkLiquid.Width = 0.5529999F;
            // 
            // chkSemiSolid
            // 
            this.chkSemiSolid.Height = 0.2F;
            this.chkSemiSolid.Left = 3.147F;
            this.chkSemiSolid.Name = "chkSemiSolid";
            this.chkSemiSolid.Style = "";
            this.chkSemiSolid.Text = "Semi-solid";
            this.chkSemiSolid.Top = 6.065001F;
            this.chkSemiSolid.Width = 0.845F;
            // 
            // chkGas
            // 
            this.chkGas.Height = 0.2F;
            this.chkGas.Left = 4.046F;
            this.chkGas.Name = "chkGas";
            this.chkGas.Style = "";
            this.chkGas.Text = "Gas";
            this.chkGas.Top = 6.065001F;
            this.chkGas.Width = 0.4980004F;
            // 
            // chkOther
            // 
            this.chkOther.Height = 0.2F;
            this.chkOther.Left = 5.125F;
            this.chkOther.Name = "chkOther";
            this.chkOther.Style = "";
            this.chkOther.Text = "Other";
            this.chkOther.Top = 6.062F;
            this.chkOther.Width = 0.6650001F;
            // 
            // label49
            // 
            this.label49.Height = 0.2F;
            this.label49.HyperLink = null;
            this.label49.Left = 0.06F;
            this.label49.Name = "label49";
            this.label49.Style = "";
            this.label49.Text = "DOT Hazardous Material?";
            this.label49.Top = 8.935999F;
            this.label49.Width = 1.764F;
            // 
            // chkHazardousMaterialYes
            // 
            this.chkHazardousMaterialYes.Height = 0.2F;
            this.chkHazardousMaterialYes.Left = 1.854F;
            this.chkHazardousMaterialYes.Name = "chkHazardousMaterialYes";
            this.chkHazardousMaterialYes.Style = "";
            this.chkHazardousMaterialYes.Text = "Yes";
            this.chkHazardousMaterialYes.Top = 8.935999F;
            this.chkHazardousMaterialYes.Width = 0.473F;
            // 
            // chkHazardousMaterialNo
            // 
            this.chkHazardousMaterialNo.Height = 0.2F;
            this.chkHazardousMaterialNo.Left = 2.339F;
            this.chkHazardousMaterialNo.Name = "chkHazardousMaterialNo";
            this.chkHazardousMaterialNo.Style = "";
            this.chkHazardousMaterialNo.Text = "No";
            this.chkHazardousMaterialNo.Top = 8.935999F;
            this.chkHazardousMaterialNo.Width = 0.421F;
            // 
            // label50
            // 
            this.label50.Height = 0.2F;
            this.label50.HyperLink = null;
            this.label50.Left = 2.74F;
            this.label50.Name = "label50";
            this.label50.Style = "font-size: 9.75pt; font-style: italic; ddo-char-set: 0";
            this.label50.Text = "(If \"No\", skip to block 5)";
            this.label50.Top = 8.935999F;
            this.label50.Width = 1.5F;
            // 
            // label51
            // 
            this.label51.Height = 0.2F;
            this.label51.HyperLink = null;
            this.label51.Left = 5.572F;
            this.label51.Name = "label51";
            this.label51.Style = "";
            this.label51.Text = "State designation";
            this.label51.Top = 8.935999F;
            this.label51.Width = 1.169F;
            // 
            // chkStateDesignationEhw
            // 
            this.chkStateDesignationEhw.Height = 0.2F;
            this.chkStateDesignationEhw.Left = 6.791F;
            this.chkStateDesignationEhw.Name = "chkStateDesignationEhw";
            this.chkStateDesignationEhw.Style = "";
            this.chkStateDesignationEhw.Text = "EHW";
            this.chkStateDesignationEhw.Top = 8.935999F;
            this.chkStateDesignationEhw.Width = 0.539F;
            // 
            // chkStateDesignationDw
            // 
            this.chkStateDesignationDw.Height = 0.2F;
            this.chkStateDesignationDw.Left = 7.39F;
            this.chkStateDesignationDw.Name = "chkStateDesignationDw";
            this.chkStateDesignationDw.Style = "";
            this.chkStateDesignationDw.Text = "DW";
            this.chkStateDesignationDw.Top = 8.935999F;
            this.chkStateDesignationDw.Width = 0.539F;
            // 
            // label52
            // 
            this.label52.Height = 0.2F;
            this.label52.HyperLink = null;
            this.label52.Left = 0.06F;
            this.label52.Name = "label52";
            this.label52.Style = "";
            this.label52.Text = "UN or NA Number";
            this.label52.Top = 9.166F;
            this.label52.Width = 1.17F;
            // 
            // txtUnNaNumber
            // 
            this.txtUnNaNumber.Height = 0.2F;
            this.txtUnNaNumber.Left = 1.23F;
            this.txtUnNaNumber.Name = "txtUnNaNumber";
            this.txtUnNaNumber.ShrinkToFit = true;
            this.txtUnNaNumber.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtUnNaNumber.Text = null;
            this.txtUnNaNumber.Top = 9.166F;
            this.txtUnNaNumber.Width = 1.832F;
            // 
            // label53
            // 
            this.label53.Height = 0.2F;
            this.label53.HyperLink = null;
            this.label53.Left = 3.114F;
            this.label53.Name = "label53";
            this.label53.Style = "";
            this.label53.Text = "Hazard class";
            this.label53.Top = 9.166F;
            this.label53.Width = 0.8780003F;
            // 
            // txtHazardClass
            // 
            this.txtHazardClass.Height = 0.2F;
            this.txtHazardClass.Left = 3.975F;
            this.txtHazardClass.Name = "txtHazardClass";
            this.txtHazardClass.ShrinkToFit = true;
            this.txtHazardClass.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtHazardClass.Text = null;
            this.txtHazardClass.Top = 9.166F;
            this.txtHazardClass.Width = 0.7490003F;
            // 
            // label54
            // 
            this.label54.Height = 0.2F;
            this.label54.HyperLink = null;
            this.label54.Left = 4.948F;
            this.label54.Name = "label54";
            this.label54.Style = "text-align: left";
            this.label54.Text = "Sub. risk 1";
            this.label54.Top = 9.166F;
            this.label54.Width = 0.6970007F;
            // 
            // txtSubRisk
            // 
            this.txtSubRisk.Height = 0.2F;
            this.txtSubRisk.Left = 5.651F;
            this.txtSubRisk.Name = "txtSubRisk";
            this.txtSubRisk.ShrinkToFit = true;
            this.txtSubRisk.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtSubRisk.Text = null;
            this.txtSubRisk.Top = 9.166F;
            this.txtSubRisk.Width = 0.7549998F;
            // 
            // label55
            // 
            this.label55.Height = 0.2F;
            this.label55.HyperLink = null;
            this.label55.Left = 6.232001F;
            this.label55.Name = "label55";
            this.label55.Style = "";
            this.label55.Text = "Packing group";
            this.label55.Top = 9.396001F;
            this.label55.Width = 0.9720002F;
            // 
            // txtPackingGroup
            // 
            this.txtPackingGroup.CanGrow = false;
            this.txtPackingGroup.Height = 0.2F;
            this.txtPackingGroup.Left = 7.174F;
            this.txtPackingGroup.Name = "txtPackingGroup";
            this.txtPackingGroup.ShrinkToFit = true;
            this.txtPackingGroup.Style = "background-color: Gainsboro; white-space: nowrap; ddo-shrink-to-fit: true; ddo-wr" +
    "ap-mode: nowrap";
            this.txtPackingGroup.Text = null;
            this.txtPackingGroup.Top = 9.396001F;
            this.txtPackingGroup.Width = 0.755F;
            // 
            // label56
            // 
            this.label56.Height = 0.2F;
            this.label56.HyperLink = null;
            this.label56.Left = 0.06000048F;
            this.label56.Name = "label56";
            this.label56.Style = "";
            this.label56.Text = "Proper shipping name";
            this.label56.Top = 9.622001F;
            this.label56.Width = 1.396F;
            // 
            // txtProperShippingName
            // 
            this.txtProperShippingName.Height = 0.2F;
            this.txtProperShippingName.Left = 2.619F;
            this.txtProperShippingName.Name = "txtProperShippingName";
            this.txtProperShippingName.ShrinkToFit = true;
            this.txtProperShippingName.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtProperShippingName.Text = null;
            this.txtProperShippingName.Top = 9.622001F;
            this.txtProperShippingName.Width = 4.168F;
            // 
            // label57
            // 
            this.label57.Height = 0.2F;
            this.label57.HyperLink = null;
            this.label57.Left = 0.06000048F;
            this.label57.Name = "label57";
            this.label57.Style = "font-size: 8.25pt; ddo-char-set: 0";
            this.label57.Text = "Shipping name comments";
            this.label57.Top = 9.852F;
            this.label57.Width = 1.396F;
            // 
            // txtAdditionalDescription
            // 
            this.txtAdditionalDescription.Height = 0.2F;
            this.txtAdditionalDescription.Left = 1.563F;
            this.txtAdditionalDescription.Name = "txtAdditionalDescription";
            this.txtAdditionalDescription.ShrinkToFit = true;
            this.txtAdditionalDescription.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtAdditionalDescription.Text = null;
            this.txtAdditionalDescription.Top = 9.852F;
            this.txtAdditionalDescription.Width = 6.366F;
            // 
            // label58
            // 
            this.label58.Height = 0.2F;
            this.label58.HyperLink = null;
            this.label58.Left = 0.06000048F;
            this.label58.Name = "label58";
            this.label58.Style = "";
            this.label58.Text = "Method of shipment";
            this.label58.Top = 10.082F;
            this.label58.Width = 1.396F;
            // 
            // chkMethodOfShipmentBulk
            // 
            this.chkMethodOfShipmentBulk.Height = 0.2F;
            this.chkMethodOfShipmentBulk.Left = 1.563F;
            this.chkMethodOfShipmentBulk.Name = "chkMethodOfShipmentBulk";
            this.chkMethodOfShipmentBulk.Style = "";
            this.chkMethodOfShipmentBulk.Text = "Bulk";
            this.chkMethodOfShipmentBulk.Top = 10.082F;
            this.chkMethodOfShipmentBulk.Width = 0.573F;
            // 
            // chkMethodOfShipmentDrum
            // 
            this.chkMethodOfShipmentDrum.Height = 0.2F;
            this.chkMethodOfShipmentDrum.Left = 2.136F;
            this.chkMethodOfShipmentDrum.Name = "chkMethodOfShipmentDrum";
            this.chkMethodOfShipmentDrum.Style = "";
            this.chkMethodOfShipmentDrum.Text = "Drum";
            this.chkMethodOfShipmentDrum.Top = 10.082F;
            this.chkMethodOfShipmentDrum.Width = 0.6040001F;
            // 
            // chkMethodOfShipmentOther
            // 
            this.chkMethodOfShipmentOther.Height = 0.2F;
            this.chkMethodOfShipmentOther.Left = 2.74F;
            this.chkMethodOfShipmentOther.Name = "chkMethodOfShipmentOther";
            this.chkMethodOfShipmentOther.Style = "";
            this.chkMethodOfShipmentOther.Text = "Other";
            this.chkMethodOfShipmentOther.Top = 10.082F;
            this.chkMethodOfShipmentOther.Width = 0.592F;
            // 
            // label59
            // 
            this.label59.Height = 0.2F;
            this.label59.HyperLink = null;
            this.label59.Left = 5.165F;
            this.label59.Name = "label59";
            this.label59.Style = "";
            this.label59.Text = "Emergency Response #";
            this.label59.Top = 10.082F;
            this.label59.Width = 1.555F;
            // 
            // txtEmergencyResponseNumber
            // 
            this.txtEmergencyResponseNumber.Height = 0.2F;
            this.txtEmergencyResponseNumber.Left = 6.75F;
            this.txtEmergencyResponseNumber.Name = "txtEmergencyResponseNumber";
            this.txtEmergencyResponseNumber.ShrinkToFit = true;
            this.txtEmergencyResponseNumber.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtEmergencyResponseNumber.Text = null;
            this.txtEmergencyResponseNumber.Top = 10.082F;
            this.txtEmergencyResponseNumber.Width = 1.179F;
            // 
            // txtMethodOfShipmentOther
            // 
            this.txtMethodOfShipmentOther.Height = 0.2F;
            this.txtMethodOfShipmentOther.Left = 3.279001F;
            this.txtMethodOfShipmentOther.Name = "txtMethodOfShipmentOther";
            this.txtMethodOfShipmentOther.ShrinkToFit = true;
            this.txtMethodOfShipmentOther.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtMethodOfShipmentOther.Text = null;
            this.txtMethodOfShipmentOther.Top = 10.082F;
            this.txtMethodOfShipmentOther.Width = 0.8210001F;
            // 
            // label60
            // 
            this.label60.Height = 0.2F;
            this.label60.HyperLink = null;
            this.label60.Left = 0.06000048F;
            this.label60.Name = "label60";
            this.label60.Style = "";
            this.label60.Text = "Cercla reportable quantity (RQ)";
            this.label60.Top = 10.312F;
            this.label60.Width = 2.003F;
            // 
            // txtCerclaReportableQuantity
            // 
            this.txtCerclaReportableQuantity.Height = 0.2F;
            this.txtCerclaReportableQuantity.Left = 2.062001F;
            this.txtCerclaReportableQuantity.Name = "txtCerclaReportableQuantity";
            this.txtCerclaReportableQuantity.ShrinkToFit = true;
            this.txtCerclaReportableQuantity.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtCerclaReportableQuantity.Text = null;
            this.txtCerclaReportableQuantity.Top = 10.312F;
            this.txtCerclaReportableQuantity.Width = 0.8600001F;
            // 
            // label61
            // 
            this.label61.Height = 0.2F;
            this.label61.HyperLink = null;
            this.label61.Left = 3.02F;
            this.label61.Name = "label61";
            this.label61.Style = "";
            this.label61.Text = "lbs";
            this.label61.Top = 10.312F;
            this.label61.Width = 0.2419999F;
            // 
            // chkWasteRestrictedYes
            // 
            this.chkWasteRestrictedYes.Height = 0.2F;
            this.chkWasteRestrictedYes.Left = 3.84F;
            this.chkWasteRestrictedYes.Name = "chkWasteRestrictedYes";
            this.chkWasteRestrictedYes.Style = "";
            this.chkWasteRestrictedYes.Text = "Yes";
            this.chkWasteRestrictedYes.Top = 4.135F;
            this.chkWasteRestrictedYes.Width = 0.4289999F;
            // 
            // chkExemptionGrantedYes
            // 
            this.chkExemptionGrantedYes.Height = 0.2F;
            this.chkExemptionGrantedYes.Left = 3.84F;
            this.chkExemptionGrantedYes.Name = "chkExemptionGrantedYes";
            this.chkExemptionGrantedYes.Style = "";
            this.chkExemptionGrantedYes.Text = "Yes";
            this.chkExemptionGrantedYes.Top = 4.365F;
            this.chkExemptionGrantedYes.Width = 0.4289999F;
            // 
            // chkApplicableTreatmentStandardsYes
            // 
            this.chkApplicableTreatmentStandardsYes.Height = 0.2F;
            this.chkApplicableTreatmentStandardsYes.Left = 3.84F;
            this.chkApplicableTreatmentStandardsYes.Name = "chkApplicableTreatmentStandardsYes";
            this.chkApplicableTreatmentStandardsYes.Style = "";
            this.chkApplicableTreatmentStandardsYes.Text = "Yes";
            this.chkApplicableTreatmentStandardsYes.Top = 4.595F;
            this.chkApplicableTreatmentStandardsYes.Width = 0.4289999F;
            // 
            // chkWasteRestrictedNo
            // 
            this.chkWasteRestrictedNo.Height = 0.2F;
            this.chkWasteRestrictedNo.Left = 4.320001F;
            this.chkWasteRestrictedNo.Name = "chkWasteRestrictedNo";
            this.chkWasteRestrictedNo.Style = "";
            this.chkWasteRestrictedNo.Text = "No";
            this.chkWasteRestrictedNo.Top = 4.135F;
            this.chkWasteRestrictedNo.Width = 0.3860002F;
            // 
            // chkExemptionGrantedNo
            // 
            this.chkExemptionGrantedNo.Height = 0.2F;
            this.chkExemptionGrantedNo.Left = 4.320001F;
            this.chkExemptionGrantedNo.Name = "chkExemptionGrantedNo";
            this.chkExemptionGrantedNo.Style = "";
            this.chkExemptionGrantedNo.Text = "No";
            this.chkExemptionGrantedNo.Top = 4.365F;
            this.chkExemptionGrantedNo.Width = 0.3860002F;
            // 
            // chkApplicableTreatmentStandardsNo
            // 
            this.chkApplicableTreatmentStandardsNo.Height = 0.2F;
            this.chkApplicableTreatmentStandardsNo.Left = 4.32F;
            this.chkApplicableTreatmentStandardsNo.Name = "chkApplicableTreatmentStandardsNo";
            this.chkApplicableTreatmentStandardsNo.Style = "";
            this.chkApplicableTreatmentStandardsNo.Text = "No";
            this.chkApplicableTreatmentStandardsNo.Top = 4.595F;
            this.chkApplicableTreatmentStandardsNo.Width = 0.3860002F;
            // 
            // label62
            // 
            this.label62.Height = 0.2F;
            this.label62.HyperLink = null;
            this.label62.Left = 3.818001F;
            this.label62.Name = "label62";
            this.label62.Style = "";
            this.label62.Text = "ER Guide DOT Pub. 5800.4 Page";
            this.label62.Top = 10.312F;
            this.label62.Width = 2.13F;
            // 
            // txtErGuidePage
            // 
            this.txtErGuidePage.Height = 0.2F;
            this.txtErGuidePage.Left = 5.941F;
            this.txtErGuidePage.Name = "txtErGuidePage";
            this.txtErGuidePage.ShrinkToFit = true;
            this.txtErGuidePage.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtErGuidePage.Text = null;
            this.txtErGuidePage.Top = 10.312F;
            this.txtErGuidePage.Width = 0.408F;
            // 
            // label63
            // 
            this.label63.Height = 0.2F;
            this.label63.HyperLink = null;
            this.label63.Left = 6.401999F;
            this.label63.Name = "label63";
            this.label63.Style = "";
            this.label63.Text = "Edition (yr)";
            this.label63.Top = 10.312F;
            this.label63.Width = 0.7079997F;
            // 
            // txtErGuideEdition
            // 
            this.txtErGuideEdition.Height = 0.2F;
            this.txtErGuideEdition.Left = 7.171999F;
            this.txtErGuideEdition.Name = "txtErGuideEdition";
            this.txtErGuideEdition.ShrinkToFit = true;
            this.txtErGuideEdition.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtErGuideEdition.Text = null;
            this.txtErGuideEdition.Top = 10.312F;
            this.txtErGuideEdition.Width = 0.7570004F;
            // 
            // label64
            // 
            this.label64.Height = 0.2F;
            this.label64.HyperLink = null;
            this.label64.Left = 0.06000048F;
            this.label64.Name = "label64";
            this.label64.Style = "";
            this.label64.Text = "Base CLIN(s)";
            this.label64.Top = 10.542F;
            this.label64.Width = 1F;
            // 
            // txtBaseClins
            // 
            this.txtBaseClins.Height = 0.2F;
            this.txtBaseClins.Left = 1.563F;
            this.txtBaseClins.Name = "txtBaseClins";
            this.txtBaseClins.ShrinkToFit = true;
            this.txtBaseClins.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtBaseClins.Text = null;
            this.txtBaseClins.Top = 10.542F;
            this.txtBaseClins.Width = 6.366F;
            // 
            // label65
            // 
            this.label65.Height = 0.2F;
            this.label65.HyperLink = null;
            this.label65.Left = 0.06000048F;
            this.label65.Name = "label65";
            this.label65.Style = "";
            this.label65.Text = "Special Handling Info";
            this.label65.Top = 10.772F;
            this.label65.Width = 1.503F;
            // 
            // txtSpecialHandlingInfo
            // 
            this.txtSpecialHandlingInfo.Height = 0.4F;
            this.txtSpecialHandlingInfo.Left = 0.06000048F;
            this.txtSpecialHandlingInfo.Name = "txtSpecialHandlingInfo";
            this.txtSpecialHandlingInfo.ShrinkToFit = true;
            this.txtSpecialHandlingInfo.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtSpecialHandlingInfo.Text = null;
            this.txtSpecialHandlingInfo.Top = 11.002F;
            this.txtSpecialHandlingInfo.Width = 7.869F;
            // 
            // line15
            // 
            this.line15.Height = 0F;
            this.line15.Left = 4.768372E-07F;
            this.line15.LineWeight = 1F;
            this.line15.Name = "line15";
            this.line15.Top = 11.432F;
            this.line15.Width = 8F;
            this.line15.X1 = 4.768372E-07F;
            this.line15.X2 = 8F;
            this.line15.Y1 = 11.432F;
            this.line15.Y2 = 11.432F;
            // 
            // label66
            // 
            this.label66.Height = 0.2F;
            this.label66.HyperLink = null;
            this.label66.Left = 0.06000048F;
            this.label66.Name = "label66";
            this.label66.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label66.Text = "5. GENERATOR CERTIFICATION";
            this.label66.Top = 11.462F;
            this.label66.Width = 2.377F;
            // 
            // label67
            // 
            this.label67.Height = 0.2F;
            this.label67.HyperLink = null;
            this.label67.Left = 0.06000048F;
            this.label67.Name = "label67";
            this.label67.Style = "";
            this.label67.Text = "Basis for information";
            this.label67.Top = 11.682F;
            this.label67.Width = 1.396F;
            // 
            // line16
            // 
            this.line16.Height = 1.45F;
            this.line16.Left = 4F;
            this.line16.LineWeight = 1F;
            this.line16.Name = "line16";
            this.line16.Top = 11.432F;
            this.line16.Width = 0F;
            this.line16.X1 = 4F;
            this.line16.X2 = 4F;
            this.line16.Y1 = 11.432F;
            this.line16.Y2 = 12.882F;
            // 
            // label68
            // 
            this.label68.Height = 0.2F;
            this.label68.HyperLink = null;
            this.label68.Left = 2.616F;
            this.label68.Name = "label68";
            this.label68.Style = "";
            this.label68.Text = "Lab";
            this.label68.Top = 11.682F;
            this.label68.Width = 0.2940001F;
            // 
            // txtLab
            // 
            this.txtLab.Height = 0.2F;
            this.txtLab.Left = 2.941F;
            this.txtLab.Name = "txtLab";
            this.txtLab.ShrinkToFit = true;
            this.txtLab.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtLab.Text = null;
            this.txtLab.Top = 11.682F;
            this.txtLab.Width = 1F;
            // 
            // txtAnalysis
            // 
            this.txtAnalysis.Height = 0.2F;
            this.txtAnalysis.Left = 2.941F;
            this.txtAnalysis.Name = "txtAnalysis";
            this.txtAnalysis.ShrinkToFit = true;
            this.txtAnalysis.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtAnalysis.Text = null;
            this.txtAnalysis.Top = 11.912F;
            this.txtAnalysis.Width = 1F;
            // 
            // txtMsds
            // 
            this.txtMsds.Height = 0.2F;
            this.txtMsds.Left = 2.941F;
            this.txtMsds.Name = "txtMsds";
            this.txtMsds.ShrinkToFit = true;
            this.txtMsds.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtMsds.Text = null;
            this.txtMsds.Top = 12.142F;
            this.txtMsds.Width = 1F;
            // 
            // line17
            // 
            this.line17.Height = 0F;
            this.line17.Left = 4.768372E-07F;
            this.line17.LineWeight = 1F;
            this.line17.Name = "line17";
            this.line17.Top = 12.372F;
            this.line17.Width = 8F;
            this.line17.X1 = 4.768372E-07F;
            this.line17.X2 = 8F;
            this.line17.Y1 = 12.372F;
            this.line17.Y2 = 12.372F;
            // 
            // label69
            // 
            this.label69.Height = 0.2F;
            this.label69.HyperLink = null;
            this.label69.Left = 2.304F;
            this.label69.Name = "label69";
            this.label69.Style = "";
            this.label69.Text = "Analysis";
            this.label69.Top = 11.912F;
            this.label69.Width = 0.6059999F;
            // 
            // label70
            // 
            this.label70.Height = 0.2F;
            this.label70.HyperLink = null;
            this.label70.Left = 2.437001F;
            this.label70.Name = "label70";
            this.label70.Style = "";
            this.label70.Text = "MSDS";
            this.label70.Top = 12.142F;
            this.label70.Width = 0.4729998F;
            // 
            // lblSigningBlock
            // 
            this.lblSigningBlock.Height = 0.8599998F;
            this.lblSigningBlock.HyperLink = null;
            this.lblSigningBlock.Left = 4.066F;
            this.lblSigningBlock.Name = "lblSigningBlock";
            this.lblSigningBlock.Style = "";
            this.lblSigningBlock.Text = "";
            this.lblSigningBlock.Top = 11.482F;
            this.lblSigningBlock.Width = 3.863F;
            // 
            // label74
            // 
            this.label74.Height = 0.2F;
            this.label74.HyperLink = null;
            this.label74.Left = 0.06000048F;
            this.label74.Name = "label74";
            this.label74.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label74.Text = "Signature of Generator\'s Representative";
            this.label74.Top = 12.402F;
            this.label74.Width = 2.862F;
            // 
            // txtSignature
            // 
            this.txtSignature.Height = 0.2F;
            this.txtSignature.Left = 0.06000048F;
            this.txtSignature.Name = "txtSignature";
            this.txtSignature.ShrinkToFit = true;
            this.txtSignature.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtSignature.Text = null;
            this.txtSignature.Top = 12.632F;
            this.txtSignature.Width = 3.881F;
            // 
            // label75
            // 
            this.label75.Height = 0.2F;
            this.label75.HyperLink = null;
            this.label75.Left = 4.066F;
            this.label75.Name = "label75";
            this.label75.Style = "font-size: 9.75pt; font-weight: bold; ddo-char-set: 0";
            this.label75.Text = "Date";
            this.label75.Top = 12.402F;
            this.label75.Width = 1F;
            // 
            // txtDate
            // 
            this.txtDate.Height = 0.2F;
            this.txtDate.Left = 4.066F;
            this.txtDate.Name = "txtDate";
            this.txtDate.ShrinkToFit = true;
            this.txtDate.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtDate.Text = null;
            this.txtDate.Top = 12.632F;
            this.txtDate.Width = 1.883F;
            // 
            // shape3
            // 
            this.shape3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(211)))), ((int)(((byte)(211)))));
            this.shape3.Height = 0.25F;
            this.shape3.Left = 4.768372E-07F;
            this.shape3.Name = "shape3";
            this.shape3.RoundingRadius = 9.999999F;
            this.shape3.Top = 12.882F;
            this.shape3.Width = 8F;
            // 
            // label76
            // 
            this.label76.Height = 0.1790001F;
            this.label76.HyperLink = null;
            this.label76.Left = 0.02000048F;
            this.label76.Name = "label76";
            this.label76.Style = "font-size: 9.75pt; font-weight: bold; text-align: center; ddo-char-set: 0";
            this.label76.Text = "ADDITIONAL PROFILE INFORMATION";
            this.label76.Top = 12.922F;
            this.label76.Width = 7.97F;
            // 
            // label77
            // 
            this.label77.Height = 0.2F;
            this.label77.HyperLink = null;
            this.label77.Left = 0.06000048F;
            this.label77.Name = "label77";
            this.label77.Style = "";
            this.label77.Text = "FSC / LSN";
            this.label77.Top = 13.162F;
            this.label77.Width = 1F;
            // 
            // txtFscLsn
            // 
            this.txtFscLsn.Height = 0.2F;
            this.txtFscLsn.Left = 1.131001F;
            this.txtFscLsn.Name = "txtFscLsn";
            this.txtFscLsn.ShrinkToFit = true;
            this.txtFscLsn.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtFscLsn.Text = null;
            this.txtFscLsn.Top = 13.162F;
            this.txtFscLsn.Width = 2.489F;
            // 
            // label78
            // 
            this.label78.Height = 0.200001F;
            this.label78.HyperLink = null;
            this.label78.Left = 5.878F;
            this.label78.Name = "label78";
            this.label78.Style = "";
            this.label78.Text = "Odor";
            this.label78.Top = 13.162F;
            this.label78.Width = 0.3940001F;
            // 
            // chkOdorNone
            // 
            this.chkOdorNone.Height = 0.2F;
            this.chkOdorNone.Left = 6.272F;
            this.chkOdorNone.Name = "chkOdorNone";
            this.chkOdorNone.Style = "";
            this.chkOdorNone.Text = "None";
            this.chkOdorNone.Top = 13.162F;
            this.chkOdorNone.Width = 0.546F;
            // 
            // chkOdorMild
            // 
            this.chkOdorMild.Height = 0.2F;
            this.chkOdorMild.Left = 6.809F;
            this.chkOdorMild.Name = "chkOdorMild";
            this.chkOdorMild.Style = "";
            this.chkOdorMild.Text = "Mild";
            this.chkOdorMild.Top = 13.162F;
            this.chkOdorMild.Width = 0.4630003F;
            // 
            // chkOdorStrong
            // 
            this.chkOdorStrong.Height = 0.2F;
            this.chkOdorStrong.Left = 7.272F;
            this.chkOdorStrong.Name = "chkOdorStrong";
            this.chkOdorStrong.Style = "";
            this.chkOdorStrong.Text = "Strong";
            this.chkOdorStrong.Top = 13.162F;
            this.chkOdorStrong.Width = 0.6570001F;
            // 
            // label79
            // 
            this.label79.Height = 0.2F;
            this.label79.HyperLink = null;
            this.label79.Left = 0.06000048F;
            this.label79.Name = "label79";
            this.label79.Style = "";
            this.label79.Text = "Other Notes";
            this.label79.Top = 13.392F;
            this.label79.Width = 1F;
            // 
            // txtOtherNotes
            // 
            this.txtOtherNotes.Height = 0.3660013F;
            this.txtOtherNotes.Left = 1.131001F;
            this.txtOtherNotes.Name = "txtOtherNotes";
            this.txtOtherNotes.ShrinkToFit = true;
            this.txtOtherNotes.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtOtherNotes.Text = null;
            this.txtOtherNotes.Top = 13.392F;
            this.txtOtherNotes.Width = 4.558999F;
            // 
            // chkDioxinYes
            // 
            this.chkDioxinYes.Height = 0.2F;
            this.chkDioxinYes.Left = 6.633F;
            this.chkDioxinYes.Name = "chkDioxinYes";
            this.chkDioxinYes.Style = "";
            this.chkDioxinYes.Text = "Yes";
            this.chkDioxinYes.Top = 3.905F;
            this.chkDioxinYes.Width = 0.4539995F;
            // 
            // chkDioxinNo
            // 
            this.chkDioxinNo.Height = 0.2F;
            this.chkDioxinNo.Left = 7.217F;
            this.chkDioxinNo.Name = "chkDioxinNo";
            this.chkDioxinNo.Style = "";
            this.chkDioxinNo.Text = "No";
            this.chkDioxinNo.Top = 3.905F;
            this.chkDioxinNo.Width = 0.3769999F;
            // 
            // line18
            // 
            this.line18.Height = 5.101999F;
            this.line18.Left = 8F;
            this.line18.LineWeight = 1F;
            this.line18.Name = "line18";
            this.line18.Top = 8.686001F;
            this.line18.Width = 0F;
            this.line18.X1 = 8F;
            this.line18.X2 = 8F;
            this.line18.Y1 = 8.686001F;
            this.line18.Y2 = 13.788F;
            // 
            // line20
            // 
            this.line20.Height = 5.101999F;
            this.line20.Left = 0F;
            this.line20.LineWeight = 1F;
            this.line20.Name = "line20";
            this.line20.Top = 8.686001F;
            this.line20.Width = 0F;
            this.line20.X1 = 0F;
            this.line20.X2 = 0F;
            this.line20.Y1 = 8.686001F;
            this.line20.Y2 = 13.788F;
            // 
            // line19
            // 
            this.line19.Height = 0F;
            this.line19.Left = 4.768372E-07F;
            this.line19.LineWeight = 1F;
            this.line19.Name = "line19";
            this.line19.Top = 13.788F;
            this.line19.Width = 8F;
            this.line19.X1 = 4.768372E-07F;
            this.line19.X2 = 8F;
            this.line19.Y1 = 13.788F;
            this.line19.Y2 = 13.788F;
            // 
            // txtFacilityAddress2
            // 
            this.txtFacilityAddress2.Height = 0.2F;
            this.txtFacilityAddress2.Left = 0.05999994F;
            this.txtFacilityAddress2.Name = "txtFacilityAddress2";
            this.txtFacilityAddress2.ShrinkToFit = true;
            this.txtFacilityAddress2.Style = "ddo-shrink-to-fit: true";
            this.txtFacilityAddress2.Text = null;
            this.txtFacilityAddress2.Top = 1.54F;
            this.txtFacilityAddress2.Width = 2.942F;
            // 
            // line22
            // 
            this.line22.Height = 0F;
            this.line22.Left = 0.06000048F;
            this.line22.LineWeight = 1F;
            this.line22.Name = "line22";
            this.line22.Top = 11.177F;
            this.line22.Width = 7.868999F;
            this.line22.X1 = 0.06000048F;
            this.line22.X2 = 7.929F;
            this.line22.Y1 = 11.177F;
            this.line22.Y2 = 11.177F;
            // 
            // line23
            // 
            this.line23.Height = 0F;
            this.line23.Left = 1.12F;
            this.line23.LineWeight = 1F;
            this.line23.Name = "line23";
            this.line23.Top = 13.562F;
            this.line23.Width = 4.57F;
            this.line23.X1 = 1.12F;
            this.line23.X2 = 5.69F;
            this.line23.Y1 = 13.562F;
            this.line23.Y2 = 13.562F;
            // 
            // txtProperShippingNamePrefix
            // 
            this.txtProperShippingNamePrefix.Height = 0.2F;
            this.txtProperShippingNamePrefix.Left = 1.578F;
            this.txtProperShippingNamePrefix.Name = "txtProperShippingNamePrefix";
            this.txtProperShippingNamePrefix.ShrinkToFit = true;
            this.txtProperShippingNamePrefix.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtProperShippingNamePrefix.Text = null;
            this.txtProperShippingNamePrefix.Top = 9.622001F;
            this.txtProperShippingNamePrefix.Width = 1.008F;
            // 
            // txtProperShippingNameSuffix
            // 
            this.txtProperShippingNameSuffix.Height = 0.2F;
            this.txtProperShippingNameSuffix.Left = 6.817F;
            this.txtProperShippingNameSuffix.Name = "txtProperShippingNameSuffix";
            this.txtProperShippingNameSuffix.ShrinkToFit = true;
            this.txtProperShippingNameSuffix.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtProperShippingNameSuffix.Text = null;
            this.txtProperShippingNameSuffix.Top = 9.622001F;
            this.txtProperShippingNameSuffix.Width = 1.112F;
            // 
            // label87
            // 
            this.label87.Height = 0.2F;
            this.label87.HyperLink = null;
            this.label87.Left = 3.776F;
            this.label87.Name = "label87";
            this.label87.Style = "";
            this.label87.Text = "NSN";
            this.label87.Top = 13.162F;
            this.label87.Width = 0.3599997F;
            // 
            // txtNSN
            // 
            this.txtNSN.Height = 0.2F;
            this.txtNSN.Left = 4.210001F;
            this.txtNSN.Name = "txtNSN";
            this.txtNSN.ShrinkToFit = true;
            this.txtNSN.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtNSN.Text = null;
            this.txtNSN.Top = 13.162F;
            this.txtNSN.Width = 1.48F;
            // 
            // label88
            // 
            this.label88.Height = 0.2F;
            this.label88.HyperLink = null;
            this.label88.Left = 3.5F;
            this.label88.Name = "label88";
            this.label88.Style = "";
            this.label88.Text = "lbs";
            this.label88.Top = 3.687F;
            this.label88.Width = 0.25F;
            // 
            // checkBox1
            // 
            this.checkBox1.Height = 0.2F;
            this.checkBox1.Left = 4.562F;
            this.checkBox1.Name = "checkBox1";
            this.checkBox1.Style = "";
            this.checkBox1.Text = "Mixed";
            this.checkBox1.Top = 6.062F;
            this.checkBox1.Width = 0.6029999F;
            // 
            // txtSubRisk2
            // 
            this.txtSubRisk2.Height = 0.2F;
            this.txtSubRisk2.Left = 7.182F;
            this.txtSubRisk2.Name = "txtSubRisk2";
            this.txtSubRisk2.ShrinkToFit = true;
            this.txtSubRisk2.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtSubRisk2.Text = null;
            this.txtSubRisk2.Top = 9.166F;
            this.txtSubRisk2.Width = 0.755F;
            // 
            // label89
            // 
            this.label89.Height = 0.2F;
            this.label89.HyperLink = null;
            this.label89.Left = 6.464F;
            this.label89.Name = "label89";
            this.label89.Style = "text-align: left";
            this.label89.Text = "Sub. risk 2";
            this.label89.Top = 9.166F;
            this.label89.Width = 0.7079997F;
            // 
            // textBox1
            // 
            this.textBox1.DataField = "RcraCharacteristicOther";
            this.textBox1.Height = 0.2F;
            this.textBox1.Left = 6.381001F;
            this.textBox1.Name = "textBox1";
            this.textBox1.ShrinkToFit = true;
            this.textBox1.Style = "ddo-shrink-to-fit: true";
            this.textBox1.Text = "RcraCharacteristicOther";
            this.textBox1.Top = 6.985F;
            this.textBox1.Width = 1.577F;
            // 
            // chkCharacteristicOther
            // 
            this.chkCharacteristicOther.Height = 0.2F;
            this.chkCharacteristicOther.Left = 6.272F;
            this.chkCharacteristicOther.Name = "chkCharacteristicOther";
            this.chkCharacteristicOther.Style = "";
            this.chkCharacteristicOther.Text = "Other";
            this.chkCharacteristicOther.Top = 6.755F;
            this.chkCharacteristicOther.Width = 1.577F;
            // 
            // checkBox2
            // 
            this.checkBox2.DataField = "ChemicalAnalysisBasis";
            this.checkBox2.Height = 0.2F;
            this.checkBox2.Left = 0.08000001F;
            this.checkBox2.Name = "checkBox2";
            this.checkBox2.Style = "";
            this.checkBox2.Text = "Chemical analysis";
            this.checkBox2.Top = 11.912F;
            this.checkBox2.Width = 2.056F;
            // 
            // checkBox3
            // 
            this.checkBox3.DataField = "UserKnowledgeBasis";
            this.checkBox3.Height = 0.2F;
            this.checkBox3.Left = 0.08000001F;
            this.checkBox3.Name = "checkBox3";
            this.checkBox3.Style = "";
            this.checkBox3.Text = "User knowledge";
            this.checkBox3.Top = 12.142F;
            this.checkBox3.Width = 2.056F;
            // 
            // txtTreatmentGroup
            // 
            this.txtTreatmentGroup.Height = 0.2F;
            this.txtTreatmentGroup.Left = 0.05999994F;
            this.txtTreatmentGroup.Name = "txtTreatmentGroup";
            this.txtTreatmentGroup.ShrinkToFit = true;
            this.txtTreatmentGroup.Style = "background-color: Gainsboro; ddo-shrink-to-fit: true";
            this.txtTreatmentGroup.Text = null;
            this.txtTreatmentGroup.Top = 6.525F;
            this.txtTreatmentGroup.Width = 1.333F;
            // 
            // chkObodYes
            // 
            this.chkObodYes.Height = 0.2F;
            this.chkObodYes.Left = 6.817F;
            this.chkObodYes.Name = "chkObodYes";
            this.chkObodYes.Style = "";
            this.chkObodYes.Text = "Yes";
            this.chkObodYes.Top = 13.362F;
            this.chkObodYes.Width = 0.473F;
            // 
            // chkObodNo
            // 
            this.chkObodNo.Height = 0.2F;
            this.chkObodNo.Left = 7.302001F;
            this.chkObodNo.Name = "chkObodNo";
            this.chkObodNo.Style = "";
            this.chkObodNo.Text = "No";
            this.chkObodNo.Top = 13.362F;
            this.chkObodNo.Width = 0.421F;
            // 
            // label71
            // 
            this.label71.Height = 0.200001F;
            this.label71.HyperLink = null;
            this.label71.Left = 5.884F;
            this.label71.Name = "label71";
            this.label71.Style = "";
            this.label71.Text = "OBOD Waste";
            this.label71.Top = 13.362F;
            this.label71.Width = 0.9250008F;
            // 
            // WasteProfile
            // 
            this.MasterReport = false;
            this.PageSettings.PaperHeight = 11F;
            this.PageSettings.PaperWidth = 8.5F;
            this.PrintWidth = 8F;
            this.Sections.Add(this.detail);
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Arial; font-style: normal; text-decoration: none; font-weight: norma" +
            "l; font-size: 10pt; color: Black", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 16pt; font-weight: bold", "Heading1", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-family: Times New Roman; font-size: 14pt; font-weight: bold; font-style: ita" +
            "lic", "Heading2", "Normal"));
            this.StyleSheet.Add(new DDCssLib.StyleSheetRule("font-size: 13pt; font-weight: bold", "Heading3", "Normal"));
            ((System.ComponentModel.ISupportInitialize)(this.label1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWasteCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTypeService)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDisposition)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGeneratorName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFacilityAddress)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWasteProfileNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGeneratorUsepaId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtGeneratorStateId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTechnicalContact)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTitle)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhone)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label17)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZipCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label18)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNameOfWaste)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label19)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUsaEpaWasteCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label20)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStateLocalHostNationWasteCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label21)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProcessGeneratingWaste)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label22)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWasteStream)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label23)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSourceCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label24)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFormCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label25)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMgmtCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label26)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProjectedAnnualVolume)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label27)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtModeOfCollection)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label28)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label29)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label30)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label31)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label32)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReferenceStandards)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label33)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label34)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label35)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label36)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label37)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label38)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtColor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBtuLb)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDensity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTotalSolids)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label39)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label40)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMultiLayered)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkBilayered)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSinglePhased)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAshContent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label41)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label42)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhysicalStateOther)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label43)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIgnitable)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCorrosive)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkReactive)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkToxicityCharacteristic)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label44)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label45)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCorrosiveSteel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHighToc)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFlashPoint)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLowToc)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCorrosivePh)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkWaterReactive)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCyanideReactive)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSulfideReactive)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label46)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label47)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label48)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSolid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkLiquid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSemiSolid)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkGas)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOther)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label49)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHazardousMaterialYes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkHazardousMaterialNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label50)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label51)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkStateDesignationEhw)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkStateDesignationDw)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label52)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtUnNaNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label53)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHazardClass)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label54)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubRisk)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label55)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPackingGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label56)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProperShippingName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label57)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAdditionalDescription)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label58)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMethodOfShipmentBulk)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMethodOfShipmentDrum)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkMethodOfShipmentOther)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label59)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmergencyResponseNumber)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMethodOfShipmentOther)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label60)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCerclaReportableQuantity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label61)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkWasteRestrictedYes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkExemptionGrantedYes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplicableTreatmentStandardsYes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkWasteRestrictedNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkExemptionGrantedNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkApplicableTreatmentStandardsNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label62)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtErGuidePage)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label63)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtErGuideEdition)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label64)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBaseClins)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label65)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSpecialHandlingInfo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label66)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label67)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label68)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLab)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAnalysis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMsds)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label69)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label70)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblSigningBlock)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label74)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSignature)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label75)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label76)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label77)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFscLsn)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label78)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOdorNone)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOdorMild)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkOdorStrong)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label79)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtOtherNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDioxinYes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkDioxinNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFacilityAddress2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProperShippingNamePrefix)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtProperShippingNameSuffix)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label87)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNSN)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label88)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSubRisk2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label89)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCharacteristicOther)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBox2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkBox3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTreatmentGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkObodYes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkObodNo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.label71)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private GrapeCity.ActiveReports.SectionReportModel.Shape shape1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label3;
        private GrapeCity.ActiveReports.SectionReportModel.Label label4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label5;
        private GrapeCity.ActiveReports.SectionReportModel.Shape shape2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label6;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWasteCategory;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSubCategory;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTypeService;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDisposition;
        private GrapeCity.ActiveReports.SectionReportModel.Line line1;
        private GrapeCity.ActiveReports.SectionReportModel.Line line2;
        private GrapeCity.ActiveReports.SectionReportModel.Line line3;
        private GrapeCity.ActiveReports.SectionReportModel.Line line4;
        private GrapeCity.ActiveReports.SectionReportModel.Label label7;
        private GrapeCity.ActiveReports.SectionReportModel.Line line5;
        private GrapeCity.ActiveReports.SectionReportModel.Label label8;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtGeneratorName;
        private GrapeCity.ActiveReports.SectionReportModel.Label label9;
        private GrapeCity.ActiveReports.SectionReportModel.Line line6;
        private GrapeCity.ActiveReports.SectionReportModel.Line line7;
        private GrapeCity.ActiveReports.SectionReportModel.Line line8;
        private GrapeCity.ActiveReports.SectionReportModel.Line line9;
        private GrapeCity.ActiveReports.SectionReportModel.Line line10;
        private GrapeCity.ActiveReports.SectionReportModel.Label label10;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFacilityAddress;
        private GrapeCity.ActiveReports.SectionReportModel.Label label11;
        private GrapeCity.ActiveReports.SectionReportModel.Label label12;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWasteProfileNo;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtGeneratorUsepaId;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtGeneratorStateId;
        private GrapeCity.ActiveReports.SectionReportModel.Label label13;
        private GrapeCity.ActiveReports.SectionReportModel.Label label14;
        private GrapeCity.ActiveReports.SectionReportModel.Label label15;
        private GrapeCity.ActiveReports.SectionReportModel.Label label16;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTechnicalContact;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTitle;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPhone;
        private GrapeCity.ActiveReports.SectionReportModel.Label label17;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtZipCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label label18;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtNameOfWaste;
        private GrapeCity.ActiveReports.SectionReportModel.Label label19;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtUsaEpaWasteCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label label20;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtStateLocalHostNationWasteCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label label21;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtProcessGeneratingWaste;
        private GrapeCity.ActiveReports.SectionReportModel.Label label22;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtWasteStream;
        private GrapeCity.ActiveReports.SectionReportModel.Label label23;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSourceCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label label24;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFormCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label label25;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtMgmtCode;
        private GrapeCity.ActiveReports.SectionReportModel.Label label26;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtProjectedAnnualVolume;
        private GrapeCity.ActiveReports.SectionReportModel.Label label27;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtModeOfCollection;
        private GrapeCity.ActiveReports.SectionReportModel.Label label28;
        private GrapeCity.ActiveReports.SectionReportModel.Label label29;
        private GrapeCity.ActiveReports.SectionReportModel.Label label30;
        private GrapeCity.ActiveReports.SectionReportModel.Label label31;
        private GrapeCity.ActiveReports.SectionReportModel.Label label32;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtReferenceStandards;
        private GrapeCity.ActiveReports.SectionReportModel.Label label33;
        private GrapeCity.ActiveReports.SectionReportModel.Label label34;
        private GrapeCity.ActiveReports.SectionReportModel.Line line11;
        private GrapeCity.ActiveReports.SectionReportModel.Label label35;
        private GrapeCity.ActiveReports.SectionReportModel.Label label36;
        private GrapeCity.ActiveReports.SectionReportModel.Label label37;
        private GrapeCity.ActiveReports.SectionReportModel.Label label38;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtColor;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtBtuLb;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDensity;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTotalSolids;
        private GrapeCity.ActiveReports.SectionReportModel.Label label39;
        private GrapeCity.ActiveReports.SectionReportModel.Label label40;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkMultiLayered;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkBilayered;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkSinglePhased;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtAshContent;
        private GrapeCity.ActiveReports.SectionReportModel.Label label41;
        private GrapeCity.ActiveReports.SectionReportModel.Label label42;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPhysicalStateOther;
        private GrapeCity.ActiveReports.SectionReportModel.Label label43;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkIgnitable;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkCorrosive;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkReactive;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkToxicityCharacteristic;
        private GrapeCity.ActiveReports.SectionReportModel.Label label44;
        private GrapeCity.ActiveReports.SectionReportModel.Label label45;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkCorrosiveSteel;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkHighToc;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFlashPoint;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkLowToc;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtCorrosivePh;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkWaterReactive;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkCyanideReactive;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkSulfideReactive;
        private GrapeCity.ActiveReports.SectionReportModel.Line line12;
        private GrapeCity.ActiveReports.SectionReportModel.Label label46;
        private GrapeCity.ActiveReports.SectionReportModel.Label label47;
        private GrapeCity.ActiveReports.SectionReportModel.Line line13;
        private GrapeCity.ActiveReports.SectionReportModel.SubReport subReport1;
        private GrapeCity.ActiveReports.SectionReportModel.Label label48;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkSolid;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkLiquid;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkSemiSolid;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkGas;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkOther;
        private GrapeCity.ActiveReports.SectionReportModel.Label label49;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkHazardousMaterialYes;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkHazardousMaterialNo;
        private GrapeCity.ActiveReports.SectionReportModel.Label label50;
        private GrapeCity.ActiveReports.SectionReportModel.Label label51;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkStateDesignationEhw;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkStateDesignationDw;
        private GrapeCity.ActiveReports.SectionReportModel.Label label52;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtUnNaNumber;
        private GrapeCity.ActiveReports.SectionReportModel.Label label53;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtHazardClass;
        private GrapeCity.ActiveReports.SectionReportModel.Label label54;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSubRisk;
        private GrapeCity.ActiveReports.SectionReportModel.Label label55;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtPackingGroup;
        private GrapeCity.ActiveReports.SectionReportModel.Label label56;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtProperShippingName;
        private GrapeCity.ActiveReports.SectionReportModel.Label label57;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtAdditionalDescription;
        private GrapeCity.ActiveReports.SectionReportModel.Label label58;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkMethodOfShipmentBulk;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkMethodOfShipmentDrum;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkMethodOfShipmentOther;
        private GrapeCity.ActiveReports.SectionReportModel.Label label59;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtEmergencyResponseNumber;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtMethodOfShipmentOther;
        private GrapeCity.ActiveReports.SectionReportModel.Label label60;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtCerclaReportableQuantity;
        private GrapeCity.ActiveReports.SectionReportModel.Label label61;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkWasteRestrictedYes;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkExemptionGrantedYes;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkApplicableTreatmentStandardsYes;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkWasteRestrictedNo;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkExemptionGrantedNo;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkApplicableTreatmentStandardsNo;
        private GrapeCity.ActiveReports.SectionReportModel.Label label62;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtErGuidePage;
        private GrapeCity.ActiveReports.SectionReportModel.Label label63;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtErGuideEdition;
        private GrapeCity.ActiveReports.SectionReportModel.Label label64;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtBaseClins;
        private GrapeCity.ActiveReports.SectionReportModel.Label label65;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSpecialHandlingInfo;
        private GrapeCity.ActiveReports.SectionReportModel.Line line15;
        private GrapeCity.ActiveReports.SectionReportModel.Label label66;
        private GrapeCity.ActiveReports.SectionReportModel.Label label67;
        private GrapeCity.ActiveReports.SectionReportModel.Line line16;
        private GrapeCity.ActiveReports.SectionReportModel.Label label68;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtLab;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtAnalysis;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtMsds;
        private GrapeCity.ActiveReports.SectionReportModel.Line line17;
        private GrapeCity.ActiveReports.SectionReportModel.Label label69;
        private GrapeCity.ActiveReports.SectionReportModel.Label label70;
        private GrapeCity.ActiveReports.SectionReportModel.Label lblSigningBlock;
        private GrapeCity.ActiveReports.SectionReportModel.Label label74;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSignature;
        private GrapeCity.ActiveReports.SectionReportModel.Label label75;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtDate;
        private GrapeCity.ActiveReports.SectionReportModel.Shape shape3;
        private GrapeCity.ActiveReports.SectionReportModel.Label label76;
        private GrapeCity.ActiveReports.SectionReportModel.Label label77;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFscLsn;
        private GrapeCity.ActiveReports.SectionReportModel.Label label78;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkOdorNone;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkOdorMild;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkOdorStrong;
        private GrapeCity.ActiveReports.SectionReportModel.Label label79;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtOtherNotes;
        private GrapeCity.ActiveReports.SectionReportModel.Line line14;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkDioxinYes;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkDioxinNo;
        private GrapeCity.ActiveReports.SectionReportModel.Line line18;
        private GrapeCity.ActiveReports.SectionReportModel.Line line20;
        private GrapeCity.ActiveReports.SectionReportModel.Line line19;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtFacilityAddress2;
        private GrapeCity.ActiveReports.SectionReportModel.Line line22;
        private GrapeCity.ActiveReports.SectionReportModel.Line line23;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtProperShippingNamePrefix;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtProperShippingNameSuffix;
        private GrapeCity.ActiveReports.SectionReportModel.Label label87;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtNSN;
        private GrapeCity.ActiveReports.SectionReportModel.Label label88;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox checkBox1;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtSubRisk2;
        private GrapeCity.ActiveReports.SectionReportModel.Label label89;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox textBox1;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkCharacteristicOther;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox checkBox2;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox checkBox3;
        private GrapeCity.ActiveReports.SectionReportModel.TextBox txtTreatmentGroup;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkObodYes;
        private GrapeCity.ActiveReports.SectionReportModel.CheckBox chkObodNo;
        private GrapeCity.ActiveReports.SectionReportModel.Label label71;
    }
}
