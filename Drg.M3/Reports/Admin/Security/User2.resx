<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="profilePicture.ImageData" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>&#xD;
        AAEAAAD/////AQAAAAAAAAAEAQAAABZTeXN0ZW0uSU8uTWVtb3J5U3RyZWFtCgAAAAdfYnVmZmVyB19v&#xD;
        cmlnaW4JX3Bvc2l0aW9uB19sZW5ndGgJX2NhcGFjaXR5C19leHBhbmRhYmxlCV93cml0YWJsZQpfZXhw&#xD;
        b3NhYmxlB19pc09wZW4dTWFyc2hhbEJ5UmVmT2JqZWN0K19faWRlbnRpdHkHAAAAAAAAAAACAggICAgB&#xD;
        AQEBCQIAAAAAAAAAAAAAAOMbAADjGwAAAQEBAQoPAgAAAOMbAAACAt4bAACJUE5HDQoaCgAAAA1JSERS&#xD;
        AAAAkAAAAJwIAgAAAB/mtZQAAAABc1JHQgCuzhzpAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAA&#xD;
        gIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAA9gAAAPYAF6eEWNAAAbR0lEQVR4&#xD;
        Xu2dh3caybLG7/v/z7vv3Ou1FQAJlJzWcdcKxCEJKyByzkERFAABIs77empAyLbWQoOwkYZTR0eWCUP/&#xD;
        5uuurq6u/h+e5/8lPyaoBQBMfkxQC/xrgq5VvlTWHcqtMFktIAObLF6ywiaMlwxMBjZpLTBh1yuPYTKw&#xD;
        CWuBCbtcWWEysAlrgQm7XFlhMrAJa4EJu1xZYTKwCWuBCbtcWWEysAlrgQm7XFlhMrAJa4EJu1xZYTKw&#xD;
        CWuBCbtcWWEysAlrgQm7XFlhMrAJa4EJu1xZYTKwCWuBCbtcSQrrjvHR4bvMunzfWjz/nTVaPKwJo+cL&#xD;
        rxGN7/CC0Vu0YWO8/OuPkniDyMDGTe2pALtLu3b4umCQV7P3fKiMhHVDW3ynCbvLe478OTKw6yaVgf3k&#xD;
        bhj53ffNGw6OWIP/xXfb3xqNSXxDsCYz4SFqq83zMOE5pC2+U4c99PX/8P0fs8JkYN/T/a2djtuAier5&#xD;
        Tmctvg0b9CdveomCf9htCtaAyQobcQvIwCZMYX3+nU6n3W7jJzw+GBORMCrBMF5dtZk1OnyV58+b3VKb&#xD;
        r/N8jecLl51CqXPZ5Bs8X2ny1aY4b6u2WpV67QpqHHjgzZvNJn7ibyO+726+3WMewxqNRqvVYpz6DwEY&#xD;
        uLXa3XqrC06EDYbmB7CTauOgVD+ptk4q7eThRTRdTOTOUwcXkfRJKn9yAVbidBv9KY83p/uAGvH6tnhI&#xD;
        Yo8ZWKdR6zbr3U4LPiFxggt4BecdimnzZy2+1OUveb7M8wc1Pl5shIqdreQ5Fziwho/toYLBlfviiP29&#xD;
        GdXuJP62hwzOaGC/fNzkzyFEnr/gedwQUBWYMX+yxwx/eUhe4gfdG9tv7XTwXaBitGCghZ4QtGotsccD&#xD;
        JwA7ueJjx5f2QHZ107e2m3hn8b3UO9+Y3O85/xu9++WG85V2753R/XJ9+4Np1+pL+/LF0NFFtHiRKkFu&#xD;
        osLAjFoQqEBRBnbPFhA9utYV37pi/h00wfNXPFMV7KjDJ8r819zVF/f+EhdUrDnVpphKH4bNGSJzhqhK&#xD;
        H9EY48uW9KIxMrfh1xi8bx3xT84YbN2ftqYKfU5EjpjJCrsnLdZNwf9u1dtXVdY3CqMXgMHO2vxhlfcf&#xD;
        Ny2ho/dbyWUuoDH4Fox+oJo3RjVcHOQADDYIbF7nXjT5F4y787qtVzb3qjd5eXlJAxiAgRMBox7y4R73&#xD;
        7gzphb93l9iu8o1yt1Hl21cErNzmizXek684osd/7eVeWkJKY0Rlis5Z05rN3BSXnjZnpo1ZshlTbsaQ&#xD;
        mdKl5vBHXWxKF1CYIkqTb0q7p+TcL3diuVyuWoWnwtwNMJOBSb1N+U6NB7MOJrnw3rFq0jksNROHF6tb&#xD;
        4fdm97zWpVp3KgxhtSUBYEpzctaaA7AX+vSUIaO0HMzbjxWm3LO1mMaWB7DnGz4lF1VbQ2CmMLnUNn8g&#xD;
        EDg9Pe1zol9ATup1/+PrH7XCGmUwg6Nx2egUMK/i+c18W20MoveDLRp9sHlTGDbDpWBKU2Io+3Njyxk9&#xD;
        Oa3zl4LzCYkhFtJssyCIYEJMRJi9ixGTIUlCuN8/HjUwyKt1iaaEN3/E8+FzftVfnDcERgXsnXbbuBOL&#xD;
        5s/OGzxGMHwQaLUQGpaBDXlrik9nN3izVu3ysHCF14eKGmuS9YHGYF9nGMBgbOgyZ4aSF578yuCFGfaS&#xD;
        sWL7osMmdhXMzJjOWLx/UGEU9R/2Wzw9hbXqcDcuO/zhRd0cL72xR2f1IdiogL3UexbWdt8Z9+zBg/Rp&#xD;
        tXDF19ptGdiw9+X186+uMOliU+OdSH7FFteYQvAsZowxUhWNXgouAXthzsGGVZhCH1Ma4hpDYMUStQbz&#xD;
        qTKb3sFrpOl6F+EVzNkHItDDfpMnpzAEHTArShRqGw7vzJqLCcuenTXFRwVsVhfVWDKLppBizfXB4nZl&#xD;
        L84aHTCTgQ17a4rPh9t2fnm1FS/AiZ/SBpWmmAIOujlDqiJs5B8+N+dhwyrsmSE3bTlSYSagjWKS8NmZ&#xD;
        DR+XEGaErmG0riYubZOvOOTjySkMwA6LF0ZP+oPFo+Lis4bIC1NqxpIdFbA/jHmYkkuDGaZ0Gr1nO5aT&#xD;
        gf38tuxPTeip/RuzDm/+vPrRHlzR7c7bMi90YQxUz7lsb9YVY5oT1HY/hf3XXHhmKU6ZsrB5U1Bl8Ovd&#xD;
        sWyNBZdhosIkjGE//OaPYR72D8AOzy4fGtgLY4aAKfU+nSuaqcrAfqax24Bh+nVwXntvDy3pnIMKI1WR&#xD;
        lwiRwe43D/vDfPLcUkA3C5s3+pQ6t3Yvkr5kq9UwWupkIeheZuPPvsed/v8xKwwzsP2z6jtbEMAQLex3&#xD;
        iQ8BbM7gBbANZzhV6crAfnLr3aawyxa/XyxjTXJpY5uAQUkIyZN/SNPnwdnYsF7iNJefMe/PGmKwBYNb&#xD;
        rdvTOUOZCnIWWa9ICqMh7H6xxCc3hiFzJl8oAdji+tZDAJvC+osATGGML5m8YGZ0R/M9p0MGdqvOblNY&#xD;
        ucHnTi7+NHsZMHMCUzH4h9OmJKmKovWkMxrJhlXYrDGt5LJYnsay5wrnWTa5Lb7YUUN0OgaBiTq70yB1&#xD;
        1+7kfoPZb7GAORQwMBs5MKxQvzR7YbZA4kTIGfmmS3yiwAbv2RvJ8bTm22bbvxBiwPQLwz5+KfF88Kj8&#xD;
        2RFmClv3qbUBpTbJDLNdZhlmpC0uyGzI9bAZR+Z/dYEZfVhjSy+te94YQ4HYYQmsaHmFpWOVKWb/RMew&#xD;
        24CJ82UhzQacakgJFZgh6ODNn2EeBqdj0RBe4WIaU24eq8mjA/ZvfVCBNBBbenHN/ScXCSdPKiAlAyMk&#xD;
        twET816ErA0mrx6wM5737F98dESW9XvLXOyVNak251VMWDnBrhWmMoVhQyvMnv6PIYyg17wlubjm/WBN&#xD;
        xvPnSPehXTBIfmQmK4zGg8EukVLMKAcbtCiXDYZcT/9R5fNWbMXgWjJFwUxpSM9oE6MCNm1LPUMCjzkB&#xD;
        W1r3rW7v5wo1fK4M7Dr63p/ZDALDf7PUJbRTs90Hhi7xsM07s5V3m/EFnWfeEFEbowp9YlYXR2qNYBkY&#xD;
        jWH3U9i0DUFIAEupuOSKLmj0nR2XKYqIOwf3EJbGqrLCxKnoN04HSwhEYm+jhS4RCsMYdlxuuPJVgzf3&#xD;
        kgtqtO455BwaIipjap4Dp1ECAy30pW+52FaycdHgEV6Rgd1JYQAGhVGqKGh5Y9lP2+nXljBWhBcw2TLF&#xD;
        MFtSIuiH5RVBWwpTCqZi8hLXoIcdw2Zt8SlzBLNm2IfNfc8Ry/0+RR4JUupYx4ibpy5mdggbLkYxDZuo&#xD;
        3PrbnA5KvwUwePYELHV0zm25ISz1houW8Bcw0hgiM/o4mI0WGEU6Pm8dBU9ZisBJXQbWuzP/YR5GTgfb&#xD;
        RNTlS1fdYOpAZ9uBo4G0eAxd6AyVkBfGMEFVg6YyJmDzRuhv6EiHwp6YscamtHAUk593Tja8pb1UBZGO&#xD;
        cruLTEWh5EcDe2fY9hm2RVpWWH/PqxDqoF1fbL581fUn8qumTdAiYDDq+h4I2Iw++saafWvL6Z0ZX/5K&#xD;
        BvaTMUxMlu51iQghsgFMa54zxGEQ0JwpiXwpljLFJfomJgqICoPIhkv7xYA3a43AoDAAWzCl5nSx10af&#xD;
        0Xd03maDGcYyGCIwMFlhN+ZhYlJ7u0tjGFZVfPHcZ51FpccmFBGYypIaObApLjhjCWMMA7wlcwbAkFeq&#xD;
        c+/LwH7uJbI4SA8YpmKR7DHGMEyTWVwDriCHsDqz2xSmNqRgw3qJL0wBKAwD2LwlvWLdn9cnX5tCBl+h&#xD;
        iCxgNnNvw2SF/WAeRruYAQz7UyjMkStWdvwxBKLmuRxoKYzJWcFGC2zaHFLaYwCGufMS8lA3Yq+MQaO/&#xD;
        KAP7icIoNEXzMNBiW/bq3fh+cdF+orYcYslqxpB6ro+9MMSnuPi0OTFjjsEGx7D7KWyWC6isLAIJt15t&#xD;
        TCsAjIuaI5UeMHhASOGSx7DvYom0mY5CUxTpQLjhqHS17Cgu2I4BbEqX+EMXBTMAm7EkRwVsGok3lhCl&#xD;
        CACYSpd4v5neyrRkYPcMEQRzRa3DvbRqe6PffrsZY/stsRcWChOyfQezE3sZVMM5iv/lMv9nSKpsGbUj&#xD;
        P7vhXDAHtuNHmYoYehZz62mpZXS1qe630Nx/1W+x4nwbz8xZ/Wsg+dHi/mB2gZZa557WhbAUMipgM46j&#xD;
        Z+as0poGM4V275Uj5j8oF4VFVBazp80QMrC7y63S4XOnNW/23Jk4WfMeLpn82Gu0aIev+K3CKI9qWC9R&#xD;
        jbfCxJwLaczhRe32l9148vQSi2BiLHEQ2OhqUz1mhWGFBcyw7lxs87uHbfSKbHOY5Qdd4v2AKTh4LrE3&#xD;
        25lPnqO1veRm4vSw1kFegAzs7qK68cxyi3kfVJVj77D12hL8Y9WDHZijUtiC3vn+a2Q7fRq96KROaweX&#xD;
        YkaiWC9YUFiv+qLw+ygej1xhWBhDH3Xa4r9mqi85/wuk4oxOYWrtzhdXOlbqYpp8gaCzMKOoINhLBZ5l&#xD;
        YMPeoHD2kVwBeSFW5N6vIRtHqQ8sWuM0Axv0Eu/XJb7TWuzeaKHWxCpztdmpod4D3I1mv6YpUxUtZvYQ&#xD;
        DvsNfvD8x6wwtBwqS1GXiMJff29FZ7U+hc4/KmBrtl1XPH8hJCKW600CJhgVoZWBDXmDIliF5is1Wcpb&#xD;
        6KTx0epd4FgJgUFg9Pv9Mn+Nm1+zJ8VGB2X8ervAOlgAEyt0EDbSVm8ny5Bf4EdPf8wKe2hgtt29w3PU&#xD;
        w0RxTFCBf9Fpt+odRMcotU0GNuz9STFGLI9hGhspND5ZPajahjpgpCcaw6QobMcXKF7WCBjqPbCCU82a&#xD;
        AIwqAlOXKCvsztwIGPawwHmLFpufbd7RAtsLhs/rDXwGusRWq8HivKK8ZGB3hnTjiXDfui2UikWbouDo&#xD;
        3zYPKu0tYOfdiBTmDMZOay2WVYeFAohMCEcJyRtClygrbGhqAjAq7Js8a32xe1fMwREC2/aFC5cNtjjA&#xD;
        ajwzVuKpHzKwoVHRCwaApc8aa3b3bcB6I9lw0XqHP35UZdGNSq+am7CB5hu3nsYwqo0zgsdj9hIJGOqb&#xD;
        497PnDcfAthxrQtgmOdRzbhmG1W8b4Sjek7HkwR2YxOqcLMOJmzTVIgCr7TAwQ5J6bLFTfwldtoyeVJL&#xD;
        ligqTg3GEqV4ie7MUbbSFDLokSF1xQqDCetePPKPYcIFtZB8RznAwi99G9z7fPP0pJ+cTzZJChsamNAV&#xD;
        0auSF12jOwlac3r/qIC50ocAhlk5REZuPc+yApB6IwOjIUnQyuD2095ZQ9eneQ3qDDcjaidX4REgebvM&#xD;
        6/YSKgPq1QQpnVT6PMwWzoWLdVRKFCq4sXkyaboHjJ2GJF6PMIummh2D1tPW4Plk14cI/nDEmySFDQsM&#xD;
        uR4ootwHtr4TRSBRYxYTtqUD+8vhdcQO8iVUqhVPBUG9dTZwigqTgQkKoy6uVyWNTvPCgAH7VmeI/MJo&#xD;
        jAmfdj9thgBsycaCh/1ovZQxDDstsEHGnakc1Hsb0YVUO1H3A4VkxQ1I4tljdM10GhnZoKoGJTjh0XpS&#xD;
        2N2B0TNBC8Vdd9KsYAeWVxYsYoKbdIXNrTlXTAHOk4sWOlgsZVv5KKOLwMjABrvEuygMS5dYV0QakzN1&#xD;
        +tduesXkmzOG+lVIpQObx+aljchrcwy5iKkLHgUfsF7axyYusoin/gm7nsWu8ro/6KFlh4301MQU9g9l&#xD;
        IiZ1DLsLsAIKq5Ta2/ETRBE12r0lgwcDGHM6RtQlIp9+ZjWo/Hvv83bem6tkK+JSjtjctCr2FID9sBIn&#xD;
        O+yJjqIUjr2h0zPaLXEDFvZg4Y8YP6rNLjsOR0gnjZV5LnSMzACgQunzOa0bSTgorE2xRMJ2w4S9lN/b&#xD;
        rdlUxozKlFWbkhou9actvuE5dqUvUGG7iC1ivbkgS0nsIN6I2QXWYISlMrHENqIibMc2+0I0pxQevSVQ&#xD;
        QWSTUrf+NmCIWTDnWPhW7LsNnNgLwdFmCOo24RliVcUaLaLmgxopnjrXoikwr2MZOCMENqtPKbDfQo8j&#xD;
        dmILWu9LU2jVEUJVdG/qMH5cKlTr2CgGWrV2E75jE7cZxfJ7wFgnIfr11/0cIiMgSbtJJx4YHSLUvxlZ&#xD;
        KEM4eo9yN+BcsKV6pNx0+Ph515UtvbQnVDrfzIYX4xZ1hlDYvBm7ZkejsFlzVmHJoX4sm9ghGUsXxD2B&#xD;
        9TbUnfqwxepso2xztFjLVdlmFtIc7ehlJoQf+z4UvgLj1DMx0j/pCgMqkZBADg/0iiidXceZiMIxbjCk&#xD;
        IEZP6pwvi8LMFNdgdcCscTiHcBEBbA7VOkYEDLSU1vycJcsMb4ukUr0XNr+xrdHtvjHsfLR6tLtBRySH&#xD;
        4s3ZSqvUaGMhhrDRSRJ05Yj0wygOSeFjUt7EK4x1951Ws1GH0VmJbExHMRU6DbHDpyv89n4TSb7AM73u&#xD;
        EfenCJVqUK8GRaFQpxm59b0MjmudiR7jkGMYNsJgd8ULUww2ZYxNm8R8rMXNrNqKGqVRhRaa8y1x4XeO&#xD;
        5F/OvDNTRKGXVKl11GTuKzRHNxkZClLBxBU1YfLyGIB1ICrQwqAgjNL9sytxYg3SbOzhQ5wHhpRsct+n&#xD;
        9JFpQxSQYEQLqABvVMCU9gz2woDWc2MUtLD5jG4Rqo2Dc8hgqGqPohMLep9G531n3l3dDtiCaU/uNH1W&#xD;
        P8YpOz1akB25VCyrQFi5Zukhk94lsjAdztpr1PCTnI7KVfu0XIseVVyJozV3/o01hFJdDI8lq7Tv/2FI&#xD;
        PDcmsS0MhgMhsGUWOhuk1fcV76ewua8ZhT3J9i/BcITBZm7aihMmMv/ZiD/TJXGmlcK8r7EewLC1cM6U&#xD;
        Va1vafTO11bvp50o58vsJIvRo1Lm/Oq03rjARqlWW3CphFwr/KtZfRTA2g0K1mEAq9Vq+ydnsXRevx34&#xD;
        2+Jc1KMwxx6QoDvCSWAoFQts+GXWzHY/gBY7JAz1mHVs01HfbszJhu0SLbEXHGrhxwAMtGBTluwzbPW0&#xD;
        7E9zOQDDUWTkSWLzLmzJ5F40uhb0O2rt1ivdFkY4kzPoCCQzhdODi3KpWgOzXiSHBUp+O2Bi5IJOUKCo&#xD;
        GtJm4EpcXrId+L21W3wNGqtLQqI84hc4TiV42LAFDv/aSv9pQWEHZBuGqeLGDJeBob1gw+5Geejn0wkv&#xD;
        VMF7sFA0TvHBqRK2yAmyXU+aVGaRnfvI2gOhf7Zu05vN9I4gvne8Q9L+sNuAwT+CoTNvN5GRIXgWgmdV&#xD;
        xqGwNRwKW9uJHGxsxz+afa9NQdikA0OaCcZdhDrXncnd+HHitFlssvuyP4sWDo0GPdYs90ZFL5QEjGLY&#xD;
        vVOABuLWgrbqTf5KiKhCW+yOa2F2xbuyFa0r+8bk1aztqVedaq1v0RCEPybUkWIZGb21Lqazh1bMsO9P&#xD;
        2hqMYVJOP62DY78h7I3Zt+7KbKcuAictJOghZCOeC46D5Jos5Pg7AqNOAN0AS/nDVoarTrJQ8mcOjZ7s&#xD;
        6lb0Ledb1jqXtJ5lnXdBH9Do/I8A2LI1hgQhFI1GiX1Ud/xgC6zuxOO5wuFZDTeu4D0iusVmNb8S2PdH&#xD;
        D1LYtI4aTcJwdd7iU6XOXrqodSc+2JEZj6pRmAuzPnDRklyypuA3o+QXlSXq3e8otyFWkxpWAQ/9/NtS&#xD;
        E6Y2/AiUIASDrVBqLoIJCeaRy/bkmsPvCOVTxSoWtWlQYIO9tIekLvE2YLiLwGy/1AlkTjlv8rPNvaLf&#xD;
        0qzZwImA4SdUhYp6VFRv0oGBFqIkMPaLPoAQGrCB2cqq9bPFtR1KY7M2KtP+DsCQhiZWFCRt4bhlGIar&#xD;
        VLFtixT/+hpb4gJqvUeld2PXN8XuUMYB9fNmDAkYaqWwEjcmpqpBfZDH+NCKGfb9v18fIM2pHQdKaxYF&#xD;
        9VGYguaOMPyi1u1i3vZ+02tPHOerbQzkYCbxIVFhPwYWP25shw4/2EJLG7s4UH4ZnbsFFSp9LwxJMFPb&#xD;
        DjT2Q4RcqbjNIwCmsGRAa8qYwE9MHOlUOjB7ZfWD2aLOsboXjRbK578cWLOBcYrlGmHXIlWaP6jxrtT5&#xD;
        W3P4DRdaNKNqudhRTBtCU/ogzjoRjM2x2DHmMGHWRZ4h3e+krZud5O8jtW9XCUhh5D3SLI2MzjNTcaix&#xD;
        7l/kvMsWv86TzNRZyFTiQ5LC2i1gEs4wb7ewe/z8ivfnL417KLHFZldwMQAMXi9buTCGweypAZszh8AM&#xD;
        wGBfdsKRc5xLLfUhCVgDG+CEIxQw24C2/PnKF+f+kt6v4RJqHC3KIfSXRF2FWZyzzDEjPfVUdV23d3AM&#xD;
        ExU2UWPYoM5IbWQIs8FQBARn/722+rfz1ROp0zBpE+c+MFQ4DB/V9buxRZ1vbnVvsKAr1cKg6lBPFhjO&#xD;
        /ls07HHRAtZCJT4kKQyFcWmNOF9uccHCit4zu+5HuVecGsT8QFFMiVlWTBSxdip//SP7LfX0vQ/5gyyS&#xD;
        Gzusr0PS4hKrEWsLCZSXVqGG+4Z73XuMWI/EhyRguF0wiCGS600ff7BH1as7Kl1o2SpWvZaBUToQgLFQ&#xD;
        zoYbS6A+pKxKe0gCxvIGsZn1tKV3JWiJb86UxkEAszj5ru8B0j3IhWFsJZAd98Vi82QUlJr0ifPgQeBU&#xD;
        V653qllq1gCFISsrMb/hfedI26NSJSYJGJz6sw7vyZc/2ZAH4V5BlS1LbnojCmDMzDi/V1yGUOA0clq6&#xD;
        fUrAUEu1D2xu3fPKEtN7DqQJTJrTgVyiZIU3hgrIG5xdd7MiGjbMhcNYbhcsKRj7HefRw36YNHiPP94W&#xD;
        oRj2rR76fchPJkOVWrU1o7FlfzEwLNmtunIAhnw0AJuz4CSbiAysfyvQPcQCAgIzVIP+lcCOeX47U3pr&#xD;
        C7PMXKpRw5a1WPpRX2GkraepMKCi+rR0WgiwgdmvBJZr8sbAPhIxkI6BBaF5QwAJF8g3koGJi7G9HJM+&#xD;
        MOjsVwKLXvCru5nZL9tYI1/ZTCE3DQfBAhhFdWkM62krCvENO8bc9vyHHnuGvc7brodGr/7/Uq/4K4F5&#xD;
        DtufHHEA0+g92GcHYKQwGRhBoqPkBoH9YoVtpqofviaV626kx9L0ENrHNREwMnHUpbOJbklDG/bvE6Qw&#xD;
        KopPgxkNY79SYRI/W375PVpA0sT5Hp8nv0RiC8jAJDbguF8uAxt3i0v8PBmYxAYc98tlYONucYmfJwOT&#xD;
        2IDjfrkMbNwtLvHzZGASG3DcL5eBjbvFJX6eDExiA4775TKwcbe4xM+TgUlswHG/XAY27haX+HkyMIkN&#xD;
        OO6Xy8DG3eISP08GJrEBx/1yGdi4W1zi58nAJDbguF8uAxt3i0v8PBmYxAYc98tlYONucYmfJwOT2IDj&#xD;
        frkMbNwtLvHzZGASG3DcL/9//4F27LbAfuUAAAAASUVORK5CYIIL&#xD;
</value>
  </data>
  <metadata name="$this.TrayLargeIcon" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>