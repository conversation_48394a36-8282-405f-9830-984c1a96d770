<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" auto-import="false" namespace="Drg.M3.Domain.Radiology" assembly="Drg.M3" default-access="property" default-cascade="none" default-lazy="true">
  <class name="Drg.M3.Domain.Radiology.WorkflowStep" table="RAD_WorkflowStep" >
    <id name="Id" type="System.Int32">
      <generator class="identity" />
    </id>

    <many-to-one name="Workflow" column="WorkflowId" />
    <many-to-one name="WorkflowTemplateStep" column="WorkflowTemplateStepId" />
    <many-to-one name="Task" column="TaskId" />

    <property name="Name" not-null="true" />
    <!--<property name="DaysToComplete" not-null="true" />-->
    <property name="Instructions" not-null="true" type="StringClob" length="1000000" />
    <property name="IsInactive" not-null="true">
      <column name="IsInactive" not-null="true" default="0" />
    </property>
    <property name="AssignedDate" />
    <!--<many-to-one name="Distribution" column="DistributionId" />-->

    <!--Common audit fields-->
    <many-to-one name="CreatedBy" column="UC" not-found="ignore" />
    <property name="DC" />
    <many-to-one name="ModifiedBy" column="UM" not-found="ignore" />
    <property name="DM" />
    <many-to-one name="DeletedBy" column="UD" not-found="ignore" />
    <property name="DD" />

    <set name="WorkflowStepLogs">
      <key column="WorkflowStepId" />
      <one-to-many class="WorkflowStepLog"/>
      <filter name="bagDeletedFilter" />
    </set>
    
    <many-to-one name="Association" column="AssociationId" not-null="true" />
    <many-to-one name="Organization" column="OrganizationId" not-null="true" />

    <filter name="associationFilter" />
    <filter name="classDeletedFilter" />
  </class>
</hibernate-mapping>
