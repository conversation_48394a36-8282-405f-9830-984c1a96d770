<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" auto-import="true" namespace="Drg.M3.Domain.Quiz" assembly="Drg.M3" default-access="property" default-cascade="save-update" default-lazy="true">
  <class name="View" table="QUZ_View">
    <id name="Id" type="System.Int32">
      <generator class="identity" />
    </id>

    <property name="Name" not-null="true"/>
    <property name="ViewType" not-null="true" />
    <property name="IsInactive" not-null="true"/>

    <many-to-one name="Template" column="TemplateId" />

    <set name="ViewElements" table="QUZ_ViewElement" lazy="false" cascade="none">
      <key column="ViewId" />
      <one-to-many class="ViewElement"/>
    </set>

    <many-to-one name="Organization" column="OrganizationId" not-null="true"/>
    <many-to-one name="Association" column="AssociationId" not-null="true"/>

    <!--Common audit fields-->
    <many-to-one name="CreatedBy" column="UC" not-found="ignore" />
    <property name="DC" />
    <many-to-one name="ModifiedBy" column="UM" not-found="ignore" />
    <property name="DM" />
    <many-to-one name="DeletedBy" column="UD" not-found="ignore" />
    <property name="DD" />

    <filter name="associationFilter" />
    <filter name="classDeletedFilter" />
  </class>
</hibernate-mapping>