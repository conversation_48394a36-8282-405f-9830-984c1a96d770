<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" auto-import="true" namespace="Drg.M3.Domain.QA" assembly="Drg.M3" default-access="property" default-cascade="none" default-lazy="true">
  <class name="TESpeciesLink" table="ENV_TESpeciesLink" >
    <id name="Id" type="System.Int32">
      <generator class="identity" />
    </id>

    <property name="Link" not-null="true"/>
    <property name="Text" />

    <many-to-one name="Species" column="SpeciesId" not-null="true" />
  </class>
</hibernate-mapping>