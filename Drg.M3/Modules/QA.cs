using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Drg.M3.Domain;
using Drg.M3.Bll;
using System.ComponentModel;

namespace Drg.M3.Modules
{
    [DisplayName("EMS")]
    public class QA : AbstractModule
    {
        public override IEnumerable<ReferenceType> ReferenceTypes
        {
            get
            {
                return new[]
                {
                    //ReferenceType.LocationType,
                    ReferenceType.RpcsCode,
                    ReferenceType.AspectType,
                    ReferenceType.PracticeType,
                    ReferenceType.BSO,
                    ReferenceType.MissionTag,
                    ReferenceType.AssetCategory,
                    ReferenceType.AssetType,
                    ReferenceType.EvaluationType,
                    ReferenceType.Priority,
                    ReferenceType.Media,
                    ReferenceType.Regulation,
                    ReferenceType.Iso14001,
                    ReferenceType.FindingType,
                    ReferenceType.FindingReference,
                    ReferenceType.FindingCategory,
                    ReferenceType.ObservationCategory,
                    ReferenceType.RootCauseCode,
                    ReferenceType.ObjectiveType,
                    ReferenceType.DWClass,
                    ReferenceType.WSOStatus,
                    ReferenceType.DocumentCitation,
                    ReferenceType.RegulatingEntity,
                    ReferenceType.FindingTier,
                    ReferenceType.Ecosystem,
                    ReferenceType.ConservationEffort,
                    ReferenceType.ProgramArea,
                    ReferenceType.Partnership,
                    ReferenceType.WsoCertLevel,
                    ReferenceType.FindingClassTier,
                    ReferenceType.FindingCategoryCompliance,
                    ReferenceType.ResponsiblePartyType,
                    ReferenceType.AuditInspectionType,
                    ReferenceType.RegulatoryMedia,
                    ReferenceType.AgencyLevel,
                    ReferenceType.InspectingAgency,
                    ReferenceType.ConservationSeries,
                    ReferenceType.ExemptionType,
                    ReferenceType.SpeciesGroup,
                    ReferenceType.GuidanceArea
                }.Union(base.ReferenceTypes);
            }
        }
    }
}
