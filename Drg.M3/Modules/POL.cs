using Drg.M3.Domain;
using System.Collections.Generic;
using System.Linq;

namespace Drg.M3.Modules
{
    public class POL : AbstractModule
    {
        public override IEnumerable<ReferenceType> ReferenceTypes
        {
            get
            {
                return new ReferenceType[]
                {
                    ReferenceType.CRDM,
                    ReferenceType.DiscrepancyArea,
                    ReferenceType.DiscrepancySource,
                    ReferenceType.DiscrepancyStatus,
                    ReferenceType.DocumentType,
                    ReferenceType.EmptyEquipmentMethod,
                    ReferenceType.FillEquipmentMethod,
                    ReferenceType.HighLevelAlarmType,
                    ReferenceType.InspectionStatus,
                    ReferenceType.InstallationType,
                    ReferenceType.InventoryContents,
                    ReferenceType.InventorySections,
                    ReferenceType.InventoryStatus,
                    ReferenceType.InventorySubType,
                    ReferenceType.InventoryType,
                    ReferenceType.InventoryUsage,
                    ReferenceType.LevelGaugeType,
                    ReferenceType.PipeCathodicProtection,
                    ReferenceType.PipeConstruction,
                    ReferenceType.PipeCorrosionProtection,
                    ReferenceType.PipeMaterial,
                    ReferenceType.PipePressurization,
                    ReferenceType.PipeReleaseDetection,
                    ReferenceType.PWD,
                    ReferenceType.ReleasePreventionBarrier,
                    ReferenceType.SecondaryContainment,
                    ReferenceType.State,
                    ReferenceType.StateRegulator,
                    ReferenceType.STISpillControl,
                    ReferenceType.TankConstructionMethod,
                    ReferenceType.TankCorrosionProtection,
                    ReferenceType.TankFabrication,
                    ReferenceType.TankFillPortLocation,
                    ReferenceType.TankLeakDetectionMethod,
                    ReferenceType.TankManufacturer,
                    ReferenceType.TankSpecification,
                }.Union(base.ReferenceTypes);
            }
        }
    }
}
