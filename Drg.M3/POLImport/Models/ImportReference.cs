using D = Drg.M3.Domain;
using System;
using System.Data.Common;

namespace Drg.M3.POLImport.Models
{
    public class ImportReference : BaseModel<ImportReference>
    {
        public int DropDownID { get; set; }
        public D.ReferenceType ReferenceType
        {
            get { return (D.ReferenceType)Enum.Parse(typeof(D.ReferenceType), this.DropDownCategory); }
        }
        public string DropDownCategory { get; set; }
        public string DropDownValue { get; set; }
        public string DropDownDescription { get; set; }

        public override string ToString()
        {
            return $"{DropDownID} {ReferenceType} {DropDownCategory} {DropDownValue} {DropDownDescription}";
        }
    }
}
