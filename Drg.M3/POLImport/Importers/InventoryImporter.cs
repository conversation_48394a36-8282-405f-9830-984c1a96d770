using Drg.M3.Bll;
using Drg.M3.Dal;
using Drg.M3.Domain;
using Drg.M3.Domain.POL;
using Drg.M3.POLImport.Models;
using System;
using System.Collections.Generic;
using System.Data.OleDb;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading.Tasks;
using Z.Collections.Extensions;
using D = Drg.M3.Domain;

namespace Drg.M3.POLImport.Importers
{
    public class InventoryImporter
    {
        public static InventoryImporter Instance { get; } = new InventoryImporter();

        private D.Reference GetReferenceValue(M3Session session,
                                              Dictionary<ReferenceType, List<Reference>> references,
                                              string value,
                                              Expression<Func<InventoryItem, D.Reference>> ex)
        {
            if (value == null)
            {
                return null;
            }

            var memberExpression = ex.Body as MemberExpression;
            var member = memberExpression.Member;

            var attribute = member.GetCustomAttribute<SupportsReferenceMergeAttribute>();
            var referenceType = attribute.ReferenceType; // (D.ReferenceType)Enum.Parse(typeof(D.ReferenceType), attribute.Category);

            var valueNormalized = value.Trim();

            var matches = references[referenceType]?.Where(r => r.Name == valueNormalized).ToList();

            if (matches == null)
            {
                throw new DataMisalignedException($"Reference type not found: {referenceType}");
            }
            else if (matches.Count > 1)
            {
                throw new DataMisalignedException($"Multiple references found: {referenceType},{valueNormalized},{String.Join("|", matches.Select(rr => rr.Name))}");
            }
            else if (references[referenceType].Any(r => r.Name == valueNormalized))
            {
                return references[referenceType].Single(r => r.Name == valueNormalized);
            }
            else if (matches.Count == 0)
            {
                var newReference = (new ReferenceBll(session)).Create(referenceType);
                newReference.Name = valueNormalized;
                session.Save(newReference);

                references[referenceType].Add(newReference);

                return newReference;
            }

            return matches.FirstOrDefault();
        }

        private InspectionCategory GetCategory(M3Session session, string value)
        {
            if (value.IsNullOrEmpty() || value == "N/A")
            {
                return null;
            }

            var normalizedValue = "Cat " + value;
            var match = session.Query<InspectionCategory>().FirstOrDefault(x => x.Name == normalizedValue);

            if (match == null)
            {
                match = new InspectionCategory
                {
                    Name = normalizedValue,
                };
                session.Save(match);
            }

            return match;
        }

        public async Task<Dictionary<Inventory, InventoryItem>> Import(OleDbConnection connection,
                                                                       M3Session session,
                                                                       Dictionary<Installation, D.Organization> orgs)
        {
            var inventoryEntries = await Inventory.Query("SELECT * FROM usys_Inventory", connection);

            var allReferences = session.Query<Reference>()
                                       .Where(r => r.Association.Id == 4)
                                       .ToList()
                                       .GroupBy(r => r.ReferenceType)
                                       .ToDictionary(r => r.Key, r => new List<Reference>(r));

            var assetIds = inventoryEntries.Select(ii => ii.Asset_ID).GroupBy(ii => ii);
            if (assetIds.Any(ai => ai.Count() > 1))
            {
                throw new DataMisalignedException("There are inventory items which share an assetId");
            }

            var inventoryItems = inventoryEntries.Select(i =>
            {
                var result = session.Query<InventoryItem>().Where(ii => ii.AssetID == i.Asset_ID).SingleOrDefault()
                             ?? new InventoryItem();

                result.AGPipePresent = i.AG_Pipe_Present;
                result.AGPipeConstruction = GetReferenceValue(session, allReferences, i.AG_Pipe_Construction, x => x.AGPipeConstruction);
                result.AGPipeMaterial = GetReferenceValue(session, allReferences, i.AG_Pipe_Material, x => x.AGPipeMaterial);
                result.AGPipePressurization = GetReferenceValue(session, allReferences, i.AG_Pipe_Pressurization, x => x.AGPipePressurization);
                result.AGPipeReleaseDetection = GetReferenceValue(session, allReferences, i.AG_Pipe_Release_Detection, x => x.AGPipeReleaseDetection);
                result.AGPipeCorrosionProtection = GetReferenceValue(session, allReferences, i.AG_Pipe_Corrosion_Protection, x => x.AGPipeCorrosionProtection);
                result.AssetID = i.Asset_ID;
                result.BuildingNumber = i.Building_Number;
                result.Capacity = i.Capacity;
                result.CRDM = GetReferenceValue(session, allReferences, i.CRDM, x => x.CRDM);
                result.Contents = GetReferenceValue(session, allReferences, i.Contents, x => x.Contents);
                result.DateClosed = i.Date_Closed;
                result.DateInstalled = i.Date_Installed;
                result.DischargeCause = i.DischargeCause;
                result.DischargeCauseOther = i.DischargeCauseOther;
                result.EmptyEquipmentMethod = GetReferenceValue(session, allReferences, i.Empty_Equipment_Method, x => x.EmptyEquipmentMethod);
                result.FacilityCode = i.Facility_Code;
                result.FacilityType = i.Facility_Type;
                result.FillEquipmentMethod = GetReferenceValue(session, allReferences, i.Fill_Equipment_Method, x => x.FillEquipmentMethod);
                result.HighLevelAlarmType = GetReferenceValue(session, allReferences, i.High_Level_Alarm_type, x => x.HighLevelAlarmType);
                result.InstallationAbbrev = i.InstallationAbbrev;
                result.InventoryType = GetReferenceValue(session, allReferences, i.Inventory_Type, x => x.InventoryType);
                result.InventorySubtype = GetReferenceValue(session, allReferences, i.Inventory_Subtype, x => x.InventorySubtype);
                result.LevelGaugeType = GetReferenceValue(session, allReferences, i.Level_Gauge_Type, x => x.LevelGaugeType);
                result.MUIC = i.MUIC;
                result.MuniServAtRisk = i.MuniServAtRisk;
                result.NatResAtRisk = i.NatResAtRisk;
                result.NFANum = i.NFA_Num;
                result.Notes = i.Notes;
                result.NumContainers = i.Num_Containers;
                result.OPElectronicCommunication = i.OP_Electronic_Communication;
                result.OPLevelGaugeVisualCommunication = i.OP_Level_Gauge_Visual_Communication;
                result.OPNone = i.OP_None;
                result.OPHighlevelAlarm = i.OP_High_level_Alarm;
                result.OPOPVFlowRestricter = i.OP_OPV_Flow_Restricter;
                result.Organization = orgs.Where(o => o.Key.InstallationID == i.InstallationID).Single().Value;
                result.ProbableDischarge = i.ProbableDischarge;
                result.ReleasePreventionBarrier = GetReferenceValue(session, allReferences, i.Release_Prevention_Barrier, x => x.ReleasePreventionBarrier);
                result.SecondaryContainment = GetReferenceValue(session, allReferences, i.Secondary_Containment, x => x.SecondaryContainment);
                result.SpillControlEquipment = i.SpillCtlEquip;
                result.Status = GetReferenceValue(session, allReferences, i.Status, x => x.Status);
                result.STICategory = GetCategory(session, i.STI_Cat);
                result.STISpillControl = GetReferenceValue(session, allReferences, i.STI_Spill_Control, x => x.STISpillControl);
                result.TankManufacturer = GetReferenceValue(session, allReferences, i.Tank_Manuf, x => x.TankManufacturer);
                result.TankConstructionMethod = GetReferenceValue(session, allReferences, i.Tank_Const_Method, x => x.TankConstructionMethod);
                result.TankFabrication = GetReferenceValue(session, allReferences, i.Tank_Fabrication, x => x.TankFabrication);
                result.TankSpecification = GetReferenceValue(session, allReferences, i.Tank_Specification, x => x.TankSpecification);
                result.TankCorrosionProtection = GetReferenceValue(session, allReferences, i.Tank_Corrosion_Protection, x => x.TankCorrosionProtection);
                result.TankFillPortLocation = GetReferenceValue(session, allReferences, i.Tank_Fillport_Location, x => x.TankFillPortLocation);
                result.TankLeakDetectionMethod = GetReferenceValue(session, allReferences, i.Tank_Leak_Detection_Method, x => x.TankLeakDetectionMethod);
                result.Tenant = i.Tenant;
                result.UGPipePresent = i.UG_Pipe_Present;
                result.UGPipeConstruction = GetReferenceValue(session, allReferences, i.UG_Pipe_Construction, x => x.UGPipeConstruction);
                result.UGPipeMaterial = GetReferenceValue(session, allReferences, i.UG_Pipe_Material, x => x.UGPipeMaterial);
                result.UGPipePressurization = GetReferenceValue(session, allReferences, i.UG_Pipe_Pressurization, x => x.UGPipePressurization);
                result.UGPipeReleaseDetection = GetReferenceValue(session, allReferences, i.UG_Pipe_Release_Detection, x => x.UGPipeReleaseDetection);
                result.UGPipeCathodicProtection = GetReferenceValue(session, allReferences, i.UG_Pipe_Cathodic_Protection, x => x.UGPipeCathodicProtection);
                result.Usage = GetReferenceValue(session, allReferences, i.Usage, x => x.Usage);
                result.WorstDischarge = i.WorstDischarge;
                result.WorstDischargeOther = i.WorstDischargeOther;

                return new
                {
                    Inventory = i,
                    InventoryItem = result,
                };
            }).ToDictionary(i => i.Inventory, i => i.InventoryItem);

            inventoryItems.Values.ForEach(ii => session.Save(ii));

            return inventoryItems;
        }
    }
}
