using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Drg.M3.Domain;
using Drg.M3.Dal;
using NHibernate;
using Drg.M3.Bll;
using System.Collections;
using Drg.M3.Modules;
using System.Reflection;
using Drg.M3.Dao;
using log4net;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.PeriodicService
{
    public class AlertRunner : AbstractPeriodicTask
    {
        public override TimeSpan Expiration
        {
            get { return TimeSpan.FromMinutes(5); }
        }

        public override bool RunInTransaction
        {
            get { return false; }
        }

        private static readonly ILog logger = LogManager.GetLogger("alerts");

        public override void Execute(IM3Session session)
        {
            Execute(session, null);
        }

        public void Execute(IM3Session session, bool? highFrequency)
        {
            logger.Debug("BEGIN AlertRunner.Execute");

            AlertBll bll = null;
            try
            {
                bll = new AlertBll(session);
            }
            catch (Exception ex)
            {
                logger.Error(ex.Message, ex);
                return;
            }
            logger.Debug("Created AlertBll");
            List<IAlertRule> rules = new List<IAlertRule>();
            if (highFrequency != false)
            {
                try
                {
                    // get all system periodic alert rules
                    foreach (Type module in Assembly.GetExecutingAssembly().GetTypes().Where(t => t.Namespace != null && t.Namespace.StartsWith("Drg.M3.Modules") && t.GetInterface(typeof(IModule).FullName) != null))
                    {
                        var ctor = module.GetConstructor(new Type[] { });
                        if (ctor != null)
                            rules.AddRange((ctor.Invoke(null) as IModule).AlertRules ?? new IAlertRule[] { });
                    }
                    logger.Debug("Got periodic alert rules");
                }
                catch (System.Reflection.ReflectionTypeLoadException ex)
                {
                    logger.Error(ex.Message, ex);
                    foreach (var lexp in ex.LoaderExceptions)
                    {
                        logger.Error(lexp.Message, lexp);
                    }
                }
            }

            // combine system rules with custom rules
            try
            {
                var cars = new CustomAlertRuleDao(session).GetActive(highFrequency);
                rules.AddRange(cars);
            }
            catch (Exception ex)
            {
                logger.Error(ex.Message, ex);
                return;
            }
            logger.Debug("Added custom alert rules");
            logger.Debug(rules.Count + " rules to run");

            // loop through all the alert rules (combined)
            foreach (var rule in rules)
            {
                try
                {
                    logger.Debug("BEGIN running rule " + rule.ToString());
                    if (rule is CustomAlertRule car && car.ShouldDeactivate())
                    {
                        car.IsInactive = true;
                    }
                    else if (rule.ShouldEvaluate())
                    {
                        logger.Debug("Evaluating rule " + rule.ToString());
                        //execute criteria
                        var records = rule.GetRecords(session)?.Where(o => rule.Evaluate(o)).ToList();

                        if (records != null)
                        {
                            if (rule.SyndicateAlert() && records.Count > 0)
                            {
                                logger.Debug("Sending alerts for rule " + rule.ToString());
                                bll.Send(records, rule);
                            }
                            else
                            {
                                foreach (var record in records)
                                {
                                    logger.Debug("Sending alert for rule " + rule.ToString() + " and " + record.GetType().FullName + "[" + record.Id + "]");
                                    bll.Send(record, rule);
                                }
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    //eat the exception. If one rule fails, don't let it prevent the rest from running.
                    logger.Error(e);
                }
                logger.Debug("END running rule " + rule.ToString());
            }
            logger.Debug("END AlertRunner.Execute");
        }
    }
}
