using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Helpers;
using Drg.M3.Dal;
using Drg.M3.Dao;
using Griddly.Mvc;
using Griddly.Mvc.Results;

namespace Drg.M3.Controls.Griddly
{
    public class GriddlyDaoResult<T> : QueryableResult<T>, ISearchableResult
    {
        IDaoResult<T> _result;

        public GriddlyDaoResult(IDaoResult<T> result)
            : base(null)
        {
            _result = result;
        }

        public override long GetCount()
        {
            return _result.ExecuteCount(M3SessionSingleton.Instance);
        }

        public override IList<T> GetPage(int pageNumber, int pageSize, SortField[] sortFields)
        {
            return _result.ExecutePage(M3SessionSingleton.Instance, pageNumber, pageSize, BuildSortClause(sortFields));
        }

        public override IEnumerable<T> GetAll(SortField[] sortFields)
        {
            return _result.Execute(M3SessionSingleton.Instance, BuildSortClause(sortFields));
        }

        protected List<string> BuildSortClause(SortField[] sortFields)
        {
            if (sortFields != null)
                return sortFields.Select(x => x.Field + " " + (x.Direction == SortDirection.Ascending ? "ASC" : "DESC")).ToList();
            else
                return null;
        }

        public override void PopulateSummaryValues(GriddlySettings<T> settings)
        {
            if (settings.Columns.Any(x => x.SummaryFunction != null))
                throw new NotImplementedException("Summary functions aren't supported.");
        }

        public void SetSearchForm(IList<Bll.Reports.SearchCriterion> criteria)
        {
            _result.SearchForm = criteria;
        }

        public void SetSearchGrouping(string grouping)
        {
            _result.SearchGrouping = grouping;
        }
    }
}
