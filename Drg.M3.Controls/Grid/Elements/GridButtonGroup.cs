using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.UI;
using Drg.M3.Dao;
using Drg.M3.Client;
using Drg.M3.Domain;
using Drg.Core;
using Drg.M3.Client.Configuration;
using Drg.M3.Bll.Reports;
using System.Reflection;
using System.Web;
using Drg.M3.Modules;
using GrapeCity.ActiveReports;
using System.Web.Mvc;
using Drg.M3.Security;

namespace Drg.M3.Controls.Grid
{
    public class GridButtonGroup : IGridElement
    {
        public void Render(Grid grid, HtmlTextWriter writer)
        {
            foreach (var b in grid.Buttons)
            {
                if (b is ActionButton)
                {
                    string action = (b as ActionButton).Action;
                    HttpVerbs verb = HttpVerbs.Get;
                    if (b is AjaxButton)
                    {
                        verb = HttpVerbs.Post;
                        action = (b as AjaxButton).InternalAction;
                    }
                    else if (b is PostButton)
                    {
                        verb = HttpVerbs.Post;
                        action = (b as PostButton).InternalAction;
                    }

                    if (action != null)
                    {
                        if (!M3Context.Current.HasUrlPermission(grid.ParseUrl(action), verb))
                            b.Visible = false;
                    }
                }
            }

            var buttons = grid.Buttons.Where(gb => gb.Visible).ToList();

            for (int i = 0; i < buttons.Count; i++)
            {
                GridButton button = buttons[i];

                button.Render(grid, writer);
            }

            // now render the filter and report buttons
            if (grid.CanFilter || grid.EnablePrint)
            {
                writer.AddAttribute("class", "sys pull-right");
                writer.RenderBeginTag("div");

                if (grid.CanFilter)
                    RenderFilterButton(grid, writer);

                if (grid.EnablePrint)
                    RenderPrintButton(grid, writer);

                writer.RenderEndTag(); // </div>
            }
        }

        private static void RenderPrintButton(Grid grid, HtmlTextWriter writer)
        {
            writer.AddAttribute("class", "btn-group");
            writer.RenderBeginTag("div");

            writer.AddAttribute("title", "Reports");
            writer.AddAttribute("data-toggle", "dropdown");
            writer.AddAttribute("class", "btn btn-default btn-sm PrintButton dropdown-toggle");
            writer.RenderBeginTag("a");
            writer.Write(GridHelper.RenderIcon("print"));
            writer.Write("<span class=\"caret\"></span>");
            writer.RenderEndTag(); //a

            writer.AddAttribute("class", "dropdown-menu pull-right");
            writer.RenderBeginTag("ul");

            bool hasReports = false;

            /* SYSTEM REPORTS */
            {
                writer.RenderBeginTag("li");
                writer.Write("<span>Reports</span>");
                writer.RenderBeginTag("ul");

#pragma warning disable
                if (!string.IsNullOrEmpty(grid.Reports))
                {
                    var reports = grid.Reports.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
#pragma warning enable
                    reports.Sort();

                    foreach (string report in reports)
                    {
                        var type = Assembly.Load("Drg.M3").GetType(report.Trim());

                        if (!AssociationAttribute.Validate(type))
                            continue; //skip reports for other associations

                        if (!ModuleAttribute.Validate(type))
                            continue;

                        string name = type.Name;

                        var attribute = type.GetCustomAttributes(typeof(ReportAttribute), false).FirstOrDefault() as ReportAttribute;
                        if (attribute != null)
                            name = attribute.Name;

                        writer.RenderBeginTag("li");
                        writer.AddAttribute("data-toggle", "post");

                        //if (M3Context.Current.Organization.Settings.EnableReportFiling || M3Context.Current.Organization.Settings.EnableReportEmailing)
                        //    writer.AddAttribute("data-saveable", "true");

                        writer.AddAttribute("href", grid.ResolveUrl("~/Reporting/GetReportTemplate/" + type.FullName + "?cacheKey=" + grid.CacheKey));
                        writer.RenderBeginTag("a");
                        writer.Write(name);
                        writer.RenderEndTag(); //a
                        writer.RenderEndTag(); //li

                        hasReports = true;
                    }
                }

                foreach (GridReport report in grid.GridReports.OrderBy(r => r.Title))
                {

                    if (report is ActiveReport)
                    {
                        var ar = report as ActiveReport;
                        if (!AssociationAttribute.Validate(Assembly.Load("Drg.M3").GetType(ar.TypeFullName)))
                            continue; //skip reports for other associations

                        if (!M3Context.Current.Permissions.HasPermission(ar.TypeFullName))
                            continue; //skip if no permissions
                    }

                    if (report.Visible)
                    {
                        writer.RenderBeginTag("li");
                        writer.AddAttribute("class", (report.FilterOnSelectColumn ? "attach-ids " : "") + (report.GetType().FullName == typeof(UrlReport).FullName ? "no-queue" : ""));
                        writer.AddAttribute("data-toggle", "post");

                        //if (M3Context.Current.Organization.Settings.EnableReportFiling || M3Context.Current.Organization.Settings.EnableReportEmailing)
                        //    writer.AddAttribute("data-saveable", "true");

                        writer.AddAttribute("href", grid.ResolveUrl(report.GetUrl(grid.CacheKey, grid)));
                        writer.RenderBeginTag("a");
                        writer.Write(report.Title);
                        writer.RenderEndTag(); //a
                        writer.RenderEndTag(); //li

                        hasReports = true;
                    }
                }

                if (!hasReports)
                {
                    writer.Write("<li><i>Not available</i></li>");
                }

                writer.RenderEndTag(); //ul
                writer.RenderEndTag(); //li
            }

            var reportRecordType = grid.RecordType;
            if (grid.ReportPath != null)
                foreach (string part in grid.ReportPath.Split("."))
                    reportRecordType = reportRecordType.GetProperty(part).PropertyType;

            /* CUSTOM REPORTS (ORDERED ALPHABETICALLY) */
            if (reportRecordType != null /*&& M3Context.Current.Permissions.HasPermission(FunctionKeys.Printing.CustomReports, AccessType.View)*/)
            {
                var wordTemplates = new MsWordTemplateDao(grid.Session).GetByClassName(reportRecordType.FullName, M3Context.Current.User)
                   .OrderBy(x => x.Name).ToList();

                if (wordTemplates.Count > 0)
                {
                    writer.RenderBeginTag("li");
                    writer.Write("<span>Merge Letters</span>");
                    writer.RenderBeginTag("ul");
                    
                    foreach (var t in wordTemplates)
                    {
                        writer.RenderBeginTag("li");
                        writer.AddAttribute("data-toggle", "post");
                        //if (M3Context.Current.Organization.Settings.EnableReportFiling || M3Context.Current.Organization.Settings.EnableReportEmailing)
                        //    writer.AddAttribute("data-saveable", "true");

                        writer.AddAttribute("href", grid.ResolveUrl("~/Reporting/GetMsWordTemplateReport/" + t.Id + "?cacheKey=" + grid.CacheKey));
                        writer.RenderBeginTag("a");
                        writer.Write(t.Name);
                        writer.RenderEndTag(); // </a>
                        writer.RenderEndTag();// </li>

                        hasReports = true;
                    }

                    writer.RenderEndTag(); //ul
                    writer.RenderEndTag(); //li
                }
            }


            var queryTemplates = new AdvancedSearchDao(grid.Session).GetQueryTemplatesForControl(grid.GridClassName, M3Context.Current.User, M3Context.Current.Organization);
            if (M3Context.Current.User.Organization.ParentOrganization == null || queryTemplates.Any())
            {
                writer.RenderBeginTag("li");
                writer.Write("<span>Queries</span>");
                writer.RenderBeginTag("ul");

                if (M3Context.Current.User.Organization.ParentOrganization == null) //Only top level admins
                {
                    writer.RenderBeginTag("li");

                    writer.AddAttribute("class", "manage");
                    writer.AddAttribute("href", grid.ResolveUrl("~/Reporting/Templates/ManageQueries?controlId=" + grid.GridClassName
                        + "&returnUrl=" + HttpContext.Current.Server.UrlEncode(HttpContext.Current.Request.Url.PathAndQuery)));
                    writer.RenderBeginTag("a");
                    writer.Write("Manage Query Templates");
                    writer.RenderEndTag();// </a>
                    writer.RenderEndTag();// </li>
                }

                foreach (var query in queryTemplates.OrderBy(x=>x.Name))
                {
                    writer.RenderBeginTag("li");
                    writer.AddAttribute("data-toggle", "post");

                    writer.AddAttribute("href", M3Context.Current.ResolveUrl("~/Reporting/Queries/Execute/" + query.Id));
                    writer.RenderBeginTag("a");
                    writer.Write(query.Name);
                    writer.RenderEndTag(); //a
                    writer.RenderEndTag(); //li
                }
                writer.RenderEndTag(); //ul
                writer.RenderEndTag(); //li
            }

            /* EXPORTS */
            //if (M3Context.Current.Permissions.HasPermission(FunctionKeys.Printing.Exports, AccessType.View))
            {
                writer.RenderBeginTag("li");
                writer.Write("<span>Exports</span>");
                writer.RenderBeginTag("ul");

                var exports = new SpreadsheetTemplateDao(grid.Session).GetByClassName(reportRecordType.FullName, M3Context.Current.User);
                foreach (var report in exports.OrderBy(q => q.Name))
                {
                    writer.RenderBeginTag("li");
                    writer.AddAttribute("data-toggle", "post");
                    writer.AddAttribute("href", grid.ResolveUrl("~/Reporting/GetReportTemplate/" + report.Id + "?cacheKey=" + grid.CacheKey + "&prop=" + grid.ReportPath));
                    writer.RenderBeginTag("a");
                    writer.Write(report.Name);
                    writer.RenderEndTag(); //a
                    writer.RenderEndTag(); //li
                }

                writer.RenderBeginTag("li");
                writer.AddAttribute("class", "default");
                writer.AddAttribute("data-toggle", "post");
                writer.AddAttribute("href", grid.ResolveUrl("~/Reporting/GetReportTemplate/?cacheKey=" + grid.CacheKey));
                writer.RenderBeginTag("a");
                writer.Write("Default export");
                writer.RenderEndTag(); //a
                writer.RenderEndTag(); //li

                //if (AdvancedSearchBll.GetDomainClass(grid.RecordType) != null)
                //{
                //    // manage exports link
                //    writer.RenderBeginTag("li");
                //    string cc = AdvancedSearchBll.GetDomainClass(grid.RecordType).GetClassCode();
                //    writer.AddAttribute("class", "manage");
                //    writer.AddAttribute("href", grid.ResolveUrl("~/Reporting/Templates/ManageQueries?cc=" + cc + "&controlId=" + grid.GridClassName + "&cacheKey=" + grid.CacheKey
                //        + "&returnUrl=" + HttpContext.Current.Server.UrlEncode(HttpContext.Current.Request.Url.PathAndQuery)));
                //    writer.RenderBeginTag("a");
                //    writer.Write("Manage Custom Exports");
                //    writer.RenderEndTag();// </a>
                //    writer.RenderEndTag();// </li>
                //}

                writer.RenderEndTag(); //ul
                writer.RenderEndTag(); //li
            }

            writer.RenderEndTag(); //ul

            writer.RenderEndTag(); //div.btn-group
        }

        //private static void RenderMessagingButton(Grid grid, HtmlTextWriter writer)
        //{
        //    writer.AddAttribute("class", "btn-group");
        //    writer.RenderBeginTag("div");

        //        writer.AddAttribute("title", "Mailings");
        //        writer.AddAttribute("data-toggle", "dropdown");
        //        writer.AddAttribute("class", "btn btn-default btn-sm MessageButton dropdown-toggle");
        //        writer.RenderBeginTag("a");
        //            writer.Write(GridHelper.RenderIcon("message"));
        //            writer.Write("<span class=\"caret\"></span>");
        //        writer.RenderEndTag(); //a

        //        writer.AddAttribute("class", "dropdown-menu pull-right");
        //        writer.RenderBeginTag("ul");
        //            writer.RenderBeginTag("li");
        //                writer.Write("<span>Create Mailing</span>");
        //                writer.RenderBeginTag("ul");
        //                    writer.AddAttribute("data-toggle", "post");
        //                    writer.AddAttribute("data-url", grid.ResolveUrl("~/Contacts/Mailing/Create?recordType=" + grid.RecordType.FullName + "&entityPath=" + grid.EntityPath));
        //                    //writer.AddAttribute("id", GetID(grid));
        //                    writer.RenderBeginTag("li");
        //                        //writer.AddAttribute("cacheKey", grid.CacheKey);
        //                        //writer.AddAttribute("class", "btn btn-default btn-sm");
        //                        writer.AddAttribute("onclick", "return " + grid.ID + ".PostAction({url:'" + grid.ParseUrl(grid.ResolveUrl("~/Contacts/Mailing/Create?recordType=" + grid.RecordType.FullName + "&entityPath=" + grid.EntityPath) + "'});"));
        //                        writer.AddAttribute("title", "");
        //                        writer.RenderBeginTag("a");
        //                            writer.Write("Selected Records");
        //                        writer.RenderEndTag();//a
        //                    writer.RenderEndTag();//li
        //                    writer.AddAttribute("data-toggle", "post");
        //                    writer.AddAttribute("data-url", grid.ResolveUrl("~/Contacts/Mailing/CreateFromCacheKey?gridCacheKey=" + grid.CacheKey + "&recordType=" + grid.RecordType.FullName + "&entityPath=" + grid.EntityPath));
        //                    writer.AddAttribute("data-noIds", "true");
        //                    writer.RenderBeginTag("li");
        //                        writer.AddAttribute("onclick", "return " + grid.ID + ".PostAction({url:'" + grid.ParseUrl(grid.ResolveUrl("~/Contacts/Mailing/CreateFromCacheKey?gridCacheKey=" + grid.CacheKey + "&recordType=" + grid.RecordType.FullName + "&entityPath=" + grid.EntityPath) + "', noIds: true});"));
        //                        writer.RenderBeginTag("a");
        //                            writer.Write("All Records");
        //                        writer.RenderEndTag();//a
        //                    writer.RenderEndTag();//li
        //                writer.RenderEndTag();//ul
        //            writer.RenderEndTag();//li
        //        writer.RenderEndTag();//ul
        //    writer.RenderEndTag();//div
            
        //    //new PostButton()
        //    //{
        //    //    Icon = "message",
        //    //    Action = grid.ResolveUrl("~/Contacts/Mailing/Create?recordType=" + grid.RecordType.FullName + "&entityPath=" + grid.EntityPath),
        //    //    Grid = grid,
        //    //}.Render(grid, writer);

        //    //writer.RenderEndTag();
        //}

        private static void RenderFilterButton(Grid grid, HtmlTextWriter writer)
        {
            writer.AddAttribute("class", "FilterButton btn-group");
            writer.RenderBeginTag("div");

            writer.AddAttribute("title", "Filter");
            writer.AddAttribute("class", "quick btn btn-default btn-sm");
            writer.RenderBeginTag("a");

            if (grid.FilterValues != null)
                writer.Write(GridHelper.RenderIcon("find_text"));
            else
                writer.Write(GridHelper.RenderIcon("find"));

            writer.RenderEndTag(); //a

            writer.AddAttribute("title", "Saved filters");
            writer.AddAttribute("data-toggle", "dropdown");
            writer.AddAttribute("class", "down btn btn-default btn-sm dropdown-toggle");
            writer.RenderBeginTag("a");
            writer.Write("<span class=\"caret\"></span>");
            writer.RenderEndTag(); //a

            writer.AddAttribute("class", "dropdown-menu pull-right");
            writer.RenderBeginTag("ul");

            var reportRecordType = grid.RecordType;
            if (grid.ReportPath != null)
                foreach (string part in grid.ReportPath.Split("."))
                    reportRecordType = reportRecordType.GetProperty(part).PropertyType;

            var queries = new AdvancedSearchDao(grid.Session).GetForGrid(grid.GridClassName, reportRecordType, M3Context.Current.User, M3Context.Current.Organization);

            if (queries.Count > 0)
            {
                if (queries.Any(q => q.AdvancedSearchType == AdvancedSearchType.GridFilter))
                {
                    writer.RenderBeginTag("li");
                    writer.Write("Saved Filters");
                    writer.RenderBeginTag("ul");

                    foreach (var query in queries.Where(q => q.AdvancedSearchType == AdvancedSearchType.GridFilter).OrderBy(q => q.Name))
                    {
                        writer.RenderBeginTag("li");
                        writer.AddAttribute("advancedSearchId", query.Id.ToString());
                        writer.RenderBeginTag("a");
                        writer.Write(query.Name);
                        writer.RenderEndTag(); //a
                        writer.RenderEndTag(); //li
                    }

                    writer.RenderEndTag(); //ul
                    writer.RenderEndTag(); //li
                }

                if (queries.Any(q => q.AdvancedSearchType == AdvancedSearchType.CustomQuery) && grid.DataSource.CanSetAdvancedFilter)
                {
                    writer.RenderBeginTag("li");
                    writer.Write("Custom Queries");
                    writer.RenderBeginTag("ul");

                    foreach (var query in queries.Where(q => q.AdvancedSearchType == AdvancedSearchType.CustomQuery).OrderBy(q => q.Name))
                    {
                        writer.RenderBeginTag("li");
                        writer.AddAttribute("advancedSearchId", query.Id.ToString());
                        writer.RenderBeginTag("a");
                        writer.Write(query.Name);
                        writer.RenderEndTag(); //a
                        writer.RenderEndTag(); //li
                    }

                    writer.RenderEndTag(); //ul
                    writer.RenderEndTag(); //li
                }
            }
            else
            {
                writer.RenderBeginTag("li");
                writer.Write("<i>No saved searches</i>");
                writer.RenderEndTag();
            }

            writer.RenderEndTag(); // ul.dropdown-menu

            writer.RenderEndTag(); //div.btn-group
        }
      
    }
}
