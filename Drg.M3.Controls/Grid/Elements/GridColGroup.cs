using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.UI;

namespace Drg.M3.Controls.Grid
{
    public class GridColGroup : IGridElement
    {
        public void Render(Grid grid, HtmlTextWriter writer)
        {
            writer.AddAttribute("span", grid.VisibleColumns.Count.ToString());
            writer.RenderBeginTag("colgroup");
            foreach (GridColumn col in grid.VisibleColumns)
                col.<PERSON><PERSON><PERSON>(grid, writer);
            writer.RenderEndTag(); //colgroup
        }
    }
}
