using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.UI;
using Drg.M3.Bll.Reports;
using Drg.M3.Domain;
using Drg.M3.Client;
using System.Collections.Specialized;

namespace Drg.M3.Controls.Grid
{
    public class GridFilters : IGridElement
    {
        public void Render(Grid grid, HtmlTextWriter writer)
        {
            bool hasSysQueryPermission = M3Context.Current.Organization.ParentOrganization == null && M3Context.Current.Permissions.HasPermission("Reporting.Queries.SystemScope", AccessType.Edit);
            bool hasOrgQueryPermission = M3Context.Current.Permissions.HasPermission("Reporting.Queries.OrganizationScope", AccessType.Edit);

            bool nonGridSearch = grid.AdvancedSearch != null && grid.AdvancedSearch.AdvancedSearchType != AdvancedSearchType.GridFilter;

            string hidden = "";
            if (!grid.AlwaysShowFilter && !nonGridSearch && grid.AdvancedFilterValues == null && NameValueCollectionsAreEqual(grid.FilterValues, grid.DefaultFilterValues))
                hidden = " hidden";

            writer.AddAttribute("class", "filters" + hidden);
            writer.RenderBeginTag("tr");
            writer.AddAttribute("colspan", grid.VisibleColumns.Count.ToString());
            writer.RenderBeginTag("td");

            if (nonGridSearch)
            {
                var criteria = new AdvancedSearchBll(grid.Session).ParseCriteria(grid.AdvancedSearch);
                if (criteria.Any(c => c.Input))
                {
                    writer.AddAttribute("role", "form");
                    writer.AddAttribute("class", "filterForm");
                    writer.RenderBeginTag("div");

                    var vals = grid.FilterValues;
                    var i = 0;
                    foreach (var c in criteria.Where(c => c.Input))
                    {
                        writer.RenderBeginTag("div");
                        writer.Write("<label class=\"wide\">" + c.Caption + " (" + c.Operator.Name + ")</label>");

                        if (vals != null && vals.GetValues("q") != null && i < vals.GetValues("q").Length)
                            writer.AddAttribute("value", vals.GetValues("q")[i++]);
                        
                        writer.AddAttribute("type", "text");
                        writer.AddAttribute("name", "q");
                        writer.AddAttribute("value", c.Value);
                        writer.RenderBeginTag("input");

                        writer.RenderEndTag(); // input 
                        writer.RenderEndTag(); // div
                    }

                    writer.Write("<div class=\"errors\"></div>");

                    writer.AddAttribute("class", "buttons");
                    writer.RenderBeginTag("div");

                    writer.Write("<input class=\"btn btn-primary btn-sm\" type=\"submit\" id=\"" + grid.ID + "_search\" value=\"Search\" /> ");
                    writer.Write("<a class=\"btn btn-default btn-sm " + grid.ID + "_searchreset\" href=\"javascript:;\">Reset</a> ");

                    writer.RenderEndTag(); //div
                    writer.RenderEndTag(); // div
                }
                else
                {
                    //an un-editable advanced search is loaded
                    writer.Write("<div class=\"filterForm\"><div>Custom Query: " + grid.AdvancedSearch.Name
                        + " (<a href=\"" + grid.ResolveUrl("~/Reporting/Queries/Edit/" + grid.AdvancedSearch.Id)
                        + "\">Edit</a>)</div><br /><div class=\"buttons\"><input type=\"reset\" class=\"" + grid.ID
                        + "_searchreset\" href=\"javascript:;\" value=\"Reset\"> </div></div>"); // <a href=\"javascript:;\" class=\"cancel\">Cancel</a>
                }
            }
            else
            {
                if (grid.EnableFilterSaving && (grid.AdvancedFilterValues != null || !NameValueCollectionsAreEqual(grid.FilterValues, grid.DefaultFilterValues)))
                {
                    writer.AddAttribute("class", "saveLinks");
                    writer.RenderBeginTag("div");

                    if (grid.AdvancedSearch == null)
                        writer.Write("Unsaved search: ");
                    else
                        writer.Write("Saved search: " + grid.AdvancedSearch.Name + "<br/>");

                    if (grid.AdvancedSearch != null && (grid.AdvancedSearch.Scope == Scope.User || (grid.AdvancedSearch.Scope == Scope.Organization && hasOrgQueryPermission) || (grid.AdvancedSearch.Scope == Scope.System && hasSysQueryPermission)))
                        writer.Write(@"<a class=""save"">Save</a> | ");

                    writer.Write(@"<a class=""saveas"">Save As</a>");

                    if (grid.AdvancedSearch != null)
                        writer.Write(@" | <a class=""delete"">Delete</a>");

                    writer.RenderEndTag(); //div


                    writer.AddAttribute("class", "saveForm novalidate form");
                    writer.RenderBeginTag("form");
                    
                    writer.Write("<div><label for=\"searchName\">Name</label><input type=\"text\" id=\"searchName\" name=\"searchName\" /></div>");
                    
                    writer.Write("<div><label for=\"searchScope\">Scope</label><select id=\"searchScope\" name=\"scope\">");
                    writer.Write("<option value=\"" + (int)Scope.User + "\">My Queries</option>");
                    if (hasOrgQueryPermission)
                        writer.Write("<option value=\"" + (int)Scope.Organization + "\">" + M3Context.Current.Organization.Name + " Queries</option>");
                    if (hasSysQueryPermission)
                        writer.Write("<option value=\"" + (int)Scope.System + "\">System Queries</option>");
                    writer.Write("</select></div>");

                    if (grid.FilterBindables != null)
                    {
                        writer.Write("<div><label for=\"searchBind\">Bind to menu</label><select id=\"searchBind\" name=\"searchBind\">");
                        writer.Write("<option></option>");
                        foreach (var menu in grid.FilterBindables)
                            writer.Write("<option value=\"" + menu.Id + "\">" + menu.Name + "</option>");
                        writer.Write("</select></div>");
                    }

                    writer.Write("<div class=\"buttons\">");
                    writer.Write("<input class=\"btn btn-primary btn-sm\" type=\"submit\" value=\"Save\" /> ");
                    writer.Write("<a href=\"javascript:;\" class=\"cancel btn btn-default btn-sm\">Cancel</a>");
                    writer.Write("</div>");
                    writer.RenderEndTag(); //form
                }

                writer.AddAttribute("class", "filterForm");
                writer.RenderBeginTag("div");

                if (grid.FilterForm != null)
                {
                    UserControl uc = new UserControl();
                    grid.FilterForm.InstantiateIn(uc);
                    uc.RenderControl(writer);

                    if (grid.DataSource.CanSetAdvancedFilter && grid.EnableAdvancedSearch)
                        new GridAdvancedFilter().Render(grid, writer);
                }
                else 
                {
                    new GridDefaultFilter().Render(grid, writer);
                }

                writer.Write("<div class=\"errors\"></div>");

                // render the cancel and print buttons
                writer.AddAttribute("class", "buttons");
                writer.RenderBeginTag("div");

                writer.Write("<input class=\"btn btn-primary btn-sm\" type=\"submit\" id=\"" + grid.ID + "_search\" value=\"Search\" /> ");
                writer.Write("<a class=\"btn btn-default btn-sm " + grid.ID + "_searchreset\" href=\"javascript:;\">Reset</a> ");

                writer.RenderEndTag(); //div
                writer.RenderEndTag(); //div
            }
           
            writer.RenderEndTag(); //td
            writer.RenderEndTag(); //tr
        }

        bool NameValueCollectionsAreEqual(NameValueCollection nvc1, NameValueCollection nvc2)
        {
            if (nvc1 == null || nvc2 == null)
                return nvc1 == nvc2;
            return nvc1.AllKeys.OrderBy(key => key).SequenceEqual(nvc2.AllKeys.OrderBy(key => key)) && nvc1.AllKeys.All(key => nvc1[key] == nvc2[key]);
        }
    }
}
