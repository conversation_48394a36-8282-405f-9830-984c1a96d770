using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web.Handlers;
using System.Web;
using System.Web.UI;
using Drg.M3.Domain;
using System.IO;
using ProjectBase.Cache;
using System.Text.RegularExpressions;
using Drg.Core.Json;
using System.Collections.Specialized;
using Drg.M3.Dao;
using Drg.M3.Dal;
using Drg.Core;
using Drg.M3.Client;
using NHibernate.Criterion;
using Drg.M3.Bll.Reports;
using NHibernate;
using Drg.M3.Client.Configuration;

namespace Drg.M3.Controls.Grid
{
	class GridService : IHttpHandler
	{
		#region IHttpHandler Members

		public bool IsReusable
		{
			get { return false; }
		}
		private string cacheKey { get; set; }
		private HttpContext Context { get; set; }

		public void ProcessRequest(HttpContext context)
        {
            try
            {
                cacheKey = context.Request["cacheKey"];
                Context = context;

                string command = context.Request["command"];

                if (AppCache.Get(cacheKey) != null)
                {
                    HtmlTextWriter tw = new HtmlTextWriter(context.Response.Output);
                    Grid grid = AppCache.Get(cacheKey) as Grid;

                    HandleCommand(grid, command);

                    switch (command)
                    {
                        case "get_included":
                        //{
                        //    var included = grid.GetReportDataSource(null).ToList().ConvertAll(o =>
                        //        Convert.ToString(DataBinder.Eval(o, grid.KeyDataField))).ToArray();
                        //    context.Response.Write(string.Join(",", included));

                        //    context.Response.ContentType = "text/plain";
                        //} break;
                        //case "get_settings":
                        //    grid.RenderSettings(tw);
                        //    break;
                        //case "get_printing":
                        //    grid.RenderPrinting(tw);
                        //    break;
                        case "showall":
                        case "page":
                        //case "filter_json":
                        case "exclude":
                        case "include":
                        case "refresh_data":
                        case "sort":
                        case "sort_add":
                            //case "clearFilter":
                            new GridRows().Render(grid, tw);
                            break;
                        default:
                            new GridTableContents().Render(grid, tw);
                            break;
                    }

                    JsonObject metaData = new JsonObject();
                    metaData.Add("footerSummary", new GridFoot().GetSummary(grid));
                    metaData.Add("page", grid.PageNumber);
                    metaData.Add("pageCount", grid.GetPageCount());

                    if (grid.AdvancedSearch == null)
                        metaData.Add("advancedSearchId", null);
                    else
                        metaData.Add("advancedSearchId", grid.AdvancedSearch.Id);

                    context.Response.AddHeader("GridMetaData", metaData.Render());
                }
                else
                {
                    context.Response.Write("grid_not_available");
                }

                context.Response.Cache.SetNoStore();
            }
            catch (Exception ex)
            {
                if (!M3SettingsSection.Current.DebugMode)
                {
                    context.Response.Clear();
                    context.Response.Write("grid_error");
                    Elmah.ErrorSignal.FromCurrentContext().Raise(ex);
                }
                else
                    throw;
            }
		}
        #endregion

        private void HandleCommand(Grid grid, string command)
		{
			switch (command)
			{
                case "init":
                    Init(grid); break;
                case "showall":
                    grid.ShowingAllRecords = !grid.ShowingAllRecords; break;
                //case "save_settings":
                //    grid.SaveSettings(Context.Request.Form); break;
				case "sort":
                    Sort(grid, false); break;
                case "sort_add":
                    Sort(grid, true); break;
				case "page":
					grid.PageNumber = int.Parse(Context.Request["pagenumber"]); break;
                case "filter":
                    Filter(grid); break;
				case "refresh":
				case "refresh_data":
                    grid.RefreshData(); break;
                case "advancedSearch":
                    AdvancedSearch(grid); break;
                case "advancedSearch_save":
                    AdvancedSearch_Save(grid); break;
                case "advancedSearch_delete":
                    AdvancedSearch_Delete(grid); break;
                case "advancedSearch_reset":
                    AdvancedSearch_Reset(grid); break;
			}
		}

        private void AdvancedSearch_Reset(Grid grid)
        {
            grid.AdvancedSearch = null;
            grid.FilterValues = grid.DefaultFilterValues;
            grid.AdvancedFilterValues = null;
            grid.ClearSort();
            grid.RefreshData();
        }

        private void AdvancedSearch_Delete(Grid grid)
        {
            var search = grid.Session.Get<AdvancedSearch>(int.Parse(Context.Request["id"]));
            grid.Session.Delete(search);
            grid.Session.Flush();

            grid.AdvancedSearch = null;
        }

        private void AdvancedSearch_Save(Grid grid)
        {
            var search = grid.Session.Get<AdvancedSearch>(Parse.Integer(Context.Request["id"]));

            if (search == null)
            {
                var scope = (Scope)Parse.Integer(Context.Request["scope"]);

                search = new AdvancedSearch();
                search.Name = Context.Request["searchName"];
                search.MenuKey = Context.Request["searchBind"];
                search.Url = Context.Request.UrlReferrer.PathAndQuery;
                if (scope == Scope.User)
                    search.User = M3Context.Current.User;
                if (scope == Scope.Organization)
                    search.Organization = M3Context.Current.Organization;
                search.AdvancedSearchType = AdvancedSearchType.GridFilter;
                search.Class = grid.GridClassName;
            }

            if (grid.AdvancedFilterValues != null)
                search.Criteria = grid.AdvancedFilterValues;
            else
                search.Criteria = grid.FilterValues.ToQueryString();

            if (grid.DataSource is IDaoGridDataSource)
            {
                var daoDs = grid.DataSource as IDaoGridDataSource;
                search.ParsedCriteriaData = daoDs.DaoResult.GetSerializedCriteria();
                search.Class = grid.RecordType.FullName;
            }

            grid.Session.Save(search);
            grid.Session.Flush();

            if (!search.Url.Contains(grid.ID + "_asid="))
            {
                search.Url += (search.Url.Contains("?") ? "&" : "?") + grid.ID + "_asid=" + search.Id;
                grid.Session.Flush();
            }
        }

        private void AdvancedSearch(Grid grid)
        {
            var search = grid.Session.Get<AdvancedSearch>(int.Parse(Context.Request["id"]));
            grid.AdvancedSearch = search;

            grid.RefreshData();
        }

        private void Filter(Grid grid)
        {
            grid.FilterValues = Context.Request.Form.RemoveEmpty();
            grid.FilterValues.Remove("cacheKey");
            grid.FilterValues.Remove("command");

            grid.OnNeedDataSource();

            if (grid.DataSource != null && grid.DataSource.CanSetAdvancedFilter)
            {
                if (grid.AdvancedSearch != null && grid.AdvancedSearch.AdvancedSearchType != AdvancedSearchType.GridFilter)
                {
                    grid.AdvancedFilterValues = grid.AdvancedSearch.Criteria;
                }
                else
                {
                    if (grid.FilterValues["ad_field"] != null)
                        grid.AdvancedFilterValues = Context.Request.Form.ToString();
                    else
                        grid.AdvancedFilterValues = null;

                    foreach (var key in grid.FilterValues.AllKeys.Where(k => k.StartsWith("ad_")))
                        grid.FilterValues.Remove(key);

                    foreach (var key in grid.FilterValues.AllKeys)
                        if (grid.FilterValues[key] == "null")
                            grid.FilterValues.Remove(key);
                }
            }

            if (grid.FilterValues.Count == 0)
                grid.FilterValues = null;

            grid.RefreshData();
        }

        private void Sort(Grid grid, bool add)
        {
            string columnName = Context.Request["columnName"];
            bool desc = bool.Parse(Context.Request["desc"]);

            grid.AddSort(columnName, desc, !add);
        }

        private void Init(Grid grid)
        {
            grid.FilterValues = Context.Request.Form.RemoveEmpty();
            grid.FilterValues.Remove("cacheKey");
            grid.FilterValues.Remove("command");
            grid.FilterValues.Remove("pageNumber");
            grid.FilterValues.Remove("sortField");
            grid.FilterValues.Remove("sortDesc");

            if (grid.FilterValues.Count == 0) grid.FilterValues = null;

            if (Context.Request["sortField"] != null)
            {
                string columnName = Context.Request["columnName"];
                bool desc = bool.Parse(Context.Request["desc"]);

                grid.AddSort(columnName, desc);
            }
            grid.RefreshData();
            grid.PageNumber = Parse.Integer(Context.Request["pagenumber"]);
        }


    }
}
