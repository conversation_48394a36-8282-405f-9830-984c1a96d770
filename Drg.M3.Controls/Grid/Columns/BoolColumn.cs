using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.ComponentModel;
using Drg.Core;
using System.Web.Mvc;
using System.Collections;
using Drg.M3.Domain;
using Drg.M3.Client;
using Drg.M3.Bll.Reports;

namespace Drg.M3.Controls.Grid
{
    [PersistenceMode(PersistenceMode.InnerProperty)]
    [ToolboxData("<{0}:BoolColumn runat=server></{0}:BoolColumn>")]
    [Obsolete("Deprecated. Use BoundColumn with type=\"bool\"")]
    public class BoolColumn : BoundColumn
    {
        public BoolColumn()
        {
            TrueIcon = "check";
            FalseIcon = null;
            NullIcon = null;
        }

        public string TrueIcon { get; set; }
        public string FalseIcon { get; set; }
        public string NullIcon { get; set; }
        public override string CssClass { get; set; }

        public override void Render(Grid grid, HtmlTextWriter writer, object row)
        {
            var obj = GridHelper.EvaluateField(row, DataField, Parse.NullableBoolean(DefaultValue));
            bool? property = obj as bool?;

            var propType = AdvancedSearchBll.EvalType(row.GetType(), DataField);
            if (propType == typeof(DateTime?) || propType == typeof(DateTime) || propType == typeof(NiftyDate))
            {
                property = obj != null;
            }
            
            string url = property == null ? NullIcon : property.Value ? TrueIcon : FalseIcon;

            if (string.IsNullOrEmpty(url))
                writer.Write("&nbsp;");
            else 
                writer.Write(GridHelper.RenderIcon(url, 16, CssClass));
        }
    }
}
