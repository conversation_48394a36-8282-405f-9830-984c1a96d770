using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Drg.M3.Dao;
using Drg.M3.Domain;
using System.Collections.Specialized;
using System.Collections;
using Drg.M3.Bll.Reports;
using Drg.M3.Dal;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Controls.Grid
{
    public interface IDaoGridDataSource : IGridDataSource
    {
        IDaoResult DaoResult { get; }
    }
    public static class DaoGridDataSource
    {
        public static IDaoGridDataSource Create(IDaoResult records)
        {
            return Create(records, records.GetType().GetGenericArguments()[0]);
        }
        public static IDaoGridDataSource Create(IDaoResult records, Type type)
        {
            var genericType = typeof(DaoGridDataSource<>).MakeGenericType(new[] { type });
            var iEnumGenericType = typeof(DaoResult<>).MakeGenericType(new[] { type });
            var constructor = genericType.GetConstructor(new[] { iEnumGenericType });
            return constructor.Invoke(new[] { records }) as IDaoGridDataSource;
        }
    }
    public class DaoGridDataSource<T> : IDaoGridDataSource, IGridDataSource<T>
    {
        public virtual void Build(IM3Session session, NameValueCollection parameters) { }
        public IDaoResult<T> DaoResult { get; set; }
        protected DaoGridDataSource() { }
        public DaoGridDataSource(IDaoResult<T> daoResult)
        {
            this.DaoResult = daoResult;
        }
        public IEnumerable<T> GetPage(IM3Session session, IList<string> sorts, int pageNumber, int pageSize)
        {
            bool newsort = false;
            if ((sorts != null && DaoResult.SortFields == null) || (sorts == null && DaoResult.SortFields != null))
                newsort = true;
            else if (sorts != null && DaoResult.SortFields != null)
            {
                for (int i = 0; i < sorts.Count; i++)
                {
                    if (DaoResult.SortFields.Count <= i || DaoResult.SortFields[i] != sorts[i]) // order matters!
                        newsort = true;
                }
            }

            if (pageNumber != DaoResult.PageNumber || pageSize != DaoResult.PageSize || newsort)
            {
                if (pageSize > 0)
                    DaoResult.ExecutePage(session, pageNumber, pageSize, sorts);
                else
                    DaoResult.Execute(session, sorts);
            }
            return DaoResult as IExecutedDaoResult<T>;
        }
        IEnumerable IGridDataSource.GetPage(IM3Session session, IList<string> sorts, int pageNumber, int pageSize)
        {
            return GetPage(session, sorts, pageNumber, pageSize);
        }

        Type type { get; set; }

        public bool CanSetAdvancedFilter { get { return typeof(PersistentObject).IsAssignableFrom(typeof(T)); } }
        public void SetAdvancedFilter(IList<SearchCriterion> formValues, string grouping)
        {
            this.DaoResult.SearchGrouping = grouping;
            this.DaoResult.SearchForm = formValues;
        }

        public bool CanSetFilter { get { return false; } }
        public void SetFilter(Grid grid, NameValueCollection formValues) { }

        public long GetCount(IM3Session session)
        {
            return DaoResult.GetRowCount(session);
        }

        public Type GetGenericType()
        {
            return DaoResult.GetGenericType();
        }

        public IList<T> Execute(IM3Session session)
        {
            return DaoResult.Execute(session);
        }



        IDaoResult IDaoGridDataSource.DaoResult
        {
            get
            {
                return DaoResult;
            }
        }
    }
}
