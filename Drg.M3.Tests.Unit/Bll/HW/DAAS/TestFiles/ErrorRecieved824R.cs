using Drg.M3.Bll.HW.DAAS;
using Drg.M3.Bll.HW.DAAS.FileResolvers;

namespace Drg.M3.Tests.Unit.Bll.HW.DAAS.TestFiles
{
    public static class ErrorRecieved824R
    {
        public static DAASFile File
        {
            get
            {
                var fileResolver = new FileResolver();

                return new DAASFile
                {
                    Filename = "Error824R",
                    Data = Data,
                    ParsedFile = fileResolver.ResolveFile(Data),
                };
            }
        }

        public const string Data = @"<?xml version=""1.0"" encoding=""UTF-8"" standalone=""yes""?>
<file>
    <T_Application_Advice_824R Standard=""X12"">
        <S_Transaction_Set_Header>
            <E_Transaction_Set_Identifier_Code>824</E_Transaction_Set_Identifier_Code>
            <E_Transaction_Set_Control_Number>71651</E_Transaction_Set_Control_Number>
        </S_Transaction_Set_Header>
        <S_Beginning_Segment>
            <E_Transaction_Set_Purpose_Code>00</E_Transaction_Set_Purpose_Code>
            <E_Reference_Identification>N684704116D103</E_Reference_Identification>
            <E_Date>21240708</E_Date>
            <E_Time>1244</E_Time>
            <E_Transaction_Type_Code>ZT</E_Transaction_Type_Code>
        </S_Beginning_Segment>
        <L_Name>
            <S_Name>
                <E_Entity_Identifier_Code>FR</E_Entity_Identifier_Code>
                <E_Identification_Code_Qualifier>M4</E_Identification_Code_Qualifier>
                <E_Identification_Code>S0A</E_Identification_Code>
            </S_Name>
        </L_Name>
        <L_Name>
            <S_Name>
                <E_Entity_Identifier_Code>TO</E_Entity_Identifier_Code>
                <E_Identification_Code_Qualifier>10</E_Identification_Code_Qualifier>
                <E_Identification_Code>N68470</E_Identification_Code>
            </S_Name>
        </L_Name>
        <L_Original_Transaction_Identification>
            <S_Original_Transaction_Identification>
                <E_Application_Acknowledgment_Code>TR</E_Application_Acknowledgment_Code>
                <E_Reference_Identification_Qualifier>TN</E_Reference_Identification_Qualifier>
                <E_Reference_Identification>N684704116D103</E_Reference_Identification>
            </S_Original_Transaction_Identification>
            <S_Reference_Identification>
                <E_Reference_Identification_Qualifier>SJ</E_Reference_Identification_Qualifier>
                <E_Reference_Identification>856SR</E_Reference_Identification>
            </S_Reference_Identification>
            <L_Technical_Error_Description>
                <S_Technical_Error_Description>
                    <E_Application_Error_Condition_Code>OTH</E_Application_Error_Condition_Code>
                    <E_Free_Form_Message>Rejected. Document number invalid.</E_Free_Form_Message>
                </S_Technical_Error_Description>
            </L_Technical_Error_Description>
            <L_Code_Source_Information>
                <S_Code_Source_Information>
                    <E_Agency_Qualifier_Code>DF</E_Agency_Qualifier_Code>
                </S_Code_Source_Information>
                <L_Industry_Code>
                    <S_Industry_Code>
                        <E_Code_List_Qualifier_Code>ET</E_Code_List_Qualifier_Code>
                        <E_Industry_Code>AF</E_Industry_Code>
                    </S_Industry_Code>
                </L_Industry_Code>
            </L_Code_Source_Information>
        </L_Original_Transaction_Identification>
        <S_Transaction_Set_Trailer>
            <E_Number_of_Included_Segments>10</E_Number_of_Included_Segments>
            <E_Transaction_Set_Control_Number>71651</E_Transaction_Set_Control_Number>
        </S_Transaction_Set_Trailer>
    </T_Application_Advice_824R>
</file>";
    }
}
