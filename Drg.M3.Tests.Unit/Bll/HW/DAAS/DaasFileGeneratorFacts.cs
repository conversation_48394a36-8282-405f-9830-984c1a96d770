using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Xunit;

namespace Drg.M3.Tests.Unit.Bll.HW.DAAS
{
    public class DaasFileGeneratorFacts
    {
        [Fact]
        public void CrcCollisionTest()
        {
            var crc = new CRC32();

            ISet<string> vals = new HashSet<string>();

            var orgIds = Enumerable.Range(1, 200);

            int recordId = 1;
            while (recordId < 50000000)
            {
                foreach (int orgId in orgIds)
                {
                    var input = Encoding.ASCII.GetBytes($"{orgId}-{recordId}");
                    var output = crc.ComputeHash(input);
                    var str = BitConverter.ToString(output).Replace("-", "");

                    Assert.Equal(8, str.Length);

                    if (vals.Contains(str))
                    {
                        Assert.True(vals.Count > 2000000);
                        return;
                    }

                    vals.Add(str);

                    recordId++;
                }
            }
        }
    }
}
