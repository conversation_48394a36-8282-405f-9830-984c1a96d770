using Drg.M3.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Drg.M3.Client.Models.QA.Finding
{
    public class RepeatSearchModel
    {
        public string QuickSearch { get; set; }
        public int[] Media { get; set; }
        public NiftyDate ObservedStart { get; set; }
        public NiftyDate ObservedEnd { get; set; }
        public bool? IsExternal { get; set; }
        public string CitationCode { get; set; }
        public int? FindingType { get; set; }
        public int? Category { get; set; }
        public int? PreliminaryRootCauseCode { get; set; }

        public IEnumerable<Reference> MediaList { get; set; }
        public IEnumerable<Reference> FindingTypeList { get; set; }
        public IEnumerable<Reference> FindingCategoryList { get; set; }
        public IEnumerable<Reference> RootCauseCodeList { get; set; }
    }
}