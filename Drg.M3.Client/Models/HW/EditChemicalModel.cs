using AutoMapper;
using Drg.M3.Domain;
using Drg.M3.Domain.HW;
using ExpressiveAnnotations.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace Drg.M3.Client.Models.HW
{
    public class EditChemicalModel : IMapsTo<Chemical>, IHasPersistentObject
    {
        public Chemical Record { get; set; }

        public EditChemicalModel()
        {
        }

        public int? Id { get; set; }
        [IgnoreMap]
        public ChemicalType Type { get; set; }

        [Required]
        public string Name { get; set; }
        [Display(Name="Sort Name")]
        public string SortName { get; set; }
        [Display(Name = "RCRA Code")]
        [RequiredIf("Type == Drg.M3.Domain.HW.ChemicalType.State || Type == Drg.M3.Domain.HW.ChemicalType.EPA", ErrorMessage ="The {0} field is required.")]
        public string R<PERSON>raCode { get; set; }
        [Display(Name = "CAS Code")]
        public string CasCode { get; set; }
        [Display(Name = "Section 313 Code")]
        public string Section313 { get; set; }
        [Display(Name = "Regulatory Level")]
        public decimal? RegulatoryLevel { get; set; }
        public string State { get; set; }
        [RegularExpression(@"^\d{3}(, ?\d{3})*$", ErrorMessage = "Reactivity Group Number must be one or more comma-separated 3-digit numbers.")]
        [Display(Name = "Reactivity Group Number")]
        public string RGN { get; set; }

        PersistentObject IHasPersistentObject.Object
        {
            get { return Record; }
        }
        
    }
}