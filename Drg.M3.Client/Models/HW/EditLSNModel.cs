using Drg.M3.Domain;
using Drg.M3.Domain.HW;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace Drg.M3.Client.Models.HW
{
    public class EditLSNModel : IMapsTo<LSN>, IHasPersistentObject
    {
        public LSN Record { get; set; }

        public EditLSNModel()
        {
        }

        public int? Id { get; set; }

        [Required]
        [Display(Name = "Local Stock Number")]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        [Display(Name = "DEMIL Code")]
        public int DemilCodeId { get; set; }

        [Required]
        [Display(Name = "Federal Supply Class")]
        public int FederalSupplyClassId { get; set; }

        PersistentObject IHasPersistentObject.Object
        {
            get { return Record; }
        }
    }
}