using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Drg.M3.Domain;
using Drg.M3.Domain.HW;
using Drg.M3.Client.Localization;
using System.ComponentModel.DataAnnotations;

namespace Drg.M3.Client.Models.HW
{
    [Localizable]
    public class ManifestReturnedModel
    {
        public int[] Ids { get; set; } = new int[] { };

        public bool FromProfile { get; set; }
        public bool IsAjax { get; set; }

        [Display(Name = "Date")]
        public DateTime? Date { get; set; }
    }
}