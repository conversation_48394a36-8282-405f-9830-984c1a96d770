using Drg.M3.Domain.HW;
using Drg.M3.Client.Localization;
using System.ComponentModel.DataAnnotations;

namespace Drg.M3.Client.Models.HW
{
    [Localizable]
    public class CreateCwrModel
    {
        [Display(Name = "Number of empty CWR's to create")]
        [Range(1, 100)]
        public int Quantity { get; set; } = 1;

        [Display(Name = "Waste Type")]
        public WasteType WasteType { get; set; }

        [Display(Name = "Waste Profile")]
        public int? WasteProfileId { get; set; }
    }
}