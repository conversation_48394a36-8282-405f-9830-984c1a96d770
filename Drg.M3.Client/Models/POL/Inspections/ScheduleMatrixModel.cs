using Drg.M3.Domain;
using Drg.M3.Domain.POL;
using System.Collections.Generic;

namespace Drg.M3.Client.Models.POL.Inspections
{
    public class ScheduleRowCriteria
    {
        public int CapacityRangeStart { get; set; }
        public int CapacityRangeEnd { get; set; }
        public ICollection<Reference> InventoryTypes { get; set; }
        public ICollection<Reference> TankFabrication { get; set; }
    }

    public class ScheduleMatrixModel
    {
        public List<InspectionCategory> ColumnHeaders { get; set; } = new List<InspectionCategory>();

        public List<List<ScheduleCriteria>> RowHeaders { get; set; } = new List<List<ScheduleCriteria>>();

        Dictionary<int, Dictionary<int, List<InspectionType>>> Matrix { get; set; } = new Dictionary<int, Dictionary<int, List<InspectionType>>>();
    }
}