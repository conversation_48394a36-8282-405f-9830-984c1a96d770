using System;
using System.Web.Mvc;
using Drg.M3.Dal;
using Drg.M3.Dal.Wrappers;

namespace Drg.M3.Client
{
    public abstract class AbstractViewPage<TModel> : ViewPage<TModel>
        where TModel : class
    {
        [Obsolete("Do not reference the database from views")]
        public new IM3Session Session
        {
            get
            {
                if (M3Context.Current.Controller is AbstractController)
                    return (M3Context.Current.Controller as AbstractController).Session;
                else
                    return null;
            }
        }
    }

    public abstract class AbstractViewPage : AbstractViewPage<object>
    {

    }

    public abstract class AbstractViewMasterPage : ViewMasterPage
    {
    }
}
