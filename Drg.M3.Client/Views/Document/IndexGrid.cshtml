@{
    Layout = "";
    
    var settings = new GriddlySettings<Drg.M3.Client.Models.Core.FileExplorerModel>()
    {
        Title = "Documents and Folders",
        RowClickUrl = @<text>@(item.Type=="Document" ? Url.Action("Profile", "Document", new { area = "", item.Id }) : Url.Action("Index", "Document", new { area = "", item.Id }))</text>,
        AllowedFilterModes=FilterMode.Inline
    };

    settings
        .SelectColumn(m => m.CalculatedId)
        .Column("", template: @<text>@(item.Type == "Document" ?
Html.Raw("<a href=\"" + Url.Action("File", "Resource", new { area = "", Id = item.FileId }) + "\">" + Html.Icon(item.Icon, 16) + "</a>")
: Html.Icon("folder_closed", 16))</text> , width: "26px", defaultSort: SortDirection.Descending, expressionString: "Type")
        .Column(m => m.Name, filter: c => c.FilterBox(FilterDataType.String, field: "name"), defaultSort: SortDirection.Ascending)
        //.Column(m => m.Organization, filter: c => c.FilterBox(FilterDataType.String))
        .Column(m => m.w_Categories, "Category", filter: c => c.FilterReference(ReferenceType.DocumentCategory, defaultSelectAll: false, field: "categoryIds"))
        .Column("Size",
            template: @<text>@(item.ContentLength == null ? null : (item.ContentLength.Value / 1024).ToString("n0") + " KB")</text>,
            filter: c => c.FilterRange(FilterDataType.Integer, "size1", "size2"),
            expressionString: "")
        .Column(m => m.DC, "Created", filter: c => c.FilterRange(FilterDataType.Date, "created1", "created2", "Date"))
        .Column(m => m.DM, "Last Updated", filter: c => c.FilterRange(FilterDataType.Date, "modified1", "modified2", "Date"))
        .Column(m => m.Owner, filter: c => c.FilterBox(FilterDataType.String, "ownerName"))
        .Column(m => m.Versions, expressionString: "");

    if (M3Context.Current.Modules.Contains(typeof(Drg.M3.Modules.QA)))
    {
        settings.Column(m => m.IsShared, "Shared", filter: c => c.FilterBool(nullItemText: "[Any]"));
    }

    settings.Column(m => m.IsInactive, "Inactive", filter: c => c.FilterBool(nullItemText: "[Any]"));

    if (ViewBag.ReadOnly == false)
    {
        settings
            .Button("drag-drop-info", "Drag & Drop", "information", action: GriddlyButtonAction.Modal)
            .Add(new GriddlyButton() { IsSeparator = true })
            .Button("addfolder-modal", "Add Folder", "add", GriddlyButtonAction.Modal)
            .Button(@<text>@Url.Action("Create2", "Document", item == null ? null : new { labelId = (item as Label).Id })</text>, "Upload Document", "add")
            .Button(@<text>@Url.Action("UploadZip", "Document", new{labelId=ViewData["labelid"]})</text>, "Upload Zip Archive", "compress_green_up", GriddlyButtonAction.Navigate)
            .Button(@<text>@Url.Action("DownloadZip", "Document")</text>, "Download Selected", "compress_green_down", GriddlyButtonAction.Post, true)
            .Button("moveDocuments", "Move Item(s)", "move", GriddlyButtonAction.Javascript, true)


            //<m3:PostButton Action="~/Document/DownloadZip/?labelId={viewdata:labelid}" Text="Download Zip Archive" Icon="arrow_down_red" UniqueName="downloadZip" NoIdsRequired="true" />
            ;
                                                                                                                /*.Button(Url.Action("Deactivate", "Document"), "Deactivate", "deactivate", GriddlyButtonAction.AjaxBulk, true)
                                                                                                                .Button(Url.Action("Activate", "Document"), "Activate", "activate", GriddlyButtonAction.AjaxBulk, true)
                                                                                                                .Button(Url.Action("Delete", "Document"), "Delete", "delete", GriddlyButtonAction.AjaxBulk, true)*/
                                                                                                            }

                                                                                                            //settings.Add(new GriddlySearchButton());
                                                                                                        }

@Html.Griddly(settings)

<div id="addfolder-modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h3>Add Folder</h3>
            </div>
            <div class="modal-body">
                <form class="form" style="border:none;">
                    @Html.TextField("folderName", "New folder name")
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-sm btn-primary" id="addfolder-save">Save Folder</button>
            </div>
        </div>
    </div>
</div>
<div id="drag-drop-info" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h3>Drag &amp; Drop to Upload</h3>
            </div>
            <div class="modal-body">
                To quickly upload documents, you can drag-and-drop one or more files from your computer's Desktop or Windows Explorer to the area above the Drag & Drop button.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-sm btn-default" data-dismiss="modal">Ok, got it!</button>
            </div>
        </div>
    </div>
</div>


@using (Html.BeginComponentScript())
{
    <script>
        $("#addfolder-save").click(function () {
            $("#addfolder-modal").modal("hide");
            $.post(
                ResolveUrl('~/Document/CreateLabel/@(ViewData["label"] != null ? (ViewData["label"] as Drg.M3.Domain.Label).Id.ToString() : "")'),
                { name: $("#folderName").val() },
                function () {
                    $(".griddly").griddly("refresh");
                }
            );
        });

        function uploadComplete(id, name, data) {
            
        }

    </script>
}