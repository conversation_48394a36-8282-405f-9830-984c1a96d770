@using Drg.M3.Dal;
@using Drg.M3.Security;
@using Drg.M3.Client.Configuration;
@using Drg.M3.Client.Security;

@{
    var ctx = M3Context.Current;
    var menus = (RenderedMenus)ViewData["layout-menus"];
}

<div class="mainmenu d-print-none">
    <ul class="menu">
        @foreach (var mi in menus.PrimaryMenuItems)
        {
            if (!M3SettingsSection.Current.HiddenMenus.Contains(mi.Key))
            {
                var miAction = mi.FirstAvailableChild ? mi.ToList<M3MenuItem>().Where(y => LocalHelper.MenuIsAvailable(y)).FirstOrDefault() : mi;
                miAction = miAction != null && miAction.FirstAvailableChild ? miAction.ToList<M3MenuItem>().Where(y => LocalHelper.MenuIsAvailable(y)).FirstOrDefault() : miAction;
                if (miAction != null)
                {
                    <li class="@((mi == menus.SelPrimary) ? "sel" : "unsel")">
                        <a href="@GetMenuUrl(miAction)">@mi.Name</a>
                    </li>
                }
            }
        }
    </ul>
</div>
<div class="mainmenusub d-print-none">
    <ul class="submenu">
        @if (M3Context.Current.Permissions.HasPermission(menus.SelPrimary))
        {
            foreach (var mi in menus.SecondaryMenuItems)
            {
                var miAction = mi.FirstAvailableChild ? mi.ToList<M3MenuItem>().Where(y => LocalHelper.MenuIsAvailable(y)).FirstOrDefault() : mi;
                if (miAction != null)
                {

                    var menu2Templates = menus.Menu2Templates.Where(m2l => m2l.Template != null && m2l.MenuKey == mi.Key);
                    var ccps = CustomContent.Where(cc => cc.MenuKey == mi.Key && HasPermission(cc)).OrderBy(x => x.Name);
                    var searches = AdvancedSearches.Where(s => s.MenuKey == mi.Key);
                    var dataCalls = DataCallAssignments.Where(x => x.DataCall.MenuKey == mi.Key);

                    <li class="submenu@((mi == menus.SelSecondary) ? " sel" : "")">
                        <a href="@GetMenuUrl(miAction)" class="submenu @((menus.SelectedMenuItem == miAction) ? " imm" : "")">@mi.Name</a>

                        @if (mi.Exists(m => LocalHelper.MenuIsAvailable(m)) || menu2Templates.Count() > 0 || ccps.Count() > 0 || searches.Count() > 0 || dataCalls.Any())
                        {
                            <ul class="dropdownmenu">
                                @foreach (var mi2 in mi.ToArray().Where(m => LocalHelper.MenuIsAvailable(m))
                                    .Select(x => x.FirstAvailableChild ? x.ToList<M3MenuItem>().Where(y => LocalHelper.MenuIsAvailable(y)).FirstOrDefault() : x).RemoveNull())
                                {
                                    var miAction2 = mi2.FirstAvailableChild ? mi2.ToList<M3MenuItem>().Where(y => LocalHelper.MenuIsAvailable(y)).FirstOrDefault() : mi2;
                                    if (miAction2 != null)
                                    {
                                        <li class="dropdownmenu@((miAction2 == menus.SelTertiary) ? " sel" : "")">
                                            <a href="@GetMenuUrl(miAction2)" class="dropdownmenu @((menus.SelectedMenuItem == miAction2) ? " imm" : "")">@mi2.Name</a>
                                        </li>
                                    }
                                }
                                @foreach (var cl in menu2Templates)
                                {
                                    <li class="dropdownmenu@((cl == menus.SelectedMenu2Template) ? " sel" : "")">
                                        <a href="@ctx.ResolveUrl("~/CustomField/ListView/" + cl.Template.Id)" class="dropdownmenu">@cl.Template.Name</a>
                                    </li>
                                }
                                @foreach (var ccp in ccps)
                                {
                                    <li class="dropdownmenu@((ccp == SelectedCustomContent) ? " sel" : "")">
                                        <a href="@ctx.ResolveUrl("~/Common/CustomContent/" + ccp.Id)" class="dropdownmenu">@ccp.Name</a>
                                    </li>
                                }
                                @foreach (var s in searches)
                                {
                                    <li class="dropdownmenu">
                                        <a href="@s.Url" class="dropdownmenu">@s.Name</a>
                                    </li>
                                }
                                @foreach (var dca in dataCalls)
                                {
                                    <li class="dropdownmenu@((dca == SelectedDataCallAssignment) ? " sel" : "" )">
                                        <a href="@ctx.ResolveUrl("~/DataCalls/AssignmentEntry/" + dca.Id)" class="dropdownmenu">@dca.DataCall.Name</a>
                                    </li>
                                }
                            </ul>
                        }
                    </li>
                }
            }
            foreach (var cl in menus.Menu2Templates.Where(m2l => m2l.Template != null && m2l.MenuKey == menus.SelPrimary.Key))
            {
                <li class="submenu@((cl == menus.SelectedMenu2Template) ? " sel" : "")">
                    <a href="@ctx.ResolveUrl("~/CustomField/ListView/" + cl.Template.Id)" class="submenu@((cl == menus.SelectedMenu2Template) ? " imm" : "")">@cl.Template.Name</a>
                </li>
            }
            foreach (var ccp in CustomContent.Where(cc => cc.MenuKey == menus.SelPrimary.Key && HasPermission(cc)).OrderBy(p => p.Name))
            {
                <li class="submenu@((ccp == SelectedCustomContent) ? " sel" : "")">
                    <a href="@ctx.ResolveUrl("~/Common/CustomContent/" + ccp.Id)" class="submenu@((ccp == SelectedCustomContent) ? " imm" : "")">@ccp.Name</a>
                </li>
            }
            foreach (var s in AdvancedSearches.Where(s => s.MenuKey == menus.SelPrimary.Key))
            {
                <li class="submenu">
                    <a href="@s.Url" class="submenu">@s.Name</a>
                </li>
            }
            foreach (var dca in DataCallAssignments.Where(dca => dca.DataCall.MenuKey == menus.SelPrimary.Key).OrderBy(x => x.Name))
            {
                <li class="submenu @((dca == SelectedDataCallAssignment) ? " sel" : "" )">                    
                    <a href="@ctx.ResolveUrl("~/DataCalls/AssignmentEntry/" + dca.Id)" class="submenu@((dca == SelectedDataCallAssignment) ? " imm" : "")">@dca.DataCall.Name</a>
                </li>
            }
        }
    </ul>
    <div style="clear:both;"></div>
</div>

@functions
{
    protected bool HasPermission(CustomContent cc)
    {
        return cc.w_UserGroupCount == 0 || M3Context.Current.User.UserGroups.Intersect(cc.UserGroups).Count() > 0;
    }

    IList<CustomContent> _customContent;
    protected IList<CustomContent> CustomContent
    {
        get
        {
            if (M3Context.Current.Organization == null) return new List<CustomContent>();
            if (_customContent == null)
            {
                _customContent = new Drg.M3.Dao.CustomContentDao(M3SessionSingleton.Instance).GetAllForMenus(M3Context.Current.Organization)
                    .Where(cc =>
                        cc.ParametersDict["function"] == null || M3Context.Current.Permissions.HasPermission(cc.ParametersDict["function"]))

                    //.Where(cc => M3Context.Current.Permissions.HasPermission(typeof(CustomContent).FullName + "." + cc.Id, AccessType.View, true))
                    .ToList();
            }
            return _customContent;
        }
    }

    protected CustomContent SelectedCustomContent
    {
        get
        {
            return ViewData["menuCustomContent"] as CustomContent;
        }
    }

    protected DataCallAssignment SelectedDataCallAssignment
    {
        get
        {
            return ViewData["selectedDataCallAssignment"] as DataCallAssignment;
        }
    }

    IEnumerable<AdvancedSearch> _advancedSearches;
    protected IEnumerable<AdvancedSearch> AdvancedSearches
    {
        get
        {
            if (M3Context.Current.Organization == null) return new List<AdvancedSearch>();
            if (_advancedSearches == null)
                _advancedSearches = new Drg.M3.Dao.AdvancedSearchDao(M3SessionSingleton.Instance).GetAllForMenus(M3Context.Current.Organization, M3Context.Current.User);
            return _advancedSearches;
        }
    }

    IEnumerable<DataCallAssignment> _dataCallAssignments;
    protected IEnumerable<DataCallAssignment> DataCallAssignments
    {
        get
        {
            if (M3Context.Current.Organization == null) return new List<DataCallAssignment>();
            if (_dataCallAssignments == null)
                _dataCallAssignments = new Drg.M3.Dao.DataCallDao(M3SessionSingleton.Instance).GetAssignmentsForMenus(M3Context.Current.Organization);
            return _dataCallAssignments;
        }
    }

    private static Dictionary<string, string> _menuCache = new Dictionary<string, string>();
    private static object _menuCacheLock = new Object();
    protected string GetMenuUrl(M3MenuItem miAction)
    {
        if (!_menuCache.ContainsKey(miAction.Key))
        {
            lock (_menuCacheLock)
            {
                if (!_menuCache.ContainsKey(miAction.Key))
                {
                    _menuCache[miAction.Key] = Url.Action(miAction.ControllerAction, miAction.ControllerType.Name.TrimEnd("Controller"), new { id = "", area = miAction.Area });
                }
            }
        }
        return _menuCache[miAction.Key];

    }
}