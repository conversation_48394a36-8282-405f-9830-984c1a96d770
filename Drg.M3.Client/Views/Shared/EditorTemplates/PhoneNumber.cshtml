@model string

@if (ViewBag.IsBootstrap4 == true)
{
    var o = ViewData.ModelMetadata;

    var attrs = Html.GetUnobtrusiveValidationAttributes(o.PropertyName, o);
    attrs.Add("class", "form-control");
    Html.MergeHtmlAttributes(attrs);

    attrs["data-type"] = "phone";

    <div class="form-group has-label">
        @Html.Label("", o.DisplayName, new { @class = $"{(attrs.ContainsKey("data-val-required") ? "required" : "")}" })
        <div class="input-group">
            @Html.TextBox("", Model, attrs)
            <div class="input-group-append"><span class="input-group-text"><span class="@(ViewBag.Icon??"fas fa-phone")"></span></span></div>
        </div>
    </div>
}
else
{
    var attrs = Html.GetUnobtrusiveValidationAttributes(ViewData.ModelMetadata.PropertyName, ViewData.ModelMetadata);
    attrs.Add("type", "tel");
    Html.MergeHtmlAttributes(attrs);

    @Html.TextBox("", Model, attrs)
}