<%@ Control Language="C#" Inherits="System.Web.Mvc.ViewUserControl<List<Drg.M3.Domain.Radiology.WorkflowStepLog>>" %>
<% var logs = (Model ?? new List<Drg.M3.Domain.Radiology.WorkflowStepLog>()).OrderByDescending(l => l.DC);    
if (logs.Any()) { %>
<table class="grid">
    <thead>
        <tr class="header">
            <td colspan="7">Log</td>
        </tr>
        <tr class="columnHeaders">
            <th>Step</th>
            <th>Date</th>
            <th>Comments</th>
            <th>Information</th>            
            <th>Version</th>
            <th>Document Status</th>
            <th>Assigned To</th>
        </tr>
    </thead>
    <tbody class="data">
        <% foreach(var log in logs) { %>
        <tr class="<%=log.WorkflowStepLogType.ToString().ToLower() %>">
            <% if (log.WorkflowStepLogType == Drg.M3.Domain.Radiology.WorkflowStepLogType.Comment) {%> 
            <td><%=log.WorkflowStep.Name %></td>
            <td><%=log.DC.ToShortDateTimeString() %></td>
            <td colspan="5"><%=log.LastActionUser %> wrote <%=log.Comments %></td>
            <% } else { %>        
            <td><%=log.WorkflowStep.Name %></td>
            <td><%=log.DC.ToShortDateTimeString() %></td>
            <td><%=log.Comments %></td>
            <td><%=log.w_Text %></td>            
            <td><a target="_blank" href="<%=Url.Action("File", "Resource", new { id = log.File.Id }) %>"><%=log.File.Document.Files.ToList().IndexOf(log.File) + 1 %> of <%=log.File.Document.Files.Count %></a></td>
            <td><%=log.DocumentStatus == null ? "" : log.DocumentStatus.Name %></td>
            <td><%=log.w_Distribution %></td>
            <% } %>
        </tr>
        <% } %>
    </tbody>
</table>            
<% } %>