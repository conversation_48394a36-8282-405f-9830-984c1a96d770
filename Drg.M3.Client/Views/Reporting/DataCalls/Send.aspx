<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<DataCall>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <% using (Html.BeginFormCurrentAction(new { id = "frmSend", Class="medium"})) { %>
	
		<div class="frbody form">
			<div class="top"><span class="title">Send Data Call</span></div>
			
			<div class="group">
				<div><div class="info"><b>Note:</b> This operation is non-reversible. If your Data Call is not finalized, do not Submit the Data Call at this time.</div></div>
            </div>
            <div class="group">
                <div><div class="info"><i>This operation may take a while. Please be patient.</i></div></div>
			</div>
			
		    <div class="errors"></div>
		    
		    <div class="buttons">
			    <input type="submit" value="Send" />
                <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="return cancel();">Cancel</a>
		    </div>
		</div>
		
	<% } %>
    
</asp:Content>
