<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" AutoEventWireup="true" Inherits="AbstractViewPage<DataCallGroup>" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz.Elements" %>
<%@ Import Namespace="Drg.Core" %>

<asp:Content ID="Content1" ContentPlaceHolderID="Styles" runat="server">
<style type="text/css">
.section{font-weight:bold;font-size:1.2em;}
.rankedSection{font-weight:bold;}
table.grid tbody.data input[type=checkbox]{float:none;} 
table.grid tbody.data tr td {padding:0px 4px 0px 4px;}
table.grid thead tr.header {display:none;}
</style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    
    <%if(ViewData.Model!=null){
          pageButtons.GetButton("delete").Action = ResolveUrl("~/Common/Delete/" + Model.Id + "?cc=" + Model.GetClassCode() + "&returnUrl=/Reporting/DataCalls/Groups/" + Model.DataCall.Id);
          pageButtons.GetButton("delete").Visible = Model != null;
          %>
    <m3:PageButtonGroup ID="pageButtons" runat="server" Type="Output">
        <m3:PageButton Icon="delete" Text="Delete" UniqueName="delete" Visible="false" ConfirmMessage="This record and all related data will be lost. Are you sure you want to delete this record?" /> 
    </m3:PageButtonGroup>
    <%} %>
    
    <% 
        grdMain.DataSource = GridDataSource.Create((ViewData["template"] as Template).Elements.ToList(), typeof(Element));
        grdMain.RowFormat += delegate(object sender, RowFormatEventArgs e)
        {
            if (e.DataItem is Drg.M3.Domain.Quiz.Elements.Section)
                e.ClassName = "section";
            if (e.DataItem is Drg.M3.Domain.Quiz.Elements.RankedSection)
                e.ClassName = "rankedSection";
            if (e.DataItem is Drg.M3.Domain.Quiz.Elements.RankedQuestion)
                e.ClassName = "rankedQuestion";
        };
    %>

    <script type="text/C#" runat="server">
        Dictionary<int, Element> viewDict = new Dictionary<int,Element>();
        public void Page_Load(object sender, EventArgs ea)
        {
            if (ViewData.Model != null)
                viewDict = ViewData.Model.Questions.ToDictionary(q => q.Id);
        }
    </script>

    <% using(Html.BeginFormCurrentAction(new { id = "frmEditGroup", Class = "medium" })) { %>
		<div class="frbody form">
			<div class="top"><span class="title"><%=ViewData.Model==null?"New":"Edit" %> Group</span></div>
		
			<div class="group">
                <div><%:Html.TextField("name", "Group Name")%></div>
                <div><%:Html.TextField("weight", "Weight")%></div>
                <div><%:Html.CheckBoxField("isInternal", "This group is Internal") %></div>
			</div>
            
            <div class="category">Questions:</div>
            <div class="group">
                <m3:Grid ID="grdMain" runat="server" Title="Questions" SaveSort="false" SortColumn="DisplayOrder" EnableFilter="false" EnablePaging="false" EnableFooter="false" EnablePrint="false">
                    <Columns>
                        <m3:BoundColumn DataField="Text" MaxLength="100" Sortable="false"/>
                        <m3:TemplateColumn Caption="Type" Sortable="false">
                            <Template>
                                <%#Container.DataItem.GetType().ToStringDescription() %>
                            </Template>
                        </m3:TemplateColumn>
                        <m3:TemplateColumn Caption="Include" Align="Center" Width="50px">
                            <Template>
                                <input type="checkbox" class="view" name="v_<%#(Container.DataItem as Element).Id %>" <%#viewDict.ContainsKey((Container.DataItem as Element).Id)?"checked=\"checked\"":"" %>/>
                            </Template>
                        </m3:TemplateColumn>
                    </Columns>
                </m3:Grid>
            </div>

			<div class="errors"></div>
				
            <div class="buttons">
                <input type="submit" value="Save Group" /> 
                <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="return cancel();">Cancel</a>
			</div>
		</div>
	<% } %>

</asp:Content>


<asp:Content ID="Content3" ContentPlaceHolderID="Scripts" runat="server">
<script>
    $(function () {
        $("#frmEditGroup .grid tbody.data input:checkbox").change(function () {
            var chk = $(this);
            var row = chk.parents("tr:first");

            if (row.hasClass("section")) {
                var childRow = row.next("tr");

                //propagate to all child rows
                while (!childRow.hasClass("section")) {
                    childRow.find("input.view")[0].checked = row.find("input.view")[0].checked;

                    childRow = childRow.next("tr");
                }
            } else if (row.hasClass("rankedSection")) {
                var childRow = row.next("tr");

                //propagate to all child rows
                while (childRow.hasClass("rankedQuestion")) {
                    childRow.find("input.view")[0].checked = row.find("input.view")[0].checked;

                    childRow = childRow.next("tr");
                }
            }
        });
    });
</script>
</asp:Content>

