<%@ Control Language="C#" Inherits="Drg.M3.Client.AbstractViewUserControl<Question>" %>
<%@ Import Namespace="Drg.Core" %>
<%@ Import Namespace="Drg.M3.Bll" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz.Elements" %>

<table class="grid data">
<thead>
    <tr>
        <th>Organization</th>
        <th>Special Area</th>
        <th>Stakeholder Group</th>
        <th>Person</th>
        <th>Value</th>
    </tr>
</thead>
<tbody class="data">
<%
    var dc = ViewData["datacall"] as DataCall;
    var dca = ViewData["dca"] as DataCallAssignment;
    var dcas = dca == null ? dc.Assignments : new DataCallBll(Session).GetAssignmentsRecursive(dca);

    foreach (var assg in dcas.Where(a => a.Quiz != null))
    {
        IEnumerable<Quiz> quizzes = new[] { assg.Quiz };
        var table = ViewData.Model.GetAncestorTable();
        if (table != null)
            quizzes = new Drg.M3.Dao.Quiz.QuizDao(Session).GetTableRows(assg.Quiz, table, true);

        foreach (var quiz in quizzes)
        {
            var val = quiz.GetValue(ViewData.Model.ColumnName);

            if (val != null)
            {
%>
<tr>
<td><%=assg.Organization%></td>
<td><%=assg.SpecialArea%></td>
<td><%=assg.Group%></td>
<td><%=assg.Task != null ? assg.Task.Distribution.ToString() : assg.Settings.Name%></td>
<td><%=ViewData.Model.Format(val)%></td>
</tr>
<%}
        }
    } %>
</tbody>
</table>