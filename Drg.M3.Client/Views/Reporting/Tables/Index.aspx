<%@ Page Title="Report Templates" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage" %>
<%@ Import Namespace="Drg.M3.Bll.Reports" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
<% grdMain.GetButton("delete").Visible = M3Context.Current.Permissions.HasPermission(typeof(ReportTemplate), AccessType.Delete);
   grdMain.NeedDataSource += (EventHandler<GridEventArgs>)ViewData["GridDataSource"];
%>

<script type="text/C#" runat="server">
    private bool CanEdit(CustomTable dataItem)
    {
        if (dataItem.User == M3Context.Current.User)
            return true;
        else if (dataItem.Organization != null)
        {
            if (dataItem.Organization == M3Context.Current.Organization)
                return true;
            else return false;
        }
            
        return false;
    }
</script>

<m3:Grid Title="Tables" runat="server" ID="grdMain" EnablePrint="true">
    <Buttons>
        <m3:ActionButton Text="New Table" Icon="add" Action="~/Reporting/Tables/Edit" />
        <m3:AjaxButton Action="~/Reporting/Tables/Delete" Text="Delete" Icon="delete" UniqueName="delete" />
    </Buttons>
    <Columns>
        <m3:SelectColumn />
        <m3:TemplateColumn Caption="Run" Align="Center" Width="32px">
            <Template>
                <a href="<%#ResolveUrl("~/Reporting/Tables/Table/" + DataBinder.Eval(Container.DataItem, "Id")) %>">
                    <div class="icon16 table2"></div></a>
            </Template>
        </m3:TemplateColumn>
        <m3:TemplateColumn Caption="Name" DataField="Name" Width="50%">
            <Template>
                <%#(CanEdit(Container.DataItem as CustomTable))
                    ? "<a href=\"" + ResolveUrl("~/") + "Reporting/Tables/Edit/" + DataBinder.Eval(Container.DataItem, "Id") + "\">" + DataBinder.Eval(Container.DataItem, "Name") + "</a>" 
                    : DataBinder.Eval(Container.DataItem, "Name") %>
            </Template>
        </m3:TemplateColumn>
        <m3:OrgColumn />
        <m3:BoolColumn DataField="IsShared" Caption="Shared" />
        <m3:BoundColumn DataField="ClassCaption" Caption="Data Class" />
        <m3:BoundColumn DataField="PrimaryBreakdownDisplay" Caption="Primary Breakdown" />
        <m3:BoundColumn DataField="Category" />		
        <m3:BoundColumn DataField="Columns.Count" Caption="Columns" />
        <m3:BoolColumn DataField="IsDashboard" Caption="Dashboard" />
    </Columns>
</m3:Grid>

</asp:Content>