@model ChartTemplate

<div id="chart-container" style="min-width: 400px;  height: @(Model.Size=="Small"?200:Model.Size=="Medium"?400:Model.Size=="Large"?600:400)px; margin: 0 auto"></div>

@if (Model != null) {
    if (ViewBag.DashboardMetric == null)
    {
        <div><a target="_blank" href="@Url.Action("Execute", "Queries", new { Model.AdvancedSearch.Id, outputFormat = "export" })@ViewBag.QueryParameters">Export Data</a></div>
    }
    else if(Model.AdvancedSearch.ReportTemplate is SpreadsheetTemplate)
    {
        <div><a id="export-data-filtered" href="javascript:;" data-query-id="@Model.AdvancedSearch.Id">Export Data</a></div>
    }
}


@using (Html.BeginComponentScript()){
    
@Scripts.Render("~/scripts/highchart.js")
    
<script type="text/javascript">
$(function () {
    var seriesData=@ViewData["series"];
    @if(Model.ChartType=="Pie"){ <text>
    var pieTotal = 0;
    if(seriesData.length>0)
        for(var i=0;i<seriesData[0].data.length;i++)
            pieTotal+=seriesData[0].data[i][1];
    </text>} 
    var centerOffset=0;
    var chart;
    $(document).ready(function() {
        chart = new Highcharts.Chart({
            chart: {
                renderTo: 'chart-container',
                type: '@Model.ChartType.ToLower()'
            },
            credits: {
                enabled: false
            },
            title: {
                text: '@Server.HtmlEncode((string)ViewData["chartTitle"])',
                x:centerOffset
            },
            subtitle: {
                text: '@Server.HtmlEncode(M3Context.Current.Organization.ToString())',
                x:centerOffset
            },
            @if(Model.ChartType=="Column"||Model.ChartType=="Bar"){<text>
            xAxis: {
                title: {
                    text: '@Model.AxisXTitle'
                },
                categories: @ViewData["categories"] ,
                labels: {
                    rotation:@(Model.LabelOrientationX==ChartLabelOrientation.Diagonal?"45":
                        Model.LabelOrientationX==ChartLabelOrientation.Vertical?"90":"0") ,
                    staggerLines: @(Model.StaggerLinesX ? "2" : "null") 
                    @if(Model.ChartType=="Column"){ <text>
                    ,align: "@(Model.LabelOrientationX==ChartLabelOrientation.Diagonal?"left":
                        Model.LabelOrientationX==ChartLabelOrientation.Vertical?"left":"center")"
                    </text>} 
                }
            },
            yAxis: {
                @if(Model.AggregateFunction==AggregateFunction.PercentNotNull){ 
                @:max: 100,
                } 
                title: {
                    text: '@Model.AxisYTitle'
                },
                plotLines: [{
                    value: 0,
                    width: 1,
                    color: '#808080'
                }]
            },
            </text>} 

            @if(Model.ChartType=="Pie"){ <text>
            tooltip: {
                formatter: function() {
                    @if (Model.InvertTags) { 
                    @:return '<b>'+ this.point.name +'</b>: '+ (this.y / pieTotal * 100).toFixed(2) +' %';
                    } else { 
                    @:return '<b>'+ this.point.name +'</b>: '+ this.y;
                    } 
                }
            },
            </text>}else{<text> 
            tooltip: {
                formatter: function() {
                    @if(Model.AggregateFunction==AggregateFunction.PercentNotNull){ <text>
                    return @if(Model.SeriesProperty!=null){<text>'<b>'+ this.series.name +'</b><br/>'+</text>} '<b>' + this.x +'</b>: '+ this.y.toFixed(2) + ' %';
                    </text>} else {<text>
                    return @if(Model.SeriesProperty!=null){<text>'<b>'+ this.series.name +'</b><br/>'+</text>} '<b>' + this.x +'</b>: '+ this.y;
                    </text>} 
                }
            },
            </text> } 
            plotOptions: {
                @if(Model.ChartType=="Pie"){<text> 
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: @Model.ShowDataLabels.ToString().ToLower(),
                        formatter: function() {
                            @if (Model.InvertTags) {
                            @:return '<b>'+ this.point.name +'</b>: '+ this.y;                            
                            } else { 
                            @:return '<b>'+ this.point.name +'</b>: '+ (this.y / pieTotal * 100).toFixed(2) +' %';
                            } 
                        }
                    }
                }
                </text>} else if (Model.ChartType=="Bar") {<text>
                bar: {
                    dataLabels: {
                        enabled: @Model.ShowDataLabels.ToString().ToLower(),
                        crop: false
                    }
                }
                </text>} else if (Model.ChartType=="Column") {<text>
                column: {
                    dataLabels: {
                        enabled: @Model.ShowDataLabels.ToString().ToLower()
                    }
                }
                </text>} 
            },
            legend: {
                @if(Model.SeriesProperty!=null){<text> 
                layout: '@(Model.LegendLocation=="Right"?"vertical":"horizontal")',
                align: '@(Model.LegendLocation=="Right"?"right":"center")',
                verticalAlign: '@(Model.LegendLocation=="Right"?"top":"bottom")',
                x: @(Model.LegendLocation=="Right"?"0":"centerOffset"),
                y: @(Model.LegendLocation=="Right"?"100":"0"),
                borderWidth: 0
                </text>}else{ <text>
                enabled:false
                </text>} 
            },
            exporting: {
                width: 1600
            },
            series: seriesData
        });
    });

});
</script>
}