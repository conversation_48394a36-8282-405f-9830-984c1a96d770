@model SelectClassModel
@{
    ViewBag.Title = "New Chart";
    ConfigureBootstrap4();
}

@using (Html.BeginFormEx(method: FormMethod.Get))
{
    <div class="bs-callout bs-callout-primary form-layout">
        @Html.DropDownEditorFor(x => x.cc, liveSearch: true)

        @Html.DropDownEditorFor(x => x.QuizTemplateId, Url.Action("QuizTemplates", "CustomSearch"), hidden: true, ajaxDataFunc: "QuizTemplateId_AjaxData", htmlAttributes: new { data_val_required = "The Template field is required." })
    </div>

    @Html.FormButtons("Proceed", renderAntiForgeryToken: false)
}

@section Scripts{
    <script>
        var dca = "@typeof(Drg.M3.Domain.DataCallAssignment).GetClassCode()";
        var quiz = "@typeof(Drg.M3.Domain.Quiz.Quiz).GetClassCode()";

        function QuizTemplateId_AjaxData() {
            return { q: '{{{q}}}', dataCallsOnly: $("#cc").val() == dca };
        }

        $("#cc").change(function () {
            var val = $(this).val();
            $("#QuizTemplateId").closest(".form-group").toggle(val == dca || val == quiz);
            $("#QuizTemplateId").html("").selectpicker("refresh");
        });
    </script>
}