<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" AutoEventWireup="true" Inherits="Drg.M3.Client.AbstractViewPage<Drg.M3.Domain.Organization>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <% 
       grdMain.NeedDataSource += ViewData["orgs"] as EventHandler<GridEventArgs>;
    %>
    <m3:Grid runat="server" ID="grdMain" KeyDataField="Id" Title="Assigned Organizations">
        <Buttons>
            <m3:TemplateButton>
                <Template>
                    <a href="<%=ResolveUrl("~/Resource/RequiresJavascript") %>" class="btn btn-default btn-sm" title="Add Organization" onclick="$(this).hide();$('#spAdd').show();$('#orgId').focus();return false;">
                        <%=Html.Icon("add")%><span>Add Organization</span>
                    </a>
                    <span id="spAdd" class="quickentry" style="display:none;">
                        <select id="orgId" rel="{ columns: ['parent'], width: 220, action: '<%=ResolveUrl("~/Ajax/Organizations")%>' }"></select>
                        <a href="javascript:void(0);" onclick="QuickAddSelectFilter('orgId', '~/Admin/Security/Organizations/AssignOrganization/<%=ViewData.Model.Id%>?orgid=', grdMain);">
                            <%=Html.Icon("add")%>
                        </a>
                    </span>
                </Template>
			</m3:TemplateButton>    
			<m3:AjaxButton Action="~/Admin/Security/Organizations/UnassignOrganizations/{Model:Id}" 
			    Icon="delete" Text="Remove Organizations" />
        </Buttons>
        <Columns>
            <m3:SelectColumn UniqueName="Select" />            
		    <m3:BoundColumn DataField="Name" Caption="Name"/>
        </Columns>
    </m3:Grid>

</asp:Content>
<asp:Content ContentPlaceHolderID="Scripts" runat="server">
<script type="text/javascript">
    $(function () {
        $("#orgId").drillDownFilter();
    });
</script>
</asp:Content>