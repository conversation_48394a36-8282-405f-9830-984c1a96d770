<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<Drg.M3.Domain.CustomContent>" %>
<%@ Import Namespace="Drg.Core" %>
<%@ Import Namespace="Drg.M3.Client.Configuration" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">
    
    
<%
    if (Model == null)
        pageButtons.GetButton("delete").Visible = false;
    else
        pageButtons.GetButton("delete").Action = ResolveUrl("~/Common/Delete/" + ViewData.Model.Id + "?cc=" + ViewData.Model.GetClassCode() + "&returnUrl=/Admin/AffiliateSettings/CustomContent");

    pageButtons.GetButton("delete").RequirePermission(AccessType.Delete, typeof(CustomContent));
%>
<%-- TODO: Move this button to the grid or to the page... --%>
<m3:PageButtonGroup ID="pageButtons" runat="server" Type="Output">
    <m3:PageButton Icon="delete" Text="Delete" UniqueName="delete" ConfirmMessage="This record and all related data will be lost. Are you sure you want to delete this record?" /> 
</m3:PageButtonGroup>
    
    <%
        if (ViewData.Model != null)
        {
            ViewData["html"] = new Drg.M3.Bll.CmsBll(Drg.M3.Dal.M3SessionSingleton.Instance).DecodeHtmlForEdit(ViewData.Model.Html);
        }
    %>
    
    <% using (Html.BeginFormCurrentAction(new { id = "frmEdit", Class="large"})) { %>
	
		<div class="frbody form">
			<div class="top"><span class="title">Custom Content Page</span></div>
			
			<div class="category">General</div>
			<div class="group">
			    <div><%:Html.TextField("name", "Page Title", null, new { Class = "focusfirst" })%></div>
                <div><%:Html.TextField("reference", "Reference Name (if different)")%></div>
                <div><%:Html.DdlField("pageType", "Page Type")%></div>
                <%
                if(M3Context.Current.User.UserLogin.UserName.EndsWith(M3SettingsSection.Current.AdminDomain)){ %>
                <div><%:Html.TextField("parameters", "Parameters (x)")%></div>
                <%} %>
			    <%if(ViewData["original"]==null){ %>
			    <%--<div><%:Html.DdlField("menu", "Menu") %></div>--%>
                <div>
                    <label for="menu">Menu</label>
                    <select id="menu" name="menu" rel="{action: '<%=ResolveUrl("~/Ajax/MenuItems?maxDepth=1&includeDashboardMenus=true") %>', columns:['parent'] }">
                        <%if(ViewData.Model!=null&&Drg.M3.Security.M3MenuItem.MenusByAction.GetSafe(ViewData.Model.MenuKey)!=null){ %>
                        <option value="<%=ViewData.Model.MenuKey %>" selected="selected"><%=Drg.M3.Security.M3MenuItem.MenusByAction.GetSafe(ViewData.Model.MenuKey).Name%></option>
                        <%} %>
                    </select>
                    
                </div>
                <div>
			        <label for="userGroups">Permissioned User Groups</label>
			        
                    <select id="userGroups" name="userGroups" multiple="multiple">
                        <%foreach(var ug in ViewData["allUserGroups"] as IEnumerable<UserGroup>){ %>
                        <option value="<%=ug.Id %>" <%=Model!=null&&Model.UserGroups.Contains(ug)?"selected=\"selected\"":"" %>><%=ug.Name%></option>
                        <%} %>
                    </select>
			    </div>

			    <div><%:Html.CheckBoxField("isShared", "Show this page to child organizations") %></div>
                <div><%:Html.CheckBoxField("isPublic", "Allow all Navy access") %></div>
			    <div><%:Html.CheckBoxField("allowOverride", "Allow child organizations to override this page") %></div>
			    <%} %>
			</div>
			
			<div class="category">Content</div>
			<div class="group">
			    <div><%=Html.TextArea("html", (string)ViewData["html"], new { title="Content", Class="tinymce" })%></div>
                <i>Note: If the Cut, Copy and Paste Icons do not work, this may be due to browser security restrictions.  As a workaround, please use the Ctrl-C button (to copy) and the Ctrl-V button (to paste).</i>
			</div>
            <div class="category">Scripts</div>
            <div class="group last">
			    <div><%=Html.TextArea("scripts", (string)ViewData["scripts"], new { title="Scripts", style="width:660px;height:200px;" })%></div>
			</div>
			
		    <div class="errors"></div>
		    
		    <div class="buttons">
			    <input type="submit" value="Save Custom Content" name="button" />
                <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="return cancel();">Cancel</a>
		    </div>
		</div>
		
	<% } %>
    
</asp:Content>
<asp:Content ContentPlaceHolderID="Styles" runat="server">
    <style>
        .mce-window iframe {
            background-color:white;
        }
    </style>
</asp:Content>
<asp:Content ContentPlaceHolderID="Scripts" runat="server">
<%=Html.Script("~/scripts/tinymce/tinymce.js") %>
<%=Html.Script("~/scripts/tinymce/jquery.tinymce.min.js") %>
    
<script type="text/javascript">
    $(function () {
        $("#menu").drillDownFilter();
        $("#userGroups").selectfilter();
    
        $('textarea.tinymce').tinymce({
            // General options
            height: "400px",
            mode: "exact",
            elements: "html",
            skin:"lightgraynofonts",
            theme: 'modern',
            plugins: [
              'advlist autolink lists link image charmap hr',
              'searchreplace visualblocks code fullscreen',
              'insertdatetime save table contextmenu directionality',
              'paste textcolor colorpicker textpattern compat3x'
            ],
            toolbar1: 'insertfile undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent',
            toolbar2: 'fontselect fontsizeselect | forecolor backcolor | link image | ccpLink documentLink insertImage',
            extended_valid_elements: "iframe[src|width|height|name|align|style|frameborder]",
            content_css: "<%=Url.ResolveCdnUrl("~/content/css/bundle.css") %>",
            class_filter: function (cls, rule) {

                if (new RegExp("^\.customContent ").test(rule)) {
                    return cls;
                }
                return false;
            },
            body_class : "customContent",
            setup: function (ed) {
                // Add a custom button 
                ed.addButton('ccpLink',
                {
                    title: 'Add link to another page',
                    image: ResolveCdnUrl('~/content/images/tinymce/window_earth_add.png'),
                    onclick: function () {
                        openDialog(ed, "~/Admin/AffiliateSettings/CustomContent/CcpLink");
                    }
                });
                ed.addButton('documentLink',
                {
                    title: 'Add link to a document',
                    image: ResolveCdnUrl('~/content/images/tinymce/document_add.png'),
                    onclick: function () {
                        openDialog(ed, "~/Document?popup=true");
                    }
                });
                ed.addButton('insertImage',
                {
                    title: 'Add an image',
                    image: ResolveCdnUrl('~/content/images/tinymce/photo_landscape_add.png'),
                    onclick: function () {
                        openDialog(ed, "~/Admin/AffiliateSettings/CustomContent/Image");
                    }
                });
            }
        });
    });
    function openDialog(ed, url) {
        ed.windowManager.open({
            url: ResolveUrl(url),
            width: 800,
            height: 600,
            inline: true,
            popup_css: false
        }, { insert: function(html) {
            ed.insertContent(html);
        }
        });
    }
</script>
</asp:Content>
