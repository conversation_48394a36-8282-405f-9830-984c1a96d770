<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" AutoEventWireup="true" Inherits="Drg.M3.Client.AbstractViewPage" %>

<asp:Content ID="Content1" ContentPlaceHolderID="Styles" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">

    <% using (Html.BeginFormCurrentAction(new { Class = "medium" })) { %>
        <div class="form">
            <div class="top"><span class="title">EMS Settings</span></div>
            
            <% if (!string.IsNullOrEmpty(ViewData.String("message"))) { %>
            <div class="group message">
                <%=ViewData["message"]%>
            </div>
            <% } %>
            
            <div class="group last">
                <div><%:Html.CheckBoxField("hideSignificantAspects", "Do not show significant aspects on home menu")%></div>
                <div><%:Html.CheckBoxField("disableBatchChecklistQuestionEditing", "Do not show batch edit controls on checklists")%></div>
            </div>
            
            <div class="buttons">
                <input type="submit" value="Save Settings" />
                <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="return cancel();">Cancel</a>
            </div>
        </div>
    <% } %>

</asp:Content>

<asp:Content ID="Content3" ContentPlaceHolderID="Scripts" runat="server">
</asp:Content>
