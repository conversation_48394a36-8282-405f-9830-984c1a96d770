<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

<%if(ViewData["errors"]!=null){ %>
    <b>Completed with <%=(ViewData["errors"] as List<string>).Count %> errors</b>
    <ul>
    <%foreach(string error in ViewData["errors"] as List<string>){ %>
        <li><%=error %></li>
    <%} %>
    </ul>
<%}else{ %>
    <a style="font-size: 15px;" href="<%= Url.ResolveCdnUrl("~/content/Documents/listbuilder_template.xls") %>">Click here to download Excel Template for upload.</a>
    <form action="<%=ResolveUrl("~/Admin/Utility/ImportListBuilderItems") %>" method="post" enctype="multipart/form-data" id="uploadForm">
        <br /><input type="file" name="listBuilderDoc" id="listBuilderDoc" /><br /><br />
        <input type="submit" />
    </form><br /><br />
<%} %>
</asp:Content>
