<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Help/Help.Master" Inherits="Drg.M3.Client.AbstractViewPage<Topic>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <div class="notes">
        <% if (ViewData["tutorial"] != null || Model.Tutorials.Count == 1) {
               var tutorial = ViewData["Tutorial"] as Tutorial ?? Model.Tutorials.First().Tutorial; %>
        <h4>Tutorial: <%=Html.ActionLink(tutorial.Name, "Tutorial", "Help", new { tutorial.Id }, null)%></li></h4>
        <ul class="tutorial">
            <% foreach (var t in tutorial.Topics.OrderBy(its => its.Order)) { %>
            <li topic="<%=t.Id%>">
                <%=Html.ActionLink(t.Topic.Name, "Topic", "Help", new { t.Topic.Id }, new { Class = (t.Topic.Id == Model.Id ? "sel" : "") })%></li>
            <% } %>
        </ul>
        <% } else if (Model.Tutorials.Count > 0) {%>
        <h4>Tutorials</h4>
        <ul>
            <% foreach (var t in Model.Tutorials) { %>
            <li>
                <%=Html.ActionLink(t.Tutorial.Name, "Tutorial", "Help", new { t.Tutorial.Id }, null)%></li>
            </li>
            <% } %>
        </ul>
        <% } %>
        <%--<h4>Related Topics</h4>
        <ul>
            <% if (new Drg.M3.Bll.Help.TopicBll(Session).GetRelatedTopics(ViewData.Model) != null) { %>
            <% foreach (Topic t in new Drg.M3.Bll.Help.TopicBll(Session).GetRelatedTopics(ViewData.Model)) { %>
            <li><%=Html.ActionLink(t.Name, "Topic", "Help", new { t.Id }, null)%></li>
            <% } %>
            <% } else { %>
            <li>None</li>
            <% } %>
        </ul>
        <hr />--%>
        <h4>Keywords</h4>
        <ul class="keywords">
            <% if (ViewData.Model.Keywords != null) { %>
            <% foreach (string s in ViewData.Model.Keywords.Split(new[]{","}, StringSplitOptions.RemoveEmptyEntries).OrderBy(k => k)) { %>
            <li><%=Html.ActionLink(s, "Search", "Help", new { keyword = s }, null)%></li>
            <% } %>
            <% } else { %>
            <li>None</li>
            <% } %>
        </ul>
    </div>
    
    <h1><%=ViewData.Model.Name%></h1>
    <div class="noprint">
        <% if (M3Context.Current.Permissions.HasPermission(Drg.M3.Security.FunctionKeys.HelpContents, AccessType.Edit)) { %>
            <%= Html.ActionLink("Edit topic", "EditTopic", "Help", new { Model.Id }, null)%>
            <%if (ViewData["pageId"] != null){ %>
            | <%= Html.ActionLink("Manage help for this page", "ManagePage", "Help", new { pageId = ViewData["pageId"] }, null)%>
            <%} %>
            |
        <% } %>
        <a href="javascript:window.print();">Print</a>
    </div>

    <div class="contents">
        <%=Model.Html%>
    </div>

</asp:Content>
