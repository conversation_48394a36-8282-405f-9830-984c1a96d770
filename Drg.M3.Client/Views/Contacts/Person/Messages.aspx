<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<dynamic>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <style>
        table.grid span.body { color: #666; }
        table.grid tr.unread { font-weight: bold; }
        table.grid tr.unread span.body { font-weight: normal; }
        table.grid tr.read { background-color: #eaeaea; }
    </style>
    
    <% 
        grdMessages.NeedDataSource += ViewData["messages"] as EventHandler<GridEventArgs>;
        var rowFormat = new EventHandler<RowFormatEventArgs>(delegate(object sender, RowFormatEventArgs e)
        {
            BLICommunication m = (e.DataItem as BLICommunication);
            
            if (m.GetIsRead(M3Context.Current.User))
                e.ClassName = "read";
            else
                e.ClassName = "unread";
        });
        grdMessages.RowFormat += rowFormat;
    %>
    
    <m3:Grid runat="server" ID="grdMessages" Title="Messages" RowClickUrl="~/Home/Messages/Profile/{id}" SortColumn="DC" SortDesc="true" QuickSearch="true">
        <FilterForm>
            <%=grdMessages.FilterTextBox("quickSearch", "Search text")%>
            <%=grdMessages.FilterBoolean("isRead", "Read")%>
            <%=grdMessages.FilterBoolean("isImportant", "Important")%>
            <%=grdMessages.FilterBoolean("hasAttachments", "Has Attachments")%>
            <%=grdMessages.FilterDateRange("fromDate", "toDate", "Received")%>
        </FilterForm>
        <Buttons>
            <m3:ActionButton Action="~/Home/Messages/Create/" Icon="add" Text="New Message" />
            <m3:AjaxButton Action="~/Home/Messages/DeleteMany" Icon="delete" Text="Delete" ConfirmMessage="Are you sure you want to delete the selected message(s)? This action cannot be undone." />
        </Buttons>
        <Columns>
            <m3:SelectColumn />
            <m3:TemplateColumn DataField="Important" Width="25px" Title="Important" Caption="">
                <Template>
                    <%# (((BLICommunication)Container.DataItem).Important) ? Html.Icon("sign_warning", 16) : null%>
                </Template>
            </m3:TemplateColumn>
            <m3:TemplateColumn DataField="Files" Width="25px" Title="Attachment" Caption="">
                <Template>
                    <%# (new FileDao(Session).GetForObject((PersistentObject)Container.DataItem).ExecuteCount(Session) > 0) ? Html.Icon("mail_attachment", 16) : null %>
                </Template>
            </m3:TemplateColumn>
            <m3:BoundColumn Caption="From" DataField="Author" />
            <m3:TemplateColumn DataField="Subject" Caption="Summary">
                <Template>
                    <span class="subject"><%# (Container.DataItem as BLICommunication).Subject ?? "(no subject)"%></span> <span class="body">- <%# (Container.DataItem as BLICommunication).GetBodySummary()%></span>
                </Template>
            </m3:TemplateColumn>
            <m3:OrgColumn />
            <m3:BoundColumn Caption="Received" DataField="DC" DataType="DateTime" Width="120px" />
        </Columns>
    </m3:Grid>

</asp:Content>
