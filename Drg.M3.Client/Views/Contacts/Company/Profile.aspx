<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" AutoEventWireup="true" CodeBehind="Profile.aspx.cs" Inherits="Drg.M3.Client.Views.Contacts.Company.Profile" %>
<asp:Content ID="Content1" ContentPlaceHolderID="Styles" runat="server">
    <style type="text/css">
        #personId {width:244px;}
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    
    <% if (Model.IsInactive)
    {
        pageButtons.GetButton("deactivate").Visible = false;
        pageButtons.GetButton("activate").Visible = true;
        pageButtons.GetButton("delete").Visible = true;
    } %>
    
    <% if (ViewData.Bool("limitedread"))
       {
           pageButtons.GetButton("deactivate").Visible = false;
           pageButtons.GetButton("activate").Visible = false;
           pageButtons.GetButton("delete").Visible = false;

           grdEmployees.GetButton("add").Visible = false;
           grdEmployees.GetButton("print").Visible = false;
       } %>

    <%
        pageButtons.GetButton("edit").RequirePermission(AccessType.Edit, typeof(Company));
        pageButtons.GetButton("deactivate").RequirePermission(AccessType.Delete, typeof(Company));
        pageButtons.GetButton("activate").RequirePermission(AccessType.Add, typeof(Company));
        pageButtons.GetButton("delete").RequirePermission(AccessType.Delete, typeof(Company));

        grdEmployees.GetButton("add").RequirePermission(AccessType.Edit, typeof(Company));
        grdEmployees.GetButton("primary").RequirePermission(AccessType.Edit, typeof(Company));
        grdEmployees.GetButton("print").RequirePermission(AccessType.Edit, typeof(Company));
        grdEmployees.GetButton("addExisting").RequirePermission(AccessType.Edit, typeof(Company));
    %>
    <m3:PageButtonGroup ID="pageButtons" runat="server" Type="Output">
        <m3:PageButton Action="~/Financial/Order/Create?entityId={model:Id}" 
		    Text="New Order" Icon="add" UniqueName="newOrder" />
		<m3:PageButton Action="~/Contacts/Edit/{model:id}?tab=Company" 
			Text="Edit" Icon="edit" UniqueName="edit"/>
	    <m3:PageButton Action="~/Contacts/Deactivate/{model:id}"
	        Text="Deactivate" Icon="deactivate" UniqueName="deactivate" />
	    <m3:PageButton Action="~/Contacts/Activate/{model:id}"
	        Text="Activate" Icon="activate" UniqueName="activate" Visible="false" />
		<m3:PageButton ConfirmMessage="This record and all related data will be lost. Are you sure you want to delete this record?" Action="~/Contacts/Company/Delete/{model:id}"
		    Text="Delete" Icon="delete" UniqueName="delete" Visible="false"/>
		<m3:PrintButton UniqueName="reports" Reports="">
            <PageReports>
                <m3:ActiveReport TypeFullName="Drg.M3.Reports.Contacts.Company.CompanyProfile" />
            </PageReports>
        </m3:PrintButton>
    </m3:PageButtonGroup>

	<% Html.RenderPartial("~/Views/Dashboard/ProfileAlert.ascx" , (ViewData.Model as Entity)); %>
	
    <div class="topoverview masonryboxes">
        <div class="dashboardbox">
            <div>
                <div class="title">Company Info</div> 
        
                <% if (!string.IsNullOrEmpty(ViewData.Model.AltName)){ %>
                <b>Doing Business As:</b> <%=ViewData.Model.AltName %><br />
                <% } %>
        	
		        <%if (ViewData.Model.PrimaryPerson!=null){ %>
                <b>Default Contact:</b> <a href="<%=ResolveUrl("~/Contacts/Person/Profile/" + ViewData.Model.PrimaryPerson.Id) %>"><%=ViewData.Model.PrimaryPerson.Name%></a><br />
                <%} %>
    	   
    	        <% Html.RenderPartial("~/Views/Contacts/ProfileContactBlock.ascx"); %><br />

                <%if (ViewData.Model.WebPageUrl!=null){ %> 
                <a href="<%=ViewData.Model.WebPageUrl%>"><%=ViewData.Model.WebPageUrl%></a>
                <%} %> 
            </div>
        </div>

        <% Html.RenderPartial("~/Views/Contacts/OtherInfoBox.ascx", ViewData.Model as Entity); %>
    </div>
    
	<m3:Grid runat="server" ID="grdEmployees" KeyDataField="Id" Title="Current Employees"
        RowClickUrl="~/Contacts/Person/EditEmployment/{Id}">
	    <Buttons>
	        <m3:TemplateButton UniqueName="addExisting">
                <Template>
                    <a href="<%=ResolveUrl("~/Resource/RequiresJavascript") %>" class="btn btn-default btn-sm" title="Add existing person" onclick="$(this).hide();$('#spAdd1').show();$('#personId').focus();return false;">
                        <%=Html.Icon("add")%><span>Add existing person</span>
                    </a>
                    <span id="spAdd1" class="quickentry" style="display:none;">
                        <select name="persons" id="personId" 
                            rel="{ width: '220px', action: '<%=ResolveUrl("~/Ajax/People?select=1&oid=" + ViewData.Model.Organization.Id)%>' }">
                        </select>
                        <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="QuickAddSelectFilter('personId', '~/Contacts/Company/AddPerson/<%=ViewData.Model.Id%>?personId=', grdEmployees);return false;">
                            <%=Html.Icon("add")%>
                        </a>
                    </span>
                </Template>
	        </m3:TemplateButton>
	        <m3:ActionButton Text="Add new person" Action="~/Contacts/Person/Create?companyId={model:Id}" 
	            Icon="add" UniqueName="add" />
	        <m3:AjaxButton Text="Primary" Action="~/Contacts/Company/SetPrimaryPerson?companyId={model:Id}" 
		        Icon="check" UniqueName="primary" />
	    </Buttons>
	    <Columns>
	        <m3:SelectColumn />
		    <m3:BoundColumn Caption="Name" DataField="Person.SortName" NavigateUrl="~/Contacts/Profile/{Person.Id}" />
            <%--<m3:BoundColumn Caption="Title" DataField="Title" />--%>
		    <m3:BoundColumn Caption="Phone" DataField="Person.DefaultPhone" />
		    <m3:BoundColumn Caption="Email" DataField="Person.DefaultEmailAddress" NavigateUrl="mailto:{Person.DefaultEmailAddress}" />
		    <m3:BoundColumn Caption="Contact Type" DataField="ContactTypesStr" />
		    <m3:BoundColumn Caption="Primary" DataField="IsPrimaryPerson" />
	    </Columns>
	</m3:Grid>
    
</asp:Content>
<asp:Content ContentPlaceHolderID="Scripts" runat="server">
<script type="text/javascript">
    $(function () {
        $("#personId").selectfilter(); 
    });
</script>
</asp:Content>