@model EditTableModel
@{
    this.ConfigureBootstrap4();
}
<div class="modal fade" tabindex="-1" role="dialog" id="edit-modal">
    <div class="modal-dialog" role="document">
        @using (Html.BeginForm("EditTable", "Guidance", new { Model.Id }, FormMethod.Post))
        {
            <div class="modal-content">
                <div class="modal-header" style="padding-bottom:0">
                    <h5>@(Model.Id==null?"Add" : "Edit") Table</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">

                    @Html.ValidationSummary(false, null, new { @class = "alert alert-danger" })

                    @Html.EditorFor(x => x.Name)
                    @Html.DropDownEditorFor(x => x.Type)
                    @Html.DropDownEditorFor(x => x.GuidanceAreas)
                    @Html.DropDownEditorFor(x => x.AddPermissions)
                    @Html.DropDownEditorFor(x => x.DeletePermissions)
                    @Html.EditorFor(x => x.DisplayOrder)
                    @Html.EditorFor(x => x.CollapseByDefault)
                    @Html.EditorFor(x => x.FirstColumnIsTitle, new { hidden = Model.Type != GuidanceTableType.Callout })
                    @Html.EditorFor(x => x.HideEmpty)
                    @Html.EditorFor(x => x.CanEnableRecursiveData)
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </div>
        }
    </div>
</div>

<script>
    $(function () {
        var modal = $("#edit-modal");
        $("#Type", modal).change(function () {
            $("#FirstColumnIsTitle", modal).closest(".form-group").toggle($("#Type").val() == "1");
        });
    });
</script>