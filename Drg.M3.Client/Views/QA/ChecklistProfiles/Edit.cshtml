@using Drg.M3.Client.Models.QA.ChecklistProfiles
@model ChecklistProfileEditModel

@{
    var desc = ((ChecklistType)(ViewData["ChecklistType"] ?? ViewData.Model.ChecklistType)).ToStringDescription();
    ConfigureBootstrap4();
}

@using (Html.BeginFormCurrentAction(new { id = "frmEdit", @class = "medium " }))
{
    @Html.HiddenFor(x => x.Id)
    <div class="row">
        <div class="col-12 col-lg-6">
            <div class="card bs-callout bs-callout-primary">
                <h5>Properties</h5>

                @Html.EditorFor(x => x.Name)
                @Html.EditorFor(x => x.Jurisdiction)
                <div class="form-group has-label" style="">
                    <label>Organization</label>
                    <input class="form-control" value="@((ViewData["organization"] as Organization).Name)" readonly autocomplete="off">
                    @Html.HiddenFor(x => x.OrganizationId)
                </div>
                @Html.EditorFor(x => x.MediaId)
                @Html.EditorFor(x => x.Instructions)
                @Html.EditorFor(x => x.IsShared)
                @Html.EditorFor(x => x.RestartNumbering)
            </div>
        </div>
        <div class="col-12 col-lg-6">
            <div class="card bs-callout bs-callout-primary">
                <h5>Configurable Fields</h5>

                @Html.EditorFor(x => x.DateScheme)
                @Html.EditorFor(x => x.Building)
                @Html.EditorFor(x => x.Room)
                @Html.EditorFor(x => x.Location)
                @Html.EditorFor(x => x.Inspector)
                @Html.EditorFor(x => x.SubmissionDate)
                @Html.EditorFor(x => x.PocName)
                @Html.EditorFor(x => x.ContactInfo)
                @Html.EditorFor(x => x.EvaluatedItem)
                @Html.EditorFor(x => x.AdditionalComments)
                @Html.EditorFor(x => x.SignatureBlock)
            </div>
        </div>
    </div>
}

@section Scripts
{
    <script type="text/javascript">
        $(function () {
            $("select#organizationId").drillDownFilter();
        });
    </script>

}
