<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

<%grid.NeedDataSource += ViewData["statistics"] as EventHandler<GridEventArgs>; %>
<m3:Grid ID="grid" runat="server" Title="Media Statistics" RowClickUrl="~/QA/MediaProfile/{Id}">
    <Columns>
        <m3:BoundColumn DataField="Name" Caption="Media Name"/>
        <m3:BoundColumn DataField="Practices" />
        <m3:BoundColumn DataField="Aspects" />
        <m3:BoundColumn DataField="Assets" Caption="Equipment"/>
        <m3:BoundColumn DataField="Inspections" Caption="Checklists" />
        <m3:BoundColumn DataField="Findings" />
        <m3:BoundColumn DataField="Permits" />
        <m3:BoundColumn DataField="AuditItems" Caption="Checklist Items"/>
    </Columns>
</m3:Grid>
</asp:Content>
