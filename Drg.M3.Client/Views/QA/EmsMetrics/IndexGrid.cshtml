@{
    Layout = null;

    this.ConfigureBootstrap4();

    var settings = new GriddlySettings<Drg.M3.Domain.QA.EmsMetricsSnapshot>()
    {
        Title = "EMS Metrics",
        RowClass = x => x.IsFrozen ? "frozen" : x.Has<PERSON>ecentInternalAudit == false ? "missing" : ""
    };

    bool hasEditPermission = M3Context.Current.Permissions.HasPermission(typeof(Drg.M3.Client.Controllers.QA.EmsMetricsController), "Index", AccessType.Edit);

    var user = M3Context.Current.User;

    if (hasEditPermission)
    {
        settings.Column("", template: @<text>@if (user.Organization.ParentOrganization == null || (!item.IsOfficial & !item.Organization.IsOutside(user.Organization)))
        {<a href="javascript:editItem(@item.Id);"><i class="fa fa-edit"></i></a>}</text>, width: "16px");
    }

    settings.OrgColumn(x => x.Organization, filter:x=>x.FilterString())
    .Column(x => x.Date, format: "M/d/yyyy", defaultSort: SortDirection.Descending, filter: x=>x.FilterRange(FilterDataType.Date, captionPlural: ""))
    .Column(x => x.Internal_AuditChecklistScore, "% Internal Audit Checklist Completed Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.Internal_AuditChecklistScore)</text>, className:"internal")
    .Column(x => x.Internal_MajorFindingsClosedScore, "Major/Regulatory internal findings closed Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.Internal_MajorFindingsClosedScore)</text>, className:"internal")
    .Column(x => x.Internal_MinorFindingsClosedScore, "Minor/Policy internal findings closed Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.Internal_MinorFindingsClosedScore)</text>, className:"internal")
    .Column(x => x.Internal_FindingsOpenScore, "Internal findings open greater than 1 year Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.Internal_FindingsOpenScore)</text>, className:"internal")
    .Column(x => x.Internal_MilestonesOnTrackScore, "POAM/Milestone on track Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.Internal_MilestonesOnTrackScore)</text>, className:"internal")
    .Column(x => x.InternalScore, "Internal Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.InternalScore)</text>, className:"internal")
    .Column(x => x.External_FindingsScore, "External Audit Instance Findings Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.External_FindingsScore)</text>)
    .Column(x => x.External_CarryoverFindingsScore, "Previously Identified Findings Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.External_CarryoverFindingsScore)</text>)
    .Column(x => x.External_OpenFindingsScore, "Open External Audit Findings Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.External_OpenFindingsScore)</text>)
    .Column(x => x.ExternalScore, "External Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.ExternalScore)</text>)
    .Column(x => x.TotalScore, "Total Score", filter: x=>x.FilterRange(FilterDataType.Integer, captionPlural: ""), template:@<text>@RenderValue(item, x=>x.TotalScore)</text>)
    .Column(x=>x.Comments, "Comments", className:"mw-10", filter: x=>x.FilterString())
    .Column("", template: @<text>
        @if (item.Data != null) {
        <a href="@Url.Action("DownloadData", new { item.Id })"><i class="fa fa-file-excel"></i></a>
        } else{
        <a href="javascript:;" class="text-danger" data-toggle="tooltip" title="No data has been exported. Please ensure your template mappings are correctly configured!"><i class="fa fa-exclamation-triangle"></i></a>
        }
        <a href="@Url.Action("ExplainModal", new { item.Id })" class="ml-2" data-toggle="ajax-modal"><i class="fa fa-info-circle "></i></a>
    </text>, className:"text-center")
    ;
    settings.AddSearchButton();
    settings.AddPrintButton();

    if(ViewBag.Official == false && M3Context.Current.Organization.EnableEmsMetrics && M3Context.Current.Permissions.HasPermission(typeof(Drg.M3.Client.Controllers.QA.EmsMetricsController), "Index", AccessType.Add))
    {
        settings.Add(new GriddlyButton() { Text = "Create Snapshot", Argument = Url.Action("GenerateSandboxSnapshot"), Action=GriddlyButtonAction.Post, Icon="add" });
    }
}

<style>
    .griddly th{
        white-space:normal;
    }
</style>

@Html.Griddly(settings)

@using (Html.BeginComponentScript())
{
    <script>
        function editItem(id) {
            getModalContainer().loadAjaxFormModal("@Url.Action("EditModal")", { id: id });
        }
    </script>
}

@helper RenderValue(EmsMetricsSnapshot snapshot, Func<EmsMetricsSnapshot, int> getValue) 
{
    if (snapshot.Previous != null)
    {
        var lastVal = getValue(snapshot.Previous);
        var val = getValue(snapshot);
        <span class="@(lastVal>val?"text-danger text-bold" : lastVal<val?"text-success text-bold":"text-warning")">@val</span>
    }
    else
    {
        @getValue(snapshot)
    }
}