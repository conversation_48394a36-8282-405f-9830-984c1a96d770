<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage" %>
<%@ Import Namespace="Drg.M3.Domain.QA" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

<%
    pageButtons.GetButton("showshared").Visible = !ViewData.Bool("shared");
    pageButtons.GetButton("hideshared").Visible = ViewData.Bool("shared");

    foreach (var btn in grid.Buttons)
        btn.RequirePermission(AccessType.Add, typeof(ChecklistTemplate));
%>

<m3:PageButtonGroup ID="pageButtons" runat="server" Type="Output">
	<m3:PageButton Icon="check" Text="Show Shared" UniqueName="showshared" Action="~/QA/ChecklistTemplate/?shared=true"/> 
	<m3:PageButton Icon="sign_forbidden" Text="Show Mine" UniqueName="hideshared" Action="~/QA/ChecklistTemplate"/> 
</m3:PageButtonGroup>

<%
    grid.NeedDataSource += ViewData["templates"] as EventHandler<GridEventArgs>;
    grid.DefaultFilterValues = new NameValueCollection();
    grid.DefaultFilterValues["inactive"] = "false";
%>
<m3:Grid ID="grid" runat="server" Title="Checklist Templates" RowClickUrl="~/QA/ChecklistTemplate/Profile/{id}">
    <FilterForm>
        <div><%=grid.FilterTextBox("name", "Name") %></div>
        <div><%=grid.FilterTextBox("organization", "Organization") %></div>
        <%var types = new[]{new{Id="",Name=""}}.Union(typeof(ChecklistType).EnumToEnumerable().OfType<Object>());%>
        <div><%=grid.FilterDropDown("type", "Type", types)%></div>
        <div><%:Html.TextField("media", "Media")%></div>
        <div><%=grid.FilterTextBox("createdBy", "Created By") %></div>
        <div><%=grid.FilterBoolean("inactive", "Inactive")%></div>
    </FilterForm>
    <Buttons>
        <m3:ActionButton Action="~/QA/ChecklistTemplate/Edit?checklistType=0" 
            Icon="add" Text="New General Template"/>
        <m3:ActionButton Action="~/QA/ChecklistTemplate/Edit?checklistType=2" 
            Icon="add" Text="New Compliance Audit Template"/>
        <m3:ActionButton Action="~/QA/ChecklistTemplate/Edit?checklistType=3" 
            Icon="add" Text="New EMS Template"/>
    </Buttons>
    <Columns>
        <m3:BoundColumn DataField="Name" Caption="Checklist Template Name"/>
        <m3:OrgColumn />
        <m3:BoundColumn DataField="ChecklistType" Caption="Type"/>
        <m3:BoundColumn DataField="Media.Name" Caption="Media"/>
        <m3:TemplateColumn DataField="CreatedBy.w_EntityName" Caption="Created&nbsp;By">
            <Template><%#DataBinder.Eval(Container.DataItem, "CreatedBy") %></Template>
        </m3:TemplateColumn>
        <m3:BoundColumn DataField="DM" Caption="Last Updated"/>
        <m3:BoundColumn DataField="IsShared" Caption="Shared"/>
        <m3:BoolColumn DataField="IsInactive" Caption="Inactive"/>
    </Columns>
</m3:Grid>

</asp:Content>
