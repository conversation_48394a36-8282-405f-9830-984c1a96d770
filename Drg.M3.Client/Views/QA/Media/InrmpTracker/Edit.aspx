<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<Drg.M3.Domain.QA.Inrmp>" %>
<%@ Import Namespace="Drg.M3.Domain.QA" %>

<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
    <%
        var mp = ViewData["mediaProfile"] as Drg.M3.Domain.QA.MediaProfile;
        var vdd = new ViewDataDictionary<Drg.M3.Domain.QA.MediaProfile>();
        
        vdd["sel"] = "INRMP Tracker";
        vdd["sel2"] = "Profile";
        vdd["mp"] = mp;
        Html.RenderPartial("~/Views/QA/Media/tabs.ascx", mp, vdd);
        if (Model != null)
            Html.RenderPartial("~/Views/QA/Media/InrmpTracker/tabs.ascx", Model, vdd);
    %>

    <% using (Html.BeginForm("Edit", "InrmpTracker", new { id = Model != null ? Model.Id : (int?)null, mediaId = mp.Media.Id }, FormMethod.Post, new { id = "frmEdit", Class="medium"})) { %>
	
		<div class="frbody form">
			<div class="top"><span class="title"><%= Model == null ? "Add" : "Edit" %> INRMP</span></div>
            <div class="category">General Information:</div>
            <div class="group">
			    <div><%:Html.TextField("name", "Title") %></div>
                <div>
                    <%:Html.Label("orgId", "Organization") %>
                    <select name="orgId" id="orgId" rel="{action: '<%= ResolveUrl("~/Ajax/Organizations/") %>' }">
                        <% if (Model != null && Model.Organization != null) { %>
                        <option selected="selected" value="<%=Model.Organization.Id%>" 
                            parent="<%=(Model.Organization.ParentOrganization == null) ? "" : Model.Organization.ParentOrganization.Id.ToString() %>"
                            ><%= Model.Organization.Name %>></option>
                        <% } else { %>
                            <option selected="selected" value="<%=M3Context.Current.Organization.Id %>"
                                parent="<%=(M3Context.Current.Organization.ParentOrganization == null) ? "" : M3Context.Current.Organization.ParentOrganization.Id.ToString() %>"
                                ><%= M3Context.Current.Organization.Name %></option>
                        <% } %>
                    </select>
                </div>
                <div><%:Html.DdlField("status", "Status") %></div>
                <div><%:Html.DateField("date", "Date") %></div>
                <div><%:Html.DdlField("signedByNMFS", "Signed by NMFS") %></div>
                <div><%:Html.DateField("reviewOE", "Review O&E Date") %></div>
                <div><%:Html.DdlField("nepaType", "NEPA Type") %></div>
                <div><%:Html.DdlField("nepaComplete", "NEPA Complete") %></div>
                <div><%:Html.DateField("nepaCompletionDate", "NEPA Completion Date") %></div>                
                <div><%:Html.CheckBoxField("signedByState", "Signed By State") %></div>
                <div><%:Html.CheckBoxField("signedByUSFWS", "Signed By USFWS") %></div>
			</div>

			
		    <div class="errors"></div>
		    
		    <div class="buttons">
			    <input type="submit" value="Save INRMP" />
                <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="return cancel();">Cancel</a>
		    </div>
		</div>
		
	<% } %>
    
</asp:Content>
<asp:Content ContentPlaceHolderID="Scripts" runat="server">
<script type="text/javascript">
    $(function () {
        $("#orgId").drillDownFilter();
    });
</script>

</asp:Content>
