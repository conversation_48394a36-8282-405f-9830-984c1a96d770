<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<Drg.M3.Domain.QA.Inrmp>" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <% 
        var mp = ViewData["mediaProfile"] as Drg.M3.Domain.QA.MediaProfile;
        var vdd = new ViewDataDictionary();
        
        vdd["sel"] = "INRMP Tracker";
        vdd["sel2"] = "Projects";
        vdd["mp"] = mp;
        Html.RenderPartial("~/Views/QA/Media/tabs.ascx", mp, vdd);
        Html.RenderPartial("~/Views/QA/Media/InrmpTracker/tabs.ascx", Model, vdd);
        
        grdMain.NeedDataSource += ViewData["projects"] as EventHandler<GridEventArgs>;        
        grdMain.RowClickUrl = "~/QA/InrmpTracker/Project/{id}?mediaId=" + mp.Media.Id + "&inrmpId=" + Model.Id;
    %>
    <m3:Grid ID="grdMain" runat="server" Title="INRMP Projects">
    <Buttons>
    </Buttons>
    <Columns>
        <m3:BoundColumn DataField="InrmpProject.ProjectNumber" Caption="Project Number" />
        <m3:BoundColumn DataField="InrmpProject.Title" Caption="Title" />
        <m3:BoundColumn DataField="FiscalYear" Caption="Fiscal Year" />
        <m3:BoundColumn DataField="FundsObligated" Caption="$ Obligated" />
        <m3:BoundColumn DataField="FundsSpent" Caption="$ Spent" />
        <m3:BoundColumn DataField="MetGoals" Caption="Met Goals" />
        <m3:BoundColumn DataField="OnSchedule" Caption="On Schedule" />
        <m3:BoundColumn DataField="Status" Caption="Status" />
        <m3:BoundColumn DataField="EcosystemQuiz.Ecosystem" Caption="Ecosystem Benefited" />
    </Columns>
</m3:Grid>

</asp:Content>