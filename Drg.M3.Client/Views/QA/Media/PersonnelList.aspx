<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" AutoEventWireup="true" Inherits="Drg.M3.Client.AbstractViewPage<Drg.M3.Domain.Group>" %>

<asp:Content ContentPlaceHolderID="Styles" runat="server">
    <style type="text/css">
    #entityId
    {
        width:300px;
    }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">
	
    <%
        var mp = ViewData["mediaProfile"] as Drg.M3.Domain.QA.MediaProfile;
        var vdd = new ViewDataDictionary<Drg.M3.Domain.QA.MediaProfile>();

        (grdMembers.GetButton("addNew") as ActionButton).Action = "~/QA/Media/CreatePersonnel/" + mp.Media.Id;
        
        vdd["sel"] = "Personnel List";
        vdd["hasProgramStatistics"] = true;
        Html.RenderPartial("~/Views/QA/Media/tabs.ascx", mp, vdd);    
    %>
    
    <script runat="server">
        string getPersonOrCompanyEmail(Entity e)
        {
            string email = null;
            
            if (e is Person)
                email = e.w_DefaultEmailAddress;
            else 
                if (e.w_DefaultEmailAddress != null)
                    email = e.w_DefaultEmailAddress;
                else if ((e as Company).PrimaryPerson != null)
                    email = (e as Company).PrimaryPerson.w_DefaultEmailAddress;

            if (email == null)
                return null;
            else
                return "<a href=\"mailto:" + email + "\">" + email + "</a>";
        }
    </script>

    <% 
       grdMembers.NeedDataSource += ViewData["GroupsDataSource"] as EventHandler<GridEventArgs>;
       grdMembers.DefaultFilterValues = new NameValueCollection();
       grdMembers.DefaultFilterValues["isInactive"] = "false";
       (grdMembers.GetButton("task") as PostButton).Action = "~/QA/Media/TaskPersonnel/" + Model.Id;

       if (M3Context.Current.Organization.Key == null || (M3Context.Current.Organization.Key.Split('.').Length > 2 
           && M3Context.Current.Organization.OrganizationType != null && M3Context.Current.Organization.OrganizationType.ToString() != "Region"))
       {
           grdMembers.GetButton("add").Visible = false;
           grdMembers.GetButton("addNew").Visible = false;
           grdMembers.GetButton("delete").Visible = false;
           grdMembers.GetButton("toggleSecondary").Visible = false;
       }

       if (!mp.Media.Name.Contains("Natural Resources"))
           grdMembers.GetColumn("series").Visible = false;

    %>

	<m3:Grid ID="grdMembers" runat="server" KeyDataField="Id" Title="Personnel" SaveSort="false" QuickSearch="true" EnableMessaging="true" 
        EntityPath="Entity.Id" SortColumn="Entity.Organization" RowClickUrl="~/QA/Media/EditPersonnel/{Model:Id}?entityGroupId={id}">
	    <FilterForm>
            <%=grdMembers.FilterTextBox("quickSearch", "Name")%>
            <%=grdMembers.FilterTextBox("location", "Location") %>
            <%=grdMembers.FilterBoolean("isSecondary", "Secondary")%>
        </FilterForm>
        <Buttons>
		    <m3:TemplateButton UniqueName="add">
		        <Template>
		            <a href="<%=ResolveUrl("~/Resource/RequiresJavascript") %>" class="btn btn-default btn-sm" title="Add Existing" onclick="$(this).hide();$('#spAdd').show();$('#entityId').focus();return false;">
                        <%=Html.Icon("add")%><span>Add Existing</span>
                    </a>
		            <span id="spAdd" class="quickentry" style="display:none;">
		                <select name="users" id="userId" 
		                    rel="{ columns: [ 'Name', 'Organization', 'Region' ], width: '350px', outerWidth: '350px', isButton: false, buttonText: 'Add existing', action: '<%=ResolveUrl("~/Ajax/Users?select=1&oid=" + M3Context.Current.Organization.Id)%>' }">
		                </select>
                        <select name="series" id="seriesId">
                            <option></option>
                            <% foreach (var series in ViewData["series"] as IEnumerable<Reference>) { %>
                                <option value="<%= series.Id %>"><%= series.Name %></option>
                            <% } %>
                        </select>
                        <span style="border: 1px solid #dddddd; padding: 0.5em;">
                        Secondary Personnel?<input type="checkbox" id="isSecondary" style="width: 20px;" /></span>&nbsp;&nbsp;
		                <a href="<%=ResolveUrl("~/resource/requiresjavascript")%>" onclick="QuickAddSelectFilter('userId', '~/QA/Media/AddPersonnel/<%=Model.Id%>?isSecondary=' + $('#isSecondary').is(':checked') + '&seriesId=' + $('#seriesId').val() + '&userId=', grdMembers);return false;">
                            <%=Html.Icon("add")%>
                        </a>
                    </span>
		        </Template>
		    </m3:TemplateButton>
            <m3:ActionButton Action="~/QA/Media/CreatePersonnel/" UniqueName="addNew" Text="Add New" Icon="add" />
            <m3:AjaxButton Action="~/QA/Media/DeletePersonnel/" UniqueName="delete" Text="Delete Selected" Icon="delete" />
            <m3:AjaxButton Action="~/QA/Media/ToggleSecondary/" UniqueName="toggleSecondary" Text="Toggle Secondary" Icon="checks" />
            <m3:PostButton Action="~/QA/Media/TaskPersonnel/" UniqueName="task" Text="Create Task" Icon="money_envelope_add" NoIdsRequired="true" />
        </Buttons>
	    <Columns>
	        <m3:SelectColumn />
            <m3:BoundColumn Caption="Name" DataField="Entity.SortName" />
            <%--<m3:TemplateColumn Caption="Name" DataField="Entity.SortName">
            <Template>
                <a href="<%= ResolveUrl("~/Contacts/Profile/") %><%# DataBinder.Eval(Container.DataItem, "Entity.Id") %>">
                    <%# (DataBinder.Eval(Container.DataItem, "Entity.Name") != null) ? DataBinder.Eval(Container.DataItem, "Entity.Name") : DataBinder.Eval(Container.DataItem, "Entity.User.Name")%><%# ((bool)(DataBinder.Eval(Container.DataItem, "Entity.IsInactive"))) ? " (Inactive)" : "" %>
                </a>
            </Template>
            </m3:TemplateColumn>--%>
			<m3:BoundColumn DataField="Entity.Organization" Caption="Location" />
			<m3:TemplateColumn Caption="Email" DataField="Entity.w_DefaultEmailAddress" >
			    <Template>
                    <%# getPersonOrCompanyEmail((Container.DataItem as EntityGroup).Entity) %>
			    </Template>
			</m3:TemplateColumn>
			<m3:BoundColumn DataField="Entity.w_DefaultPhone" Caption="Phone" />
            <m3:BoundColumn DataField="ConservationSeries" Caption="Series" UniqueName="series" />
            <m3:BoolColumn DataField="IsSecondary" Caption="Secondary" />
	    </Columns>
	</m3:Grid>
    
</asp:Content>

<asp:Content ID="Content1" ContentPlaceHolderID="Scripts" runat="server">
    <script type="text/javascript">
        $(function () {
            $("#userId, #seriesId").selectfilter();
        });

        var removeUrl = "~/Contacts/Group/RemoveMembers/<%=ViewData.Model.Id%>";
    </script>
</asp:Content>
