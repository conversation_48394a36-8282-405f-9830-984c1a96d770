<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<Drg.M3.Domain.QA.TESpecies>" %>
<%@ Import Namespace="Drg.Core" %>

<asp:Content ID="Content2" ContentPlaceHolderID="Styles" runat="server">
    <%Html.RenderPartial("~/Views/Quiz/QuizCss.ascx"); %>
</asp:Content>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <style>
        .dashboardbox table:not(.simplegrid) th { min-width: 10px; }
        .linkBox a 
        {
            float:right;
            color: Red;
        }
        #linksmasonry h5  
        {
            margin: 2px 0;
        }
    </style>
    <link href="<%= Url.ResolveCdnUrl("~/content/css/lightbox.css") %>" rel="Stylesheet" />

    <m3:StartupScript runat="server" ID="linkscript">
    <script type="text/javascript" language="javascript">
        function AddLink() {
            $("#addLink").slideToggle("fast");
            var ht = $("#linksmasonry").height();
            if ($("#addLink").is(".expanded")) {
                $("#linksmasonry").height(ht - 140);
                $(".topoverview").masonry("reload");
                $("#addLink").removeClass("expanded");
                $('#addLinkBox').show();
            } else {
                $("#linksmasonry").height(ht + 140);
                $(".topoverview").masonry("reload");
                $("#addLink").addClass("expanded");
                $("#addLinkBox").hide();
            }
        }
        function RemoveLink(url) {
            if (confirm("Remove link from this record?")) {
                $.post(ResolveUrl(url), function (result) {
                    if (result != null && result != true) {
                        alert(result);
                    } else {
                        window.location = window.location;
                    }
                }, 'json');
            }
        }
        function CleanLink(text) {
            return text.replace("?", "^").replace("&", "$");
        }
    </script>
    </m3:StartupScript>

    <%
        var mp = ViewData["mediaProfile"] as Drg.M3.Domain.QA.MediaProfile;
        var vdd = new ViewDataDictionary<Drg.M3.Domain.QA.MediaProfile>();
        
        vdd["sel"] = "Species Tracker";
        vdd["sel2"] = "Profile";
        vdd["mp"] = mp;
        Html.RenderPartial("~/Views/QA/Media/tabs.ascx", mp, vdd);
        Html.RenderPartial("~/Views/QA/Media/SpeciesTracker/tabs.ascx", Model, vdd);

        pgbuttons.GetButton("edit").Action = "~/QA/SpeciesTracker/Edit/" + Model.Id + "?mediaId=" + mp.Media.Id;
        pgbuttons.GetButton("delete").Action = "~/QA/SpeciesTracker/Delete/" + Model.Id + "?mediaId=" + mp.Media.Id;
        pgbuttons.GetButton("deactivate").Action = "~/QA/SpeciesTracker/Deactivate/" + Model.Id + "?mediaId=" + mp.Media.Id;
        pgbuttons.GetButton("reactivate").Action = "~/QA/SpeciesTracker/Reactivate/" + Model.Id + "?mediaId=" + mp.Media.Id;

        pgbuttons.GetButton("delete").Visible = Model.IsInactive;
        pgbuttons.GetButton("reactivate").Visible = Model.IsInactive;
        pgbuttons.GetButton("deactivate").Visible = !Model.IsInactive;
        
        UrlReport expenditure = new UrlReport();
        expenditure.Url = "~/QA/SpeciesTracker/SpeciesExpenditure/" + Model.Id;
        expenditure.Title = Model.Name;
        expenditure.Category = "Expenditure";
        UrlReport expenditure2 = new UrlReport();
        expenditure2.Url = "~/QA/SpeciesTracker/SpeciesExpenditureFY/" + Model.Id;
        expenditure2.Title = Model.Name + " (Fiscal Year)";
        expenditure2.Category = "Expenditure";
        UrlReport taxGroupExpenditure = new UrlReport();
        taxGroupExpenditure.Url = "~/QA/SpeciesTracker/TaxGroupExpenditure/" + Model.Group.Id;
        taxGroupExpenditure.Title = Model.Group.Name;
        taxGroupExpenditure.Category = "Expenditure";
        
        (pgbuttons.GetButton("print") as PrintButton).PageReports.AddRange(new[] { expenditure, taxGroupExpenditure, expenditure2 });
    %>

    <m3:PageButtonGroup ID="pgbuttons" runat="server">
        <m3:PageButton Action="~/QA/SpeciesTracker/Deactivate/" Icon="arrow_down_red" UniqueName="deactivate" Text="Deactivate" ConfirmMessage="Are you sure you want to deactivate this species?" />
        <m3:PageButton Action="~/QA/SpeciesTracker/Reactivate/" Icon="arrow_up_green" UniqueName="reactivate" Text="Reactivate" />
        <m3:PageButton Action="~/QA/SpeciesTracker/Delete/" Icon="delete" UniqueName="delete" Text="Delete" ConfirmMessage="Are you sure you want to delete this species?" />
        <m3:PageButton Action="~/QA/Media/EditSpecies/" Icon="edit" Text="Edit" UniqueName="edit" />
        <m3:PrintButton UniqueName="print" />
    </m3:PageButtonGroup>

    <div class="topoverview masonryboxes">

    <div class="dashboardbox">
        <div>
            <div class="title">General Information</div>        
            <div class="infobox">
                <table cellspacing="0" >
                    <colgroup>
                        <col width="140px;" />
                        <col />
                    </colgroup>
                    <tbody>
                        <tr>
                            <th>Scientific Name:</th>
                            <td><%= Model.ScientificName %></td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td><%= (Model.Status != null) ? Model.Status.ToStringDescription() : "" %></td>
                        </tr>
                        <tr>
                            <th>Tax Group:</th>
                            <td><%= Model.Group != null ? Model.Group.Name : "" %></td>
                        </tr>
		                <tr>
		                    <th>CH Designated:</th> 
		                    <td><%= (Model.CriticalHabitat == true) ? "Yes" : "No" %></td>
		                </tr>
                        <tr>
                            <th>CH Designated (Navy):</th>
                            <td><%= (Model.CHDesignatedNavy == true) ? "Yes" : "No" %></td>
                        </tr>
		                <tr>
                            <th>Exclusion:</th> 
                            <td><%= Model.Exclusion %></td>
		                </tr>
                        <tr>
                            <th>Exemption:</th> 
                            <td><%= Model.Exemption %></td>
		                </tr>
                        <tr>
                            <th>CH Exemption (INRMP):</th>
                            <td><%= (Model.INRMPExemption == true) ? "Yes" : "No" %></td>
                        </tr>
                        <tr>
                            <th>CH Exemption (Mission):</th>
                            <td><%= (Model.MissionExemption == true) ? "Yes" : "No" %></td>
                        </tr>
                        <%--<tr>
                            <th>Exemption Type:</th> 
                            <td><%= Model.ExemptionType != null ? Model.ExemptionType.Name : "" %></td>
		                </tr>--%>
                        <tr>
                            <th>Jurisdiction:</th>
                            <td><%= (Model.Jurisdiction != null) ? Model.Jurisdiction.ToStringDescription() : "" %></td>
                        </tr>
                        <tr>
                            <th>Host Country:</th>
                            <td><%= Model.HostCountry %></td>
                        </tr>
                        <tr>
                            <th>Where Listed:</th>
                            <td><%= Model.WhereListed %></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <% grdSpecialAreas.NeedDataSource += ViewData["dcas"] as EventHandler<GridEventArgs>; %>
    <div class="dashboardbox" style="width: 380px; padding: 10px">
        <m3:Grid runat="server" ID="grdSpecialAreas" EnableFilter="false" EnablePrint="false" Title="Location Information"
            PageSize="10" OverrideUserPageSize="true">
            <Columns>
                <m3:BoundColumn Caption="Installation Name" DataField="Organization.Name"></m3:BoundColumn>
                <m3:BoundColumn Caption="Site Name" DataField="SpecialArea.Name"></m3:BoundColumn>
            </Columns>
        </m3:Grid>
    </div>

    <% 
        grdINRMPS.NeedDataSource += ViewData["inrmps"] as EventHandler<GridEventArgs>;
        grdINRMPS.RowClickUrl = "~/QA/InrmpTracker/Profile/{id}?mediaId=" + mp.Media.Id;
    %>
    <div class="dashboardbox" style="width: 380px; padding: 10px">
        <m3:Grid runat="server" ID="grdINRMPS" EnableFilter="false" EnablePrint="false" Title="INRMP Information"
            RowClickUrl="~/QA/InrmpTracker/Profile/{id}" PageSize="10" OverrideUserPageSize="true">
            <Columns>
                <m3:BoundColumn DataField="Name" />
                <m3:BoundColumn DataField="Date" DataType="Date" />
            </Columns>
        </m3:Grid>
    </div>

    <div class="dashboardbox">
        <div>
            <div class="title">Images</div>
            <div class="infobox" style="height: 110px; display: block; overflow-y: scroll">
                <% foreach (var image in Model.Documents.Where(w => w.ReviewDate != null && w.DefaultFile != null && 
                    (new[] { ".gif", ".jpeg", ".jpg", ".png", ".bmp" }).Contains(w.DefaultFile.OriginalFileName.Substring(w.DefaultFile.OriginalFileName.LastIndexOf('.'))))) { %>
                    <a href="<%= ResolveUrl("~/Resource/Thumbnail/" + image.DefaultFile.Id + "?width=500&height=500") %>" rel="lightbox">
                        <img src="<%= ResolveUrl("~/Resource/Thumbnail/" + image.DefaultFile.Id + "?width=100&height=100") %>" style="margin-bottom: 10px;" />
                    </a>
                <% } %>
            </div>
            <% if (M3Context.Current.User.Organization.Name == "Navy" || M3Context.Current.User.Organization == new OrganizationDao(Session).GetRootOrg()) { %>
            <div><a href="javascript:ManageImages()">Manage Images (<%= Model.Documents.Where(w => w.DefaultFile != null && 
                    (new[] { ".gif", ".jpeg", ".jpg", ".png", ".bmp" }).Contains(w.DefaultFile.OriginalFileName.Substring(w.DefaultFile.OriginalFileName.LastIndexOf('.')))).Count() %>)</a></div>
            <% } %>
        </div>
    </div>

    <div class="dashboardbox">
        <div>
            <div class="title">Description and Life History</div>        
            <div class="infobox">
                <table cellspacing="0" >
                    <tbody>
                        <tr>
                            <th>Species Description:</th>
                        </tr>
                        <tr>
                            <td style="padding-left: 10px"><%= Model.Description %></td>
                        </tr>
                        <tr>
                            <th>Life History:</th>
                        </tr>
                        <tr>
                            <td style="padding-left: 10px"><%= Model.LifeHistory %></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="dashboardbox" id="linksmasonry">
        <div>
            <div class="title">Related Links</div>
            <div class="infobox">
                <table cellspacing="0" style="width:100%">
                    <colgroup>
                        <col width="80px;" />
                        <col />
                    </colgroup>
                    <tbody>
                        <tr>
		                    <th>Links:</th>
                            <td>
                                <% foreach (var link in Model.Links) { %>                                    
                                    <a href="<%= link.Link %>" target="_blank"><%= link.Text %></a>
                                    &nbsp;&nbsp;&nbsp;<span class="linkBox"><a href="javascript:void(0);" onclick="RemoveLink('~/QA/SpeciesTracker/DeleteLink/<%= link.Id %>')">x</a></span><br />
                                <% } %>
                                <span class="hr"></span>
                                <a class="linkBox" id="addLinkBox" href="javascript:AddLink();">Add Link</a>
                                <div style="display:none;" id="addLink">
                                    <div style="text-align: right">
                                    Text: 
                                    <input type="text" style="width: 200px" id="linkText" /><br /><br />
                                    Url:
                                    <input type="text" style="width: 200px" id="linkUrl" />
                                    </div><br />
                                    <a href="javascript:void(0);" onclick="QuickAddInput('~/QA/SpeciesTracker/AddLink/<%=ViewData.Model.Id%>?linkText=' + CleanLink($('#linkText').val()) + '&linkUrl=' + CleanLink($('#linkUrl').val()));"><%=Html.Icon("add")%> Add Link</a> 
                                    <span style="float:right;margin-right:10px;"><a href="javascript:AddLink();">Cancel</a></span>
                                    <span class="hr"></span>
                                </div>
                            </td>
		                </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <% grdDocuments.NeedDataSource += ViewData["documents"] as EventHandler<GridEventArgs>;
       (grdDocuments.GetButton("add") as ActionButton).Action = "~/QA/SpeciesTracker/AddDocument/" + Model.Id + "?mediaId=" + mp.Media.Id; %>
    <%--<div class="dashboardbox">
        <div>
            <div class="title">Related Documents</div>
            <div class="infobox">--%>
            <div class="dashboardbox" style="width: 380px; padding: 10px">
                <m3:Grid runat="server" ID="grdDocuments" EnableFilter="false" EnablePrint="false" Title="Related Documents"
                    PageSize="10" OverrideUserPageSize="true">
                    <Buttons>                    
                        <m3:ActionButton Icon="add" UniqueName="add" Text="Add" />
                        <m3:AjaxButton Action="~/QA/SpeciesTracker/DeleteDocuments/" UniqueName="delete" Icon="delete" Text="Delete Selected" />
                    </Buttons>
                    <Columns>
                        <m3:SelectColumn />
                        <m3:TemplateColumn Caption="Name" DataField="Name">
                            <Template>
                                <a href="<%= ResolveUrl("~/Resource/File/") %><%# DataBinder.Eval(Container.DataItem, "DefaultFile.Id") %>">
                                    <%# DataBinder.Eval(Container.DataItem, "Name")%>
                                </a>
                            </Template>
                        </m3:TemplateColumn>
                    </Columns>
                </m3:Grid>
            </div>
            <%--</div>
        </div>
    </div> --%>
    </div>

    <div id="imagesDialog" style="display:none;overflow:hidden;">
        <iframe id="imagesIframe" width="730" height="520" marginWidth="0" marginHeight="0" frameBorder="0" scrolling="auto" title="Manage Images"></iframe>
    </div>

    <m3:StartupScript runat="server">
        <%=System.Web.Optimization.Scripts.Render("~/scripts/jqueryui.js") %>
        <script type="text/javascript" src="<%=Url.ResolveCdnUrl("~/content/js/lightbox-2.6.min.js") %>"></script>
        <script>
            function ManageImages() {
                $("#imagesDialog").dialog("open");
                $("#imagesIframe").attr("src", ResolveUrl("~/QA/SpeciesTracker/ManageImages/<%= Model.Id %>"));
            }
            $(function () {
                $("#imagesDialog").dialog({
                    autoOpen: false,
                    modal: true,
                    resizable: false,
                    title: "Manage Images",
                    width: 740, height: 625,
                    buttons: {
                        "Close Dialog": function () {
                            $(this).dialog("close");
                            window.location = window.location;
                        }
                    }
                });
            });
        </script>
    </m3:StartupScript>

</asp:Content>