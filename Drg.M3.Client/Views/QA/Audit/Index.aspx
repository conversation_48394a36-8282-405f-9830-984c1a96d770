<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage" %>
<%@ Import Namespace="Drg.M3.Domain.QA" %>
<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

<%
    grid.NeedDataSource += ViewData["audits"] as EventHandler<GridEventArgs>;
    grid.GetButton("new").RequirePermission(AccessType.Add, typeof(Audit));
    grid.DefaultFilterValues = new NameValueCollection();
    grid.DefaultFilterValues["closedAndNoOpenFindings"] = "false";
    //grid.DefaultFilterValues["IsCurrent"] = "true";

    grid.GetButton("capaSpreadsheet").Visible = M3Context.Current.Organization.EvaluateEnableCAPASpreadsheetRecursive();

    grid.GetGridReport("cnrmaCapaTracker").Visible = M3Context.Current.Organization.Id == 48 || M3Context.Current.Organization.Key.Contains(".48."); //Magic number for CNRMA
    grid.GetButton("cnrmaCapaTracker").Visible = M3Context.Current.Organization.Id == 48 || M3Context.Current.Organization.Key.Contains(".48."); //Magic number for CNRMA

    if (ViewBag.ShowDeleted == true)
    {
        foreach (var btn in grid.Buttons)
        {
            btn.Visible = false;
        }
        grid.RowClickUrl = null;
        grid.GetButton("restore").Visible = true;
    }
%>

<m3:PageButtonGroup ID="pageButtons" runat="server" Type="Output">
    <m3:PrintButton OtherReports="Navy External Audit Dashboard (All audits)|Main|~/QA/Audit/NavyExternalAuditDashboard,Navy Internal Audit Dashboard (All audits)|Main|~/QA/Audit/NavyInternalAuditDashboard,PWD Dashboard for Navy External Audits (All audits)|Main|~/QA/Audit/PWDExternalAuditDashboard?mostRecentOnly=true,PWD Dashboard for Navy External Audits (All audits) - Raw Data|Main|~/QA/Audit/PWDExternalAuditDashboardExport?mostRecentOnly=true,Internal Audit Review (Current FY)|Main|~/QA/Audit/InternalAuditReviewExport" />
</m3:PageButtonGroup>

<m3:Grid ID="grid" runat="server" Title="Audits/Inspections" RowClickUrl="~/QA/Audit/Profile/{id}" QuickSearch="true">
    <FilterForm>
        <%= grid.FilterTextBox("quickSearch", "Search") %>        
        <%= grid.FilterBoolean("IsCurrent", "Current (within 30 days)") %>
        <%= grid.FilterTextBox("name", "Title") %>
        <%= grid.FilterTextBox("organization", "Organization") %>
        <%= grid.FilterDropDown("auditType", "Type", ViewData["auditTypes"] as IEnumerable<Reference>) %>
        <%= grid.FilterTextBox("leadAuditor", "Lead Auditor") %>
        <%= grid.FilterDateRange("startDateFrom", "startDateTo", "Start Date") %>
        <%= grid.FilterDateRange("endDateFrom", "endDateTo", "End Date") %>
        <%= grid.FilterBoolean("isExternal", "External?") %>
        <%= grid.FilterBoolean("isDraft", "Draft") %>
        <%= grid.FilterBoolean("isRecurring", "Recurring") %>
        <%= grid.FilterBoolean("IsInactive", "Inactive (Closed)") %>
        <%= grid.FilterBoolean("closedAndNoOpenFindings", "Closed & No Open Findings") %>
        <%if(M3Context.Current.Organization.EvaluateEnableCAPASpreadsheetRecursive()){ %>
        <%= grid.FilterBoolean("SimplePoams", "Has CAPA Spreadsheet") %>
        <%} %>
    </FilterForm>
    <Buttons>
        <m3:ActionButton Action="~/QA/Audit/Create" 
            Icon="add" Text="New Audit/Inspection" UniqueName="new"/>
        <m3:ActionButton Action="~/QA/Audit/EditRegulatoryInspection" 
            Icon="add" Text="New Regulatory Performed Inspection" UniqueName="newRegulatory"/>
        <m3:PostButton Action="~/QA/Audit/NavyExternalAuditDashboard" 
            Icon="print" Text="Navy External Audit Dashboard (Selected audits)"/>
        <m3:PostButton Action="~/QA/Audit/NavyInternalAuditDashboard" 
            Icon="print" Text="Navy Internal Audit Dashboard (Selected audits)"/>
        <m3:PostButton Action="~/QA/Audit/PWDExternalAuditDashboard" 
            Icon="print" Text="PWD Dashboard for Navy External Audits (Selected audits)"/>
        <m3:PostButton Action="~/QA/Audit/PWDExternalAuditDashboardExport" 
            Icon="print" Text="PWD - Raw Data"/>
        <m3:PostButton Action="~/QA/Audit/SimpleAuditPoams" 
            Icon="view" Text="CAPA Spreadsheet" UniqueName="capaSpreadsheet"/>
        <m3:PostButton Action="~/QA/Audit/PoamReport?isCNRMA=true" 
            Icon="print" Text="CNRMA Audit Report (Selected audits)" UniqueName="cnrmaCapaTracker"/>
        <m3:PostButton Action="~/QA/Audit/RestoreDeleted" Visible="false"
            Icon="undo" Text="Restore Selected Audit" UniqueName="restore" ConfirmMessage="Are you sure you want to restore the selected audit?"/>
    </Buttons>
    <Columns>
        <m3:SelectColumn />
        <m3:BoundColumn DataField="Name" Caption="Title" />
        <m3:OrgColumn />
        <m3:BoundColumn DataField="AuditType.Name" Caption="Type" />
        <m3:BoundColumn DataField="LeadAuditor.Entity.Name" Caption="Lead Auditor"/>
        <m3:BoundColumn DataField="StartDate" Caption="Start Date"/>
        <m3:BoundColumn DataField="EndDate" Caption="End Date"/>
        <m3:TemplateColumn Caption="Evaluation Types" >
            <Template>
                <%#(Container.DataItem as Audit).EvaluationTypes.ConcatToString() %>
            </Template>
        </m3:TemplateColumn>
        <m3:TemplateColumn DataField="IsExternal" Caption="Int/Ext">
            <Template>
                <%#(Container.DataItem as Audit).InternalExternal %>
            </Template>
        </m3:TemplateColumn>
        <m3:BoundColumn DataField="IsDraft" Caption="Draft" />
        <m3:BoundColumn DataField="IsRecurring" Caption="Recurring" />
        <m3:BoundColumn DataField="Closed" Caption="Closed" />
        <m3:BoundColumn DataField="w_OpenFindings" Caption="# Open Findings" />
     <%--   <m3:TemplateColumn Caption="# Open Findings">
            <Template>
                <%#(Container.DataItem as Audit).RelatedFindings.Where(x=>!x.IsObservation && x.Status!=FindingStatus.Draft && x.Status!=FindingStatus.Closed).Count() %>
            </Template>
        </m3:TemplateColumn>--%>
    </Columns>
    <GridReports>
        <m3:UrlReport Title="POAM Report" Url="~/QA/Audit/PoamReport"/>
        <m3:UrlReport Title="CNRMA CAPA Tracker Report" Url="~/QA/Audit/PoamReport?isCNRMA=true" UniqueName="cnrmaCapaTracker"/>
    </GridReports>
</m3:Grid>

</asp:Content>
