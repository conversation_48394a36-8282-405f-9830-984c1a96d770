<%@ Page Title="" Language="C#" MasterPageFile="~/Views/Shared/Site.Master" Inherits="Drg.M3.Client.AbstractViewPage<Audit>" %>
<%@ Import Namespace="Drg.M3.Domain.QA" %>

<asp:Content ID="Content1" ContentPlaceHolderID="MainContent" runat="server">

    <% using (Html.BeginFormCurrentAction(new { id = "frmSubmit", Class="medium"})) { %>
	
		<div class="frbody form">
			<div class="top">Submit Audit</div>
			
			<div class="group">
				<div>
                    <div class="info">
                        <b>Note:</b> 
                        After submitting, the following actions will occur:  
                        <ol>
                            <li>Checklists will be assigned to users and can be accessed through Home -> My Tasks, Audit/Inspections -> My Checklists, and on the Audit/Inspection profile, </li>
                            <li>On the Audit/Inspection profile, the "Users view/fill out X Checklist Items" under Related Items will be populated,</li>
                            <li>On the Audit/Inspection profile, the Finding table's columns related to draft Findings will be replaced with columns more appropriate to tracking the Finding progress.</li>
                        </ol>
                        Even after submitting, most audit/inspection fields can be updated, including Checklist Template(s), Audit/Inspection Personnel, and Documents.  Fields related to the audit/inspection recurrence cannot be updated.
				    </div>
				</div>
                
                <div style="margin-top:20px"><div class="info"><i>This operation may take a while. Please be patient.</i></div></div>
			</div>
			
		    <div class="errors"></div>
		    
		    <div class="buttons">
			    <input type="submit" value="Submit" />
                <a href="<%=ResolveUrl("~/Resource/RequiresJavascript")%>" onclick="return cancel();">Cancel</a>
		    </div>
		</div>
		
	<% } %>
    
</asp:Content>
