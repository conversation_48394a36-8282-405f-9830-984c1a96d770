@model Drg.M3.Client.Models.Reporting.Queries.MoveQueryModel
@{
    ConfigureBootstrap4();
}

<div class="alert alert-warning">
    This will move the audit and related findings/observations to a new organization. Any other related data will not be moved.
</div>

@using (Html.BeginFormEx())
{
    <div class="bs-callout bs-callout-primary form-layout">
        @Html.DropDownEditorFor(x => x.OrganizationId, Url.Action("LookupOrganization", "Ajax", new { area = "" }), showSubtext: true)
    </div>

    @Html.FormButtons("Move Audit")
}