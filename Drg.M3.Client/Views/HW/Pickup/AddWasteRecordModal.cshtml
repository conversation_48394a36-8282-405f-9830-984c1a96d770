@model EditWasteRecordModel

@{ 
    ConfigureBootstrap4();
    var org = M3Context.Current.Organization;
}

<div class="modal fade modal-confirm-close" role="dialog" aria-hidden="true" id="add-wr-modal">
    <div class="modal-dialog modal-xl modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <h5>New Waste Record</h5>
                <button type="button" class="btn btn-sm btn-link" id="advanced-mode">Advanced Mode</button>
            </div>
            @using (Html.BeginFormEx(includeValidationSummary: false, htmlAttributes: new { id = "main-form", @class="ajax-form" }))
            {
            <div class="modal-body">
                @Html.ValidationSummary(false, null, new { @class = "alert alert-danger" })

                <div class="@Html.IfBrowser(Browser.InternetExplorer, "form-row", "card-columns card-columns-lg-2")">

                    @if (Html.IsBrowser(Browser.InternetExplorer)) {
                    @:<div class="col-lg-6">
                    }
                    @Html.Partial("../WasteRecord/Partials/GeneralInfo")
                    @Html.Partial("../WasteRecord/Partials/WasteInfo")
                    @Html.Partial("../WasteRecord/Partials/OtherInfo")
                    @Html.Partial("../WasteRecord/Partials/ContainerInfo")

                    @if (Html.IsBrowser(Browser.InternetExplorer)) {
                    @:</div>
                    @:<div class="col-lg-6">
                    }

                    @Html.Partial("../WasteRecord/Partials/StorageInfo")

                    @if (org.BillingType == BillingType.WorkingCapital)
                    {
                        @Html.Partial("../WasteRecord/Partials/Billing")
                    }

                    @Html.Partial("../WasteRecord/Partials/RcraInfo")
                    @Html.Partial("../WasteRecord/Partials/Disposition")
                    @Html.Partial("../WasteRecord/Partials/ShippingInfo")

                    @if (Html.IsBrowser(Browser.InternetExplorer)) {
                    @:</div>
                    }
                </div>
                <div class="form-row">
                    <div class="col-12">
                        @Html.Partial("../WasteRecord/Partials/Composition")
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <button class="btn btn-link" data-dismiss="modal" aria-hidden="true">Cancel</button>
                <button class="btn btn-primary" type="submit">Save</button>
            </div>
            }
        </div>
    </div>
</div>

<script>
    $(function () {
        var modal = $("#add-wr-modal");

        $("#advanced-mode", modal).click(function () {
            $(".hide-pickup, .hide-pickup-containerized", modal).addClass("advanced-mode").removeClass("hide-pickup").removeClass("hide-pickup-containerized");
            $(this).hide();
        });

    });
</script>

@Html.ComponentScripts()