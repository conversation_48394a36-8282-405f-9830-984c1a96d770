
@model EditWasteRecordModel

@{
    ConfigureBootstrap4();

    var org = Model.Record == null ? M3Context.Current.Organization : Model.Record.Organization;
}

@section Buttons{
@Html.Partial("~/Views/HW/WasteRecord/ButtonsPartial.cshtml", new ViewDataDictionary(Model.Record))
}

@if (Model.Record?.RemovalDate != null)
{
<div class="alert alert-warning">
    <b>This is a legal record.</b> This item is a legal record of wastes that have been manifested and should not be edited.
</div>
}

<div id="dropZone" class="qq-upload-extra-drop-area dropzone" qq-hide-dropzone><div>Drop files here to upload.</div></div>

@using (Html.BeginFormEx(htmlAttributes: new { id = "main-form" }, includeValidationSummary: !Model.HasWarning))
{
    if (Model.HasWarning)
    {
    <div class="alert alert-danger">
        @Html.ValidationSummary(false, null)
        @Html.EditorFor(x => x.OverrideWarning)
    </div>
    }

    <div class="@Html.IfBrowser(Browser.InternetExplorer, "form-row", "card-columns card-columns-lg-2")">

        @if (Html.IsBrowser(Browser.InternetExplorer)) {
        @:<div class="col-lg-6">
        }
        @Html.Partial("Partials/GeneralInfo")
        @Html.Partial("Partials/WasteInfo")
        @Html.Partial("Partials/OtherInfo")
        @Html.Partial("Partials/ContainerInfo")

        @if (Html.IsBrowser(Browser.InternetExplorer)) {
        @:</div>
        @:<div class="col-lg-6">
        }

        @Html.Partial("Partials/StorageInfo")

        @if (org.BillingType == BillingType.WorkingCapital)
        {
            @Html.Partial("Partials/Billing")
        }

        @Html.Partial("Partials/RcraInfo")
        @Html.Partial("Partials/Disposition")
        @Html.Partial("Partials/ShippingInfo")

        @if (Html.IsBrowser(Browser.InternetExplorer)) {
        @:</div>
        }
    </div>
    <div class="form-row">
        <div class="col-12 col-xl-6">
            @Html.Partial("Partials/Composition")
        </div>
    </div>

    <div class="form-footer form-row">
        <div class="col-auto">
            <input class="btn btn-primary" type="submit" name="button" value="Save" />
            <input class="btn btn-primary" type="submit" name="button" value="Save/Close" />
            <button type="button" class="btn btn-link" onclick="javascript:history.go(-1)">Cancel</button>
        </div>
        <div class="col"></div>
        <div class="col-auto">
            <input class="btn btn-secondary" type="submit" name="button" value="Save/Clone Customer" />
            @if (Model.Record?.Form1348 == null)
            {
                <input id="form1348-button" class="btn btn-secondary" type="submit" name="button" value="Save/Create 1348" 
                       style="@(Model.DispositionType==null || Model.SignOnly || !((Model.AccumulationStartDate != null && Model.StorageLocationId != null) || Model.ContainerIssue)?"display:none":null)" />
            }
            @if (Model.Record?.Manifest == null)
            {
                <input id="manifest-button" class="btn btn-secondary" type="submit" name="button" value="Save/Create Manifest" style="@(Model.DispositionType==null?"display:none":null)" />
            }
            <input class="btn btn-secondary" type="submit" name="button" value="Save/New" />
        </div>
    </div>
}

@section Scripts
{
    <script>
    var startingValues;
    $(function () {
        startingValues = $("#main-form").serialize();

        $(window).bind("beforeunload", function (e) {
            var currentValues = $("#main-form").serialize();
            var message = "This action will discard your unsaved changes."
            e = e || window.event;

            if (startingValues != currentValues) {
                // For IE and Firefox
                if (e) {
                    e.returnValue = message;
                }
                // For Safari
                return message;
            }
        });
        $("#main-form").submit(function () { $(window).unbind("beforeunload"); });

        $("#@Html.IdFor(x=>x.DispositionType)").change(function () {
            $("#manifest-button").toggle(!!$(this).val());
        });

        $("#@Html.IdFor(x=>x.DispositionType), #@Html.IdFor(x=>x.SignOnly), #@Html.IdFor(x=>x.AccumulationStartDate), #@Html.IdFor(x=>x.StorageLocationId), #@Html.IdFor(x=>x.ContainerIssue)").change(function () {
            $("#form1348-button").toggle(
                !!$("#@Html.IdFor(x=>x.DispositionType)").val()
                && !$("#@Html.IdFor(x=>x.SignOnly)").is(":checked")
                && 
                (
                    (
                        $("#@Html.IdFor(x=>x.AccumulationStartDate)").val()
                        && $("#@Html.IdFor(x=>x.StorageLocationId)").val()
                    )
                    || 
                    $("#@Html.IdFor(x=>x.ContainerIssue)").is(":checked")
                )
            );
        });
    });
    </script>
}

@section Sidebar
{
    @if (Model.Record != null)
    {
    @Html.Partial("../WasteRecord/RelatedItemsSidebar", Model.Record)
    }
}