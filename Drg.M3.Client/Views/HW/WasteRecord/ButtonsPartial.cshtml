@model WasteRecord

@{
    ConfigureBootstrap4();

    List<GriddlyButton> buttons = new List<GriddlyButton>();

    buttons.Add(new GriddlyButton() { Text = "Back to Waste Record Grid", Icon = "fa fa-chevron-circle-left", Action = GriddlyButtonAction.Navigate, Argument = Url.Action("Index", "WasteRecord") });

    buttons.Add(new GriddlyButton() { IsSeparator = true });

    if (Model != null)
    {
        buttons.Add(new GriddlyHtmlButton(){HtmlTemplate =@<text>
        @Html.Partial("~/Views/Shared/Controls/TastyUpload.cshtml", new TastyUploadSettings()
        {
            ButtonClass = "btn-sm",
            Type = typeof(Drg.M3.Domain.HW.WasteRecord).FullName,
            Id = Model.Id,
            IsPublic = false,
            DropZoneSelector = "#dropZone",
            ButtonText = "Attach Documents",
            ButtonIcon = "fa fa-paperclip"
        })</text>});

        buttons.Add(new GriddlyButton() { IsSeparator = true });

        var functionsButton = new GriddlyButton() { Text = "Actions", Icon = "fa fa-bars" };
        var printButton = new GriddlyButton() { Icon = "fa fa-print" };

        if (!Model.SignOnly && Model.Organization.UseReadyFor1348)
        {
            if (Model.ReadyFor1348 == null)
            {
                functionsButton.Buttons.Add(new GriddlyButton() { Text = "Ready For 1348", Icon = "fa fa-calendar-alt",
                    Action = GriddlyButtonAction.Post, Argument = Url.Action("SetReadyFor1348", "WasteRecord", new { Model.Id }) });
            }
            else
            {
                if(Model.Form1348 == null && Model.w_Status != "Draft" && Model.RemovalDate == null && !(Model.IsConsolidated && !Model.WasteRecords.Any())) 
                { 
                    functionsButton.Buttons.Add(new GriddlyButton() { Text = "Create a 1348", Icon = "fa fa-plus",
                        Action = GriddlyButtonAction.Post, Argument = Url.Action("Create", "Form1348", new { ids = Model.Id }) });
                }

                functionsButton.Buttons.Add(new GriddlyButton() { Text = "Undo Ready For 1348", Icon = "fa fa-undo",
                    Action = GriddlyButtonAction.Post, Argument = Url.Action("UndoReadyFor1348", "WasteRecord", new { Model.Id }) });
            }
        }

        if(new Drg.M3.Bll.HW.WasteRecordBll(null).ValidateForClose(Model))
        {
            if (Model.Closed == null)
            {
                functionsButton.Buttons.Add(new GriddlyButton() { Text = "Close", Icon = "fa fa-calendar-alt",
                    Action = GriddlyButtonAction.Post, Argument = Url.Action("SetClosed", "WasteRecord", new { Model.Id }) });
            }
            else
            {
                functionsButton.Buttons.Add(new GriddlyButton() { Text = "Undo Closed", Icon = "fa fa-undo",
                    Action = GriddlyButtonAction.Post, Argument = Url.Action("UndoClosed", "WasteRecord", new { Model.Id }) });
            }
        }

        if (Model.IsConsolidated)
        {
            if (Model.WasteType == WasteType.IndustrialWaste || Model.WasteType == WasteType.OilyWaste)
            {
                if (Model.TreatmentComplete == null)
                {
                    functionsButton.Buttons.Add(new GriddlyButton() { Argument = Url.Action("SetTreatmentComplete", "WasteRecord", new { ids = Model.Id, area = "HW", returnToWr = true }), Action = GriddlyButtonAction.Post, Text = "Treatment Complete", Icon = "fa fa-check" });
                }
                else
                {
                    functionsButton.Buttons.Add(new GriddlyButton() { Argument = Url.Action("UndoTreatmentComplete", "WasteRecord", new { ids = Model.Id, area = "HW", returnToWr = true }), Action = GriddlyButtonAction.Post, Text = "Undo Treatment Complete", Icon = "fa fa-undo" });
                }
            }

            printButton.Buttons.Add(new GriddlyButton() { Argument = Url.Action("PrintActiveReport", "Reporting", new { ids = Model.Id, area = "", DomainType = typeof(Drg.M3.Domain.HW.WasteRecord), ReportType = typeof(Drg.M3.Reports.HW.CwrPackingList).FullName }), Action = GriddlyButtonAction.Post, Text = "Packing List"});
        }

        if (Model.WasteType == WasteType.IndustrialWaste || Model.WasteType == WasteType.OilyWaste)
        {
            var wasteType = Model.WasteType == WasteType.IndustrialWaste ? WasteType.OilyWaste : WasteType.IndustrialWaste;
            functionsButton.Buttons.Add(new GriddlyButton() { Icon = "fa fa-tools", Text = $"Convert to {wasteType.ToStringDescription()}", Argument = Url.Action("ChangeWasteType", "IWOW", new { area = "HW", Model.Id, wasteType }), Action = GriddlyButtonAction.Post, ConfirmMessage = $"Are you sure you want to convert this to {wasteType.ToStringDescription()}? This will generate a new Waste Number." });
        }

        if (Model.w_Status == "Draft")
        {
            if (Model.WasteRecords.Count == 0)
            {
                functionsButton.Buttons.Add(new GriddlyButton() { Icon = "fa fa-trash-alt", Text = "Delete", Argument = Url.Action("Delete", "WasteRecord", new { area = "HW", Model.Id }), Action = GriddlyButtonAction.Post, ConfirmMessage = "Are you sure you want to delete this record?" });
            }
            else
            {
                functionsButton.Buttons.Add(new GriddlyButton() { Icon = "fa fa-trash-alt", Text = "Delete", Action = GriddlyButtonAction.Modal, Argument = "nodelete" });
            }
        }


        if(functionsButton.Buttons.Any())
        {
            buttons.Add(functionsButton);
        }
        if (printButton.Buttons.Any())
        {
            buttons.Add(printButton);
        }
    }

    Html.RenderPartial(GriddlySettings.ButtonListTemplate, new ViewDataDictionary(buttons) { { "ClassName", "pagebuttons" }, { "AlignRight", true }, { "GriddlyCss", ViewBag.IsBootstrap4 == true ? GriddlyCss.Bootstrap4Defaults : GriddlyCss.Bootstrap3Defaults } });
}

@if (Model != null && Model.WasteRecords.Count > 0 && Model.w_Status == "Draft")
{
<div id="nodelete" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5>Delete CWR</h5>
                <button class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body">
                Please remove all Waste Record's before deleting this Consolidated Waste Record
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-dismiss="modal" aria-hidden="true">Close</button>
            </div>
        </div>
    </div>
</div>
}