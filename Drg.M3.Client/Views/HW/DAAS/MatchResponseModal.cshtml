@{
    var responseId = (int)this.ViewBag.ResponseId;
    var files = this.ViewBag.OpenRequests as List<DAASFileHistory>;

    var dropdownValues = files.Select(f =>
    {
        if (f.Form1348?.DTID != null)
        {
            return new NameIdPair(f.Id, $"{f.DC.ToShortDateTimeString()} - 1348 {f.Form1348?.DTID}");
        }

        return new NameIdPair(f.Id, $"{f.DC.ToShortDateTimeString()} - Waste Profile {f.WasteProfile.WasteProfileNumber}");
    });

    this.ConfigureBootstrap4();
}

<div class="modal fade" tabindex="-1" role="dialog">
    @using (Html.BeginForm("MatchResponse", "DAAS"))
    {
        @Html.Hidden("id", responseId)
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header" style="padding-bottom:0">
                    <h5>Match Response to Request</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    @Html.DropDownList("requestId", new SelectList(dropdownValues, "Id", "Name"))
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Match</button>
                </div>
            </div>
        </div>
    }
</div>