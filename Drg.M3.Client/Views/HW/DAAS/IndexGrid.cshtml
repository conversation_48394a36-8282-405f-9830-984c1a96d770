@{
    Layout = null;

    var settings = new GriddlySettings<Drg.M3.Domain.HW.DAASSync>()
    {
        Title = "DAAS Exports",
        TableClassName = "table table-bordered grid",
        RowClickUrl = i => Url.Action("Details", new { id = i.Id }),
    };

    settings.Column(x => x.Id)
            .Column(x => x.DC, format: "MM-dd-yyyy h:mm tt (z)")
            .Column(x => x.Status,
                    filter: x => x.FilterList(new SelectList(typeof(DAASSyncStatus).GetEnumValues()
                                                                                   .Cast<DAASSyncStatus>()
                                                                                   .Select(s => s.ToStringDescription())
                                                                                   .Select(s => new NameIdPair<string>(s, s)),
                                                             "Id",
                                                             "Name"), 
                                              field: "status"))
            .Column("1348 Qty", template: @<text>@(item.Form1348s.Count)</text>)
            .Column("WP Qty", template: @<text>@(item.Form1348s.Select(f => f.WasteProfile).Where(wp => wp != null).Select(wp => wp.Id).Distinct().Count())</text>);
}

@Html.Griddly(settings)
