@{
    this.ConfigureBootstrap4();

    Layout = null;
    var rp = ViewBag.ResponsibleParty as ResponsibleParty;

    var settings = new GriddlySettings<Drg.M3.Domain.HW.JobOrder>()
    {
        Title = "GLAs",
        RowClickUrl =@<text>@if (rp.Organization == M3Context.Current.Organization) {@Url.Action("EditJobOrder", new { item.Id })} else {<text>javascript:alert('Permission Denied')</text>}</text>
    }.SelectColumn(x => x.Id)
    .Column(x => x.JobOrderNumber, "GLA", filter: x => x.Filter<PERSON>ox(FilterDataType.String))
    .Column(x => x.<PERSON>, "Inactive", filter: x => x.FilterBool(nullItemText: "[Any]"))
    .Column(x => x.Comment);

    if (rp.Organization == M3Context.Current.Organization)
    {
        settings.Button(@<text>@Url.Action("EditJobOrder", "LookupTable", new { responsiblePartyId = ViewBag.Id }) </text>, "New GLA", "add", htmlAttributes: new { id="new-gla" })
            .Button("update-selected-jons-modal", "Update GLAs", "fa fa-cogs", GriddlyButtonAction.Modal)
            .Button(@<text>@Url.Action("Deactivate", "LookupTable", new { ClassName = typeof(Drg.M3.Domain.HW.JobOrder).FullName }) </text>, "Deactivate", "deactivate", GriddlyButtonAction.Ajax)
            .Button(@<text>@Url.Action("Activate", "LookupTable", new { ClassName = typeof(Drg.M3.Domain.HW.JobOrder).FullName }) </text>, "Activate", "activate", GriddlyButtonAction.Ajax)
            .Add(new GriddlyButton() { Text = "Merge GLAs", Icon = "merge", Action=GriddlyButtonAction.Post, Argument = Url.Action("MergeJONs", "LookupTable", new { rp.Id }), EnableOnSelection = true });
    }

    settings.AddPrintButton();
}
@Html.Griddly(settings)

@if (M3Context.Current.Organization.BillingType == BillingType.WorkingCapital)
{
    using (Html.BeginComponentScript())
    {
    <script>
        $("#new-gla").click(function (e) {
            bootbox.confirm({
                message: "Are you updating a last year GLA to a current year GLA?",
                buttons: {
                    confirm: { label: 'Yes' },
                    cancel: { label: 'No' }
                },
                callback: function (result) {
                    if (result) {
                        $("#update-selected-jons-modal").modal("show");
                    } else {
                        window.location = $("#new-gla").attr("href");
                    }
                }
            });

            e.preventDefault();
            return false;
        });
    </script>
    }
}