@{
    Layout = null;
    var type = (Drg.M3.Domain.HW.ChemicalType)ViewBag.ChemicalType;

    var editPermission = M3Context.Current.Permissions.HasPermission(typeof(Drg.M3.Client.Controllers.Admin.AffiliateSettings.ReferencesController), "Index", AccessType.Edit)
        || (type == ChemicalType.Material && M3Context.Current.Permissions.HasPermission(typeof(Drg.M3.Client.Controllers.HW.MaterialComponentController), "ChemicalsMaterial", AccessType.Edit));
    var addPermission = M3Context.Current.Permissions.HasPermission(typeof(Drg.M3.Client.Controllers.Admin.AffiliateSettings.ReferencesController), "Index", AccessType.Add)
        || (type == ChemicalType.Material && M3Context.Current.Permissions.HasPermission(typeof(Drg.M3.Client.Controllers.HW.MaterialComponentController), "ChemicalsMaterial", AccessType.Add));

    var settings = new GriddlySettings<Drg.M3.Domain.HW.Chemical>() { Title = type.ToStringDescription() + "s" };

    if (editPermission)
    {
        settings.SelectColumn(x => x.Id);
        settings.RowClickUrl = @<text>@if (item.Organization == M3Context.Current.Organization) {@Url.Action("EditChemical", new { id = item.Id })}else{<text>javascript:alert('Permission Denied')</text>}</text>;
    }

    if (type == Drg.M3.Domain.HW.ChemicalType.Material)
    {
        settings.Column(x => x.CasCode, "CAS Code", filter: x => x.FilterBox(FilterDataType.String));
    }
    settings.Column(x => x.RcraCode, (type==Drg.M3.Domain.HW.ChemicalType.Material?"RCRA ":"")+ "Code", filter: x => x.FilterBox(FilterDataType.String));

    settings.Column(x => x.Name, "Chemical Name", filter: x => x.FilterBox(FilterDataType.String));
    settings.Column(x => x.SortName, "Sort Name", defaultSort: SortDirection.Ascending, filter: x => x.FilterBox(FilterDataType.String));

    if (type == Drg.M3.Domain.HW.ChemicalType.State)
    {
        settings.Column(x => x.State, "State", filter: x => x.FilterBox(FilterDataType.String));
    }
    if (type == Drg.M3.Domain.HW.ChemicalType.Toxic || type == Drg.M3.Domain.HW.ChemicalType.Material)
    {
        settings.Column(x => x.RegulatoryLevel, "CERCLA RQ", "g0", filter: x => x.FilterRange(FilterDataType.Decimal));
        settings.Column(x => x.Section313, "Section 313", filter: x => x.FilterBox(FilterDataType.String));
    }
    if (type == ChemicalType.Material)
    {
        settings.Column(x => x.RGN, "Reactivity Group Number", filter: x => x.FilterBox(FilterDataType.String));
    }
    settings.Column(x => x.Organization, filter: x => x.FilterBox(FilterDataType.String))
    .Column(x => x.IsInactive, "Inactive", filter: x => x.FilterBool(nullItemText: "[Any]"));

    if (addPermission)
    { 
        settings.Button(@<text>@Url.Action("EditChemical", new { type = (int)type })</text>, "New " + type.ToStringDescription(), "add");
    }

    if (editPermission)
    {
        settings.Button(@<text>@Url.Action("Deactivate", new { ClassName = typeof(Drg.M3.Domain.HW.Chemical).FullName }) </text>, "Deactivate", "deactivate", GriddlyButtonAction.Ajax)
            .Button(@<text>@Url.Action("Activate", new { ClassName = typeof(Drg.M3.Domain.HW.Chemical).FullName }) </text>, "Activate", "activate", GriddlyButtonAction.Ajax);
    }

    settings.AddPrintButton();
    }

@Html.Griddly(settings)