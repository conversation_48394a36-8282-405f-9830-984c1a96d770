@{
    Layout = null;

    var settings = new GriddlySettings<Drg.M3.Domain.HW.LSN>()
    {
        Title = "LSNs",
        RowClickUrl = @<text>@if (item.Organization == M3Context.Current.Organization) {@Url.Action("EditLSN", new { id = item.Id })}else{<text>javascript:alert('Permission Denied')</text>}</text>
    }
    .SelectColumn(x => x.Id)
    .Column(x => x.Name, "LSN", filter: x => x.FilterBox(FilterDataType.String))
    .Column(x => x.Description, "Description", filter: x => x.FilterBox(FilterDataType.String))
    .Column(x => x.<PERSON>ode, "DEMIL Code", filter: x => x.FilterBox(FilterDataType.String))
    .Column(x => x.FederalSupplyClass, "Federal Supply Class", filter: x => x.FilterBox(FilterDataType.String))
    .Column(x => x.Organization, filter: x => x.FilterBox(FilterDataType.String))
    .Column(x => x.IsInactive, "Inactive", filter: x => x.FilterBool(nullItemText: "[Any]"))
    .Button(@<text>@Url.Action("EditLSN", "LookupTable")</text>, "New LSN", "add")
    .Button(@<text>@Url.Action("Deactivate", "LookupTable", new { ClassName = typeof(Drg.M3.Domain.HW.LSN).FullName }) </text>, "Deactivate", "deactivate", GriddlyButtonAction.Ajax)
    .Button(@<text>@Url.Action("Activate", "LookupTable", new { ClassName = typeof(Drg.M3.Domain.HW.LSN).FullName }) </text>, "Activate", "activate", GriddlyButtonAction.Ajax);

    settings.AddPrintButton();
}

@Html.Griddly(settings)