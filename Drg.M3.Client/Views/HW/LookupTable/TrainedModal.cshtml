@model Drg.M3.Client.Models.HW.TrainedModel
@{
    Layout = null;
}
<div class="modal fade" id="trained-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="@Url.Action("SetTrainedDate", "LookupTable")" id="frmTrainedModal" method="post">
                <span class="ids"></span>
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h3>Enter Date Trained</h3>
                </div>
                <div class="modal-body">
                    <div>@Html.Label(l => l.DateTrained) @Html.EditorFor(e => e.DateTrained)</div>
                </div>
                <div class="modal-footer">
                    <a class="btn btn-default cancel" data-dismiss="modal" aria-hidden="true">Cancel</a>
                    <button class="btn btn-primary" type="submit">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>