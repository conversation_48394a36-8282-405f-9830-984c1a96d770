<%@ Control Language="C#" Inherits="Drg.M3.Client.AbstractViewUserControl<Drg.M3.Domain.Quiz.Elements.Table>" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz.Elements" %>
<%
    var t = ViewData.Model;
    var quiz = ViewData["quiz"] as Quiz;
    var readOnly = ViewData.Bool("readonly") || (t.ParametersDict["readonly"] == "true");
    var top = quiz.Assignments.FirstOrDefault();
    while(top!=null && top.Parent!=null)
    {
        top = top.Parent;
    }
    bool isAtTop = top != null && top.Task != null && new Drg.M3.Bll.DistributionBll(null).Contains(top.Task.Distribution, M3Context.Current.User);
%>

<label><%=t.Text %></label>
    
<table class="quiz_table grid rowclick" id="table_<%=t.Id %>">
    <thead>
        <tr class="columnHeaders">
            <%if(quiz.Id>0 && !readOnly){ %>
            <th style="width:16px"></th>
            <%} %>
    
            <%if(quiz.Id>0&&t.IsScored){ %>
                <th style="width:16px"></th>
            <%} %>
            
            <%foreach(var q in new Drg.M3.Bll.Quiz.QuizBll(Session).GetTableColumns(t)){ %>
            <th align="left"><%=q.ShortTitle ?? q.Text %></th>
            <%} %>
        </tr>
    </thead>
    <tbody class="data">
        <% if (!ViewData.Bool("print")) { %>
        <%/*if (quiz != null && quiz.Id != 0)
            {*/
               Html.RenderPartial("~/Views/Quiz/RenderTableData.ascx", t, ViewData);
               /*}*/
        %>
        <% } %>
    </tbody>
</table>
<%if (!readOnly && (!t.OnlyRootOrgCanAdd || isAtTop)){ %>
<a class="newTableItem" tableId="<%=t.Id %>" >New Item</a>
<%} %>
<%if(!readOnly && (!t.OnlyRootOrgCanAdd || isAtTop) && quiz.Id>0){ %>|<%} %>
<%if(quiz.Id>0){ %>
<a class="noprint" href="<%=ResolveUrl("~/Quiz/PrintElement?elementId="+Model.Id+"&quizId="+quiz.Id) %>">Download Table as PDF</a>
    <%if(new Drg.M3.Dao.DataCallDao(Session).GetAssignment(quiz) != null) { %>
    <% var dca = new Drg.M3.Dao.DataCallDao(Session).GetAssignment(quiz); %><span class="noprint"> | </span>
    <a class="noprint" href="<%=ResolveUrl("~/Reporting/DataCalls/TableExcelDump/" + dca.DataCall.Id + "?tableId="+Model.Id + "&assignmentId=" + dca.Id) %>">Download Table as Excel</a>
    <%} %>
<%} %>

<table class="printonly">
    <%foreach(var question in t.GetAllContents().OfType<Question>()){ %>
        <tr>
            <%--<th style="vertical-align:top;"><%=question.ShortTitle%></th>--%>
            <td>
                <%=question.Text %>
                <%if(question is MultipleChoice){ %>
                <label class="label">Options: <%=((MultipleChoice)question).Options.Select(op => op.Text).ConcatToString()%></label>
                <%} %>
            </td>
        </tr>
        
    <%} %>
</table>

<br /><br />

<m3:StartupScript runat="server" ID="Quiz_Table_Js" Generic="true">
    <%var quiz = ViewData["quiz"] as Quiz;%>

    <script>
        $(function () {
            <%--//Setup table dialog--%>
            $("#tableDialog").dialog({
                autoOpen: false,
                modal: true,
                resizable: false,
                //title: "Add/Edit Item",
                width: 950, height: 735
                <%if(!ViewData.Bool("readonly") && (ViewData["quiz"] as Quiz)!=null&&(ViewData["quiz"] as Quiz).Id>0){ %>
                ,buttons: {
                    Save: function () {
                        $("#tableDialogIframe")[0].contentWindow.document.getElementById("frmEditItem").submit();
                    },
                    Cancel: function () {
                        $("#tableDialogIframe").attr("src", "");
                        $(this).dialog("close");
                    }
                }
                <%}else{  %>
                ,buttons: {
                    Close: function () {
                        $("#tableDialogIframe").attr("src", "");
                        $(this).dialog("close");
                    }
                }
                <%} %>
            });

            <%--//Setup table add link--%>
            $("a.newTableItem").click(function(){
                currentTableId = $(this).attr("tableId");
                $("#tableDialog").dialog("open");
                $("#tableDialogIframe").attr("src", ResolveUrl("~/Quiz/EditItem/?parentQuizId=<%=quiz.Id %>&parentElementId=" + currentTableId));
                return false;
            });
        
            <%--//Setup table delete links--%>
            $("form.quiz table.grid tbody a.delete").live("click", function(){
                if(confirm("Are you sure you want to delete this item?"))
                {
                    var itemId = $(this).parents("tr:first").attr("rel");
                    $.post(ResolveUrl("~/Quiz/DeleteItem/" + itemId));
                    $(this).parents("tr:first").remove();
                }
                return false;
            });

            <%--//Setup table view/edit click events--%>
            $("form.quiz table.grid tbody tr[rel]").live("click", function(){
                var itemId = $(this).attr("rel");
                var autoPop = $(this).hasClass("autopop");
                
                currentTableId = $(this).parents("table:first")[0].id.split("_")[1];
                $("#tableDialog").dialog("open");
                
                $("#tableDialogIframe").attr("src", ResolveUrl("~/Quiz/EditItem/" + 
                    (autoPop ? ("?autoPop=" + itemId + "&") : (itemId + "?" + itemId))
                    + "parentQuizId=<%=quiz.Id %>&readonly=<%=ViewData.Bool("readonly") %>&parentElementId=" + currentTableId));
                
            });
        });
        var currentTableId;
        function TableItemSaved()
        {
            $("#tableDialog").dialog("close");
            $("#tableDialogIframe").attr("src", "");
            $("#table_" + currentTableId + " tbody.data").load(ResolveUrl("~/Quiz/RenderTableData?quizId=<%=quiz.Id %>&tableId=" + currentTableId ));
        }
    </script>

    <div id="tableDialog" style="overflow:hidden;display:none;" title="<%=ViewData.Bool("readonly")?"View":"Add/Edit" %> Item">
        <iframe id="tableDialogIframe" width="930" height="610" marginWidth="0" marginHeight="0" frameBorder="0" scrolling="auto" title="Dialog Title"></iframe>
    </div>
</m3:StartupScript>