<%@ Control Language="C#" Inherits="Drg.M3.Client.AbstractViewUserControl<Drg.M3.Domain.Quiz.Elements.Table>" %>
<%@ Import Namespace="Drg.M3.Bll.Quiz" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz" %>
<%@ Import Namespace="Drg.M3.Domain.Quiz.Elements" %>
<%@ Import Namespace="Drg.Core" %>

<%
    var quiz = ViewData["quiz"] as Quiz;
    IEnumerable<Quiz> items;
    if (quiz != null && quiz.Id > 0)
        items = new Drg.M3.Dao.Quiz.QuizDao(Session).GetTableRows(quiz, ViewData.Model, false);
    else
        items = new List<Quiz>();
    
    Question orderQuestion = null;
    if (ViewData.Model.ParametersDict["orderBy"] != null)
        orderQuestion = new QuizBll(Session).FindQuestionRecursive(ViewData.Model, ViewData.Model.ParametersDict["groupBy"]);
    if (orderQuestion == null && ViewData.Model.Identifier != null)
        orderQuestion = ViewData.Model.Identifier;

    if (orderQuestion != null) {
        items = items.OrderBy(i => i.GetValue(orderQuestion));
    }

    bool isGrouped = false;
    IEnumerable<IGrouping<string, Quiz>> groupedItems = null;
    if (ViewData.Model.ParametersDict["groupBy"] != null)
    {
        var groupQuestion = new Drg.M3.Bll.Quiz.QuizBll(Session).FindQuestionRecursive(ViewData.Model, ViewData.Model.ParametersDict["groupBy"]);
        groupedItems = items.GroupBy(i => groupQuestion.Format(i)).OrderBy(g => g.Key);
        isGrouped = true;
    }
    else
    {
        groupedItems = items.GroupBy(i => "");
    }
        
    var cols = new Drg.M3.Bll.Quiz.QuizBll(Session).GetTableColumns(ViewData.Model);

    int colCount = cols.Count() + 1;
    if (quiz.Completed == null)
        colCount++;

    var readOnly = ViewData.Bool("readonly") || Model.ParametersDict["readonly"] == "true";

    IEnumerable<Option> autoPops = null;
    MultipleChoice autoPopCol = null;
    if (Model.ParametersDict["autopop"] != null)
    {
        autoPopCol = new Drg.M3.Bll.Quiz.QuizBll(Session).FindQuestionRecursive(Model, Model.ParametersDict["autopop"]) as MultipleChoice;
        if (autoPopCol != null)
        {
            var alreadyExisting = items.Select(i=>i.GetValue(autoPopCol) as Option).Distinct();
            autoPops = autoPopCol.Options.Except(alreadyExisting).OrderBy(o=>o.Text);
        }
    }
    
    var top = quiz.Assignments.FirstOrDefault();
    while(top!=null && top.Parent!=null)
    {
        top = top.Parent;
    }
    bool isAtTop = top != null && top.Task != null && new Drg.M3.Bll.DistributionBll(null).Contains(top.Task.Distribution, M3Context.Current.User);
%>

<%foreach(var group in groupedItems){
        var sorted = group.AsEnumerable();
        if (Model.Sort1 != null)
        {
            sorted = sorted.OrderBy(x => Model.Sort1.FormatValue(x.GetValue(Model.Sort1)));
        }
        if (Model.Sort2 != null)
        {
            sorted = ((IOrderedEnumerable<Quiz>)sorted).ThenBy(x => Model.Sort2.FormatValue(x.GetValue(Model.Sort2)));
        }
        %>

    <%if(isGrouped){ %>
    <tr class="group"><th colspan="<%=colCount %>"><%=group.Key %></th></tr>
    <%} %>

    <%foreach(var item in sorted){ %>
    <tr rel="<%=item.Id %>">
        <%if (quiz.Id > 0 && !readOnly)
          { %>
        <td>
            <%if((quiz.Completed == null && !Model.OnlyRootOrgCanDelete) || (Model.OnlyRootOrgCanDelete && isAtTop)) {%>
            <a class="delete"><span class="icon16 delete"></span></a>
            <%} %>
        </td>
        <%} %>

        <%if(quiz.Id>0 && ViewData.Model.IsScored) { 
            var rowColor = ScoresBll.GetColor(item.FinalScore);
            %>
            <td><%=Html.IconImage("nav_plain_" + rowColor, 16)%></td>
        <%} %>
        <%foreach(var q in cols) {
            if (q != null) {
                var str = q.Format(item);
              
                if (str!=null && str.StartsWith("*Other")) { //substitue "*Other (please specify)" with comments
                    var comments = item.Settings.Comments.GetSafe(q.Id);
                    if (comments != null) str = comments;
                }
                %>

                <td>
                <%--<%if (str != null && !str.Contains("</a>")) { %>
                    <%=str == null ? null : str.Snippet(50)%>
                <%} else { %>--%>
                    <%=str %>
                <%--<%} %>--%>
                </td>
            <% } %>
        <%} %>
    </tr>
    <%} %>
      
<%} %>



<%if(autoPops!=null && autoPops.Count()>0){%>
<tr class="group"><th colspan="<%=colCount %>">Not yet supplied</th></tr>
      
      <%foreach(var autoPop in autoPops){ %>
<tr class="autopop" rel="<%=autoPop.Id %>">
    <%if (quiz.Id>0 && !readOnly&&quiz.Completed==null)      { %>
    <td></td>
    <%} %>
    <%if (quiz.Id>0 && ViewData.Model.IsScored){%>
    <td></td>
    <%} %>

    <%foreach(var q in cols){
    if(q == autoPopCol){ %>
    <td><%=autoPop.Text %></td>
    <%}else{ %>
    <td>&nbsp;</td>
    <%}} %>
</tr>
<%}} %>