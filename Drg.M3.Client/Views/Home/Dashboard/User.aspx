<%@ Page Language="C#" MasterPageFile="~/Views/Shared/Site.Master" AutoEventWireup="true" CodeBehind="User.aspx.cs" Inherits="Drg.M3.Client.Views.Home.Index" %>
<%@ Import Namespace="Drg.M3.Client" %>
<asp:Content ID="indexContent" ContentPlaceHolderID="MainContent" runat="server">
    
    <style type="text/css">
        tr.upcoming td {color:Navy; }
        tr.overdue td { color:Red; }
        tr.completed td { color:Green; }
        tr.draft td { font-style:italic; }
    </style>

    <%Html.RenderPartial("DashboardAlert");%>
    
    <%
        grdTasks.RowFormat += new EventHandler<RowFormatEventArgs>(delegate(object sender, RowFormatEventArgs e)
        {                
            var t = (e.DataItem as Task);
            
            if (t.IsDraft)
                e.ClassName += " draft";
            else if (t.CompletedDate != null)
                e.ClassName = "completed";
            else if (t.DueDate < NiftyDate.Now)
                e.ClassName = "overdue";
            else
                e.ClassName = "upcoming";
        });
    %>
    
    <m3:Grid ID="grdTasks" runat="server" Title="Tasks" EnableFilter="false" EnablePrint="false" 
        ViewAllUrl="~/Comm/Tasks" RowClickUrl="~/Comm/TaskProfile/{id}">
        <Columns>
            <m3:TemplateColumn Caption="" Align="Center">
                <Template>
                    <%# (Container.DataItem as Task).Important ? Html.Icon("sign_warning", 16) : null %>
                    <%#(Container.DataItem as Task).HasAttachments ? Html.Icon("paperclip", 16) : null%>
                    <%#(Container.DataItem as Task).HasAlerts ? Html.Icon("alert", 16) : null%>
                </Template>
            </m3:TemplateColumn>
            <m3:BoundColumn DataField="Summary" Caption="Name"/>
            <m3:OrgColumn />
            <m3:BoundColumn DataField="DueDate" Caption="Due Date" />
            <m3:BoundColumn DataField="w_Regarding" Caption="Regarding" MaxLength="25" />
            <m3:BoundColumn Caption="From" DataField="Author" />
                <%--<Template>
                    <%#(Container.DataItem as Task).Author %>
                    <%#(Container.DataItem as Task).Organization==null?"":"("+(Container.DataItem as Task).Organization.Name.ToString()+")" %>
                </Template>
            </m3:TemplateColumn>--%>
            <m3:BoundColumn DataField="IsComplete" Caption="Complete" />
        </Columns>
    </m3:Grid>
    <m3:Grid ID="grdNotifications" runat="server" Title="Notifications" EnableFilter="false" EnablePrint="false" 
        ViewAllUrl="~/Comm/Notifications" RowClickUrl="~/Contacts/Tasks/NoteProfile/{id}" Visible="false">
        <Columns>
            <m3:ActionColumn Action="~/Comm/MarkAsRead/{id}" Caption="Read" Icon="delete" />
            <m3:TemplateColumn Caption="" Align="Center">
                <Template>
                    <%#: (Container.DataItem as Communication).GetIconHtml()%>
                </Template>
            </m3:TemplateColumn>
            <m3:BoundColumn DataField="Summary" />
            <m3:OrgColumn />
            <m3:BoundColumn DataField="DC" Caption="Date" DataFormatString="M/d/yy"/>
            <m3:BoundColumn DataField="NoteType" Caption="Type" />
            <m3:BoundColumn DataField="w_Regarding" Caption="Regarding" MaxLength="25"/>
        </Columns>
    </m3:Grid>
   
    
</asp:Content>