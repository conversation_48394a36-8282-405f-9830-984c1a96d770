using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Drg.M3.Domain;
using Drg.M3.Client.Controllers.Contacts;
using Drg.M3.Client.Controllers;
using Drg.M3.Client.Security.ProfileMenus.Contacts;
using Drg.M3.Client.Controllers.Admin.Security;

namespace Drg.M3.Client.Security.ProfileMenus
{
    public class CompanyMenu : ProfileMenu<Domain.Company>
    {
        public CompanyMenu(Domain.Company company)
            : base(company)
        {
            //ProfileIcon = "company";
            Items.AddRange(new EntityMenu(company).Items);
            Items.AddRange(new List<ProfileMenuItem>()
            {   
                new ProfileMenuItem("*Profile", "UnifiedProfile", typeof(ContactsController), "Contact Info"),
                new ProfileMenuItem("Custom Fields", "CustomFields", typeof(CompanyController), "Related Items"),
                new ProfileMenuItem("Tasks", "Tasks", typeof(CompanyController), "Communication"),
            });

            if (company.IsOrganization)
            {
                if (M3Context.Current.Modules.Contains(typeof(Modules.QA)))
                {
                    Items.Add(new ProfileMenuItem("Assigned Organizations", "AssignedOrganizations", typeof(OrganizationsController), "Related Items") { RouteValues = new { id = company.Organization.Id } });
                    Items.Add(new ProfileMenuItem("Special Areas", "SpecialAreas", typeof(OrganizationsController), "Related Items") { RouteValues = new { id = company.Organization.Id } });
                    Items.Add(new ProfileMenuItem("Responsible Parties", "Tenants", typeof(OrganizationsController), "Related Items") { RouteValues = new { id = company.Organization.Id } });
                }
            }
        }
    }
}
