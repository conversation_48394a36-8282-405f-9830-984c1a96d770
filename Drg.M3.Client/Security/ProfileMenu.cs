using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Drg.M3.Domain;
using System.Web.Mvc;
using Drg.M3.Client.Security.ProfileMenus;

namespace Drg.M3.Client.Security
{
    public class ProfileMenu
    {
        public ProfileMenu() { }
        public ProfileMenu(PersistentObject obj)
        {
            this.ProfileObject = obj;
        }
        public List<ProfileMenuItem> Items { get; set; }
        public PersistentObject ProfileObject { get; protected set; }
        public virtual string ProfileIcon { get; set; }

        public static ProfileMenuItem GetItem(List<ProfileMenuItem> context, ActionDescriptor ad)
        {
            if (ad == null)
                return null;

            return GetItem(context, ad.ControllerDescriptor.ControllerType, ad.ActionName);
        }
        private static ProfileMenuItem GetItem(List<ProfileMenuItem> context, Type controllerType)
        {
            return GetItem(context, controllerType, "Index");
        }
        private static ProfileMenuItem GetItem(List<ProfileMenuItem> context, Type controllerType, string action)
        {
            string key = controllerType.FullName + "." + action;
            return context.FirstOrDefault(t => t.Key == key);
        }
        
        public static ProfileMenu GetForRecord(object record)
        {
            var type = record.GetType();

            var menuType = typeof(ProfileMenu).Assembly.GetTypes().Where(t =>
                t.Name == type.SkipProxy().Name + "Menu"
                && typeof(ProfileMenu).IsAssignableFrom(t)
                && t.BaseType.IsGenericType
                && t.BaseType.GetGenericArguments().Length == 1
                && t.BaseType.GetGenericArguments()[0] == type
            ).FirstOrDefault();

            if (menuType == null) return null;

            var constructor = menuType.GetConstructor(new Type[] { type });
            
            if (constructor == null) return null;
            
            return constructor.Invoke(new object[] { record }) as ProfileMenu;
        }
    }
    public class ProfileMenu<T> : ProfileMenu
        where T : PersistentObject
    {
        public ProfileMenu(T obj)
        {
            ProfileObject = obj;
            Items = new List<ProfileMenuItem>();
        }

        #region Properties

        protected string Key
        {
            get
            {
                return this.GetType().FullName;
            }
        }
        private string _profileIcon;
        public override string ProfileIcon
        {
            get
            {
                // if the profile object has a default profile set, then use that icon else use the icon of the set
                if (ProfileObject is IHasDefaultRole && (ProfileObject as IHasDefaultRole).DefaultRole != null)
                    return (ProfileObject as IHasDefaultRole).DefaultRole.Icon;
                else
                    return _profileIcon;
            }
            set
            {
                _profileIcon = value;
            }
        }

        #endregion
    }
}
