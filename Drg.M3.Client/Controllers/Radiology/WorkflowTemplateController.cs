using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Drg.M3.Client.Models;
using Drg.M3.Dal;
using Drg.M3.Dao;
using Drg.M3.Domain;
using Drg.M3.Domain.Radiology;
using Drg.M3.Security;
using Drg.M3.Controls.Grid;

namespace Drg.M3.Client.Controllers.Radiology
{
    [RequiresPermission(typeof(WorkflowTemplateController), "Index", AccessType.View)]
    public class WorkflowTemplateController : AbstractController
    {
        public ActionResult Index()
        {
            ViewData["selectedMenu"] = M3MenuItem.GetMenu(GetType(), "Index");

            ViewData["grid"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(new WorkflowTemplateDao(Session).GetAll(M3Context.Current.Organization));
            });

            return View();
        }

        public new ActionResult Profile(int id)
        {
            var template = Session.Get<WorkflowTemplate>(id);

            ViewData["title"] = template.Name;
            ViewData["selectedMenu"] = M3MenuItem.GetMenu(GetType(), "Index");

            ViewData["grid"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(template.WorkflowTemplateSteps);
            });

            return View(template);
        }

        [HttpPost]
        [RequiresPermission(typeof(WorkflowTemplateController), "Index", AccessType.Delete)]
        public ActionResult Delete(int id)
        {
            WorkflowTemplate template = Session.Get<WorkflowTemplate>(id);

            Session.Delete(template);

            Session.Flush();

            return RedirectToAction("Index");
        }

        [RequiresPermission(typeof(WorkflowTemplateController), "Index", AccessType.Add)]
        public ActionResult Create()
        {
            WorkflowTemplateEdit model = new WorkflowTemplateEdit();

            return View("Edit", model);
        }

        [HttpPost]
        [RequiresPermission(typeof(WorkflowTemplateController), "Index", AccessType.Add)]
        public ActionResult Create(WorkflowTemplateEdit model)
        {
            if (ModelState.IsValid)
            {
                WorkflowTemplate template = new WorkflowTemplate
                {
                    Name = model.Name,
                    Organization = M3Context.Current.Organization
                };

                Session.Save(template);

                if (model.SaveAndContinue.HasValue && model.SaveAndContinue.Value)
                {
                    return RedirectToAction("Create", "WorkflowTemplateStep", new { id = template.Id });
                }

                return RedirectToAction("Profile", new { id = template.Id });
            }

            return View("Edit", model);
        }

        [RequiresPermission(typeof(WorkflowTemplateController), "Index", AccessType.Edit)]
        public ActionResult Edit(int id)
        {
            WorkflowTemplate template = Session.Get<WorkflowTemplate>(id);

            WorkflowTemplateEdit model = new WorkflowTemplateEdit(template);

            return View(model);
        }

        [HttpPost]
        [RequiresPermission(typeof(WorkflowTemplateController), "Index", AccessType.Edit)]
        public ActionResult Edit(int id, WorkflowTemplateEdit model)
        {
            WorkflowTemplate template = Session.Get<WorkflowTemplate>(id);

            if (ModelState.IsValid)
            {                
                template.Name = model.Name;                

                Session.Save(template);

                Session.Flush();

                if (model.SaveAndContinue.HasValue && model.SaveAndContinue.Value)
                {
                    return RedirectToAction("Create", "WorkflowTemplateStep", new { id = template.Id });
                }

                return RedirectToAction("Profile", new { id = template.Id });
            }

            model.WorkflowTemplateSteps = template.WorkflowTemplateSteps;

            return View("Edit", model);
        }
    }
}
