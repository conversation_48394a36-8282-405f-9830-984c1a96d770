using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Drg.M3.Controls.Grid;
using Drg.M3.Dal;
using Drg.M3.Domain.QA;
using Drg.M3.Dao.QA;
using Drg.M3.Domain;
using Drg.M3.Client.Security.ProfileMenus.QA;
using Drg.M3.Security;
using Drg.Core;
using System.IO;
using Drg.M3.Dao;
using Drg.M3.Bll;

namespace Drg.M3.Client.Controllers.QA.Media
{
    public class BuiltEnvironmentsController : AbstractController
    {
        public ActionResult Index(int id, int? specialArea)
        {
            var media = Session.Get<Reference>(id);
            var mp = new MediaDao(Session).GetMediaProfile(media);
            ViewData["mediaProfile"] = mp;

            ViewData["facilityTypes"] = (new List<object>() { new { Name = FacilityType.Building.ToStringDescription(), Id = (int)FacilityType.Building }, new { Name = FacilityType.Structure.ToStringDescription(),
                Id = (int)FacilityType.Structure } }).AsEnumerable();
            ViewData["registerPropertyTypes"] = (new List<object>() { new { Name = PropertyType.BuildingStandAlone.ToStringDescription(), Id = (int)PropertyType.BuildingStandAlone }, new {
                Name = PropertyType.StructureStandAlone.ToStringDescription(), Id = (int)PropertyType.StructureStandAlone }, new { Name = PropertyType.SiteStandAlone.ToStringDescription(), Id =
                (int)PropertyType.SiteStandAlone }, new { Name = PropertyType.ObjectStandAlone.ToStringDescription(), Id = (int)PropertyType.ObjectStandAlone }, new { Name =
                PropertyType.BuildingContributing.ToStringDescription(), Id = (int)PropertyType.BuildingContributing }, new { Name = PropertyType.StructureContributing.ToStringDescription(), Id = 
                (int)PropertyType.StructureContributing }, new { Name = PropertyType.SiteContributing.ToStringDescription(), Id = (int)PropertyType.SiteContributing }, new { Name =
                PropertyType.ObjectContributing.ToStringDescription(), Id = (int)PropertyType.ObjectContributing }, new { Name = PropertyType.NoData.ToStringDescription(), Id = (int)PropertyType.NoData }
                }).AsEnumerable();
            ViewData["historicStatusCodes"] = (new List<object>() { new { Name = HistoricStatusCode.NHLI.ToStringDescription(), Id = (int)HistoricStatusCode.NHLI }, new { Name = HistoricStatusCode.NRLI.ToStringDescription(),
                Id = (int)HistoricStatusCode.NRLI }, new { Name = HistoricStatusCode.NREI.ToStringDescription(), Id = (int)HistoricStatusCode.NREI }, new { Name = HistoricStatusCode.NCE.ToStringDescription(), Id =
                (int)HistoricStatusCode.NCE }, new { Name = HistoricStatusCode.DNE.ToStringDescription(), Id = (int)HistoricStatusCode.DNE }, new { Name = HistoricStatusCode.NEV.ToStringDescription(), Id = 
                (int)HistoricStatusCode.NEV }, new { Name = HistoricStatusCode.NHLC.ToStringDescription(), Id = (int)HistoricStatusCode.NHLC }, new { Name = HistoricStatusCode.NRLC.ToStringDescription(), Id =
                (int)HistoricStatusCode.NRLC }, new { Name = HistoricStatusCode.NREC.ToStringDescription(), Id = (int)HistoricStatusCode.NREC }, new { Name = HistoricStatusCode.ELPA.ToStringDescription(), Id =
                (int)HistoricStatusCode.ELPA }, new { Name = HistoricStatusCode.DNR.ToStringDescription(), Id = (int)HistoricStatusCode.DNR }
                }).AsEnumerable();
            ViewData["ageClasses"] = (new List<object>() { new { Name = AgeClass.Over50.ToStringDescription(), Id = (int)AgeClass.Over50 }, new { Name = AgeClass.Under50.ToStringDescription(), Id = (int)AgeClass.Under50 },
                new { Name = AgeClass.Between45and50.ToStringDescription(), Id = (int)AgeClass.Between45and50 }
                }).AsEnumerable();

            SpecialArea filter = null;
            if (specialArea.HasValue)
            {
                ViewData["specialArea"] = specialArea.Value;
                filter = new SpecialAreaDao(Session).Get(specialArea.Value);
            }

            BuildSelectList("specialArea", new SpecialAreaDao(Session).GetAll(M3Context.Current.Organization, false), true);

            ViewData["builtEnvironments"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(new BuiltEnvironmentDao(Session).GetAll(
                    M3Context.Current.Organization,
                    false,
                    e.Grid.GetFilterValue("quickSearch"),
                    (FacilityType?)Parse.NullableInteger(e.Grid.GetFilterValue("facilityType")),
                    (AgeClass?)Parse.NullableInteger(e.Grid.GetFilterValue("ageClass")),
                    e.Grid.GetFilterValue("facilityName"),
                    e.Grid.GetFilterValue("facilityNumber"),
                    e.Grid.GetFilterValue("facilityID"),
                    Parse.NullableDateTime(e.Grid.GetFilterValue("facilityBuiltDateMin")),
                    Parse.NullableDateTime(e.Grid.GetFilterValue("facilityBuiltDateMax")),
                    e.Grid.GetFilterValue("historicName"),
                    e.Grid.GetFilterValue("historicDistrictName"),
                    (HistoricStatusCode?)Parse.NullableInteger(e.Grid.GetFilterValue("historicStatusCode")),
                    (PropertyType?)Parse.NullableInteger(e.Grid.GetFilterValue("registerPropertyType")),
                    Parse.NullableDateTime(e.Grid.GetFilterValue("registerDateMin")),
                    Parse.NullableDateTime(e.Grid.GetFilterValue("registerDateMax")),
                    Parse.NullableBoolean(e.Grid.GetFilterValue("hasSHPODoc")),
                    filter
                    ));
            });

            ViewData["title"] = media.Name;
            ViewData["titleDetail"] = "Built Environments";
            ViewData["profileMenu"] = new MediaMenu(media);

            ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(MediaController), "Index");

            return View("Index");
        }

        public ActionResult SpecialArea(int mediaProfileId, int? specialArea)
        {
            return RedirectOrReturn("~/QA/Media/BuiltEnvironments/Index/" + mediaProfileId + "?specialArea=" + specialArea);
        }

        [HttpGet]
        [Validation.ValidationSet(typeof(Drg.M3.Client.Validation.QA.BuiltEnvironment.EditValidationSet))]
        public ActionResult Edit(int? id, int? specialArea)
        {
            if (id == null)
            {
                ViewData["title"] = "New Built Environment";
                ViewData.Model = null;
                if (specialArea.HasValue)
                    ViewData["specialArea"] = specialArea.Value;
            }
            else
            {
                BuiltEnvironment be = new BuiltEnvironmentDao(Session).Get(id);
                ViewData.Model = be;

                ViewData["title"] = be.Name;
                ViewData["titleDetail"] = "Built Environment";

                ViewData["facilityType"] = ((int)be.FacilityType).ToString();
                ViewData["ageClass"] = ((int)be.AgeClass).ToString();
                ViewData["registerPropertyType"] = ((int?)be.RegisterPropertyType).ToString();
                ViewData["historicStatusCode"] = ((int?)be.HistoricStatusCode).ToString();

                if (be.SpecialArea != null)
                    ViewData["specialArea"] = be.SpecialArea.Id;
            }

            BuildSelectList("facilityType", typeof(FacilityType).EnumToEnumerable(), false);
            BuildSelectList("ageClass", typeof(AgeClass).EnumToEnumerable(), false);
            BuildSelectList("registerPropertyType", typeof(PropertyType).EnumToEnumerable(), true);
            BuildSelectList("historicStatusCode", typeof(HistoricStatusCode).EnumToEnumerable(), true);

            BuildSelectList("specialArea", new SpecialAreaDao(Session).GetAll(M3Context.Current.Organization, false), true);

            ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(MediaController), "Index");

            return View();
        }

        [HttpPost]
        [Validation.ValidationSet(typeof(Drg.M3.Client.Validation.QA.BuiltEnvironment.EditValidationSet))]
        public ActionResult Edit(int? id, int? specialArea, FacilityType? facilityType, AgeClass? ageClass, string name, string facilityNumber, string facilityID, DateTime? facilityBuiltDate,
            string historicName, string historicDistrictName, HistoricStatusCode? historicStatusCode, PropertyType? registerPropertyType, DateTime? registerDate,
            string nationalRegisterRemarks)
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return Edit(id, specialArea);
            }

            BuiltEnvironment be = new BuiltEnvironment();

            if (id == null)
            {
                be.Organization = M3Context.Current.Organization;
                be.Association = M3Context.Current.Association;

                be.FacilityType = facilityType;
                be.AgeClass = ageClass;
                be.Name = name;
                be.FacilityNumber = facilityNumber;
                be.FacilityID = facilityID;
                be.FacilityBuiltDate = facilityBuiltDate;
                if (specialArea != null)
                    be.SpecialArea = new SpecialAreaDao(Session).Get(specialArea);

                be.IsNew = true;
            }
            else
            {
                be = new BuiltEnvironmentDao(Session).Get(id.Value);

                if (be.IsNew == true)
                {
                    be.FacilityType = facilityType;
                    be.AgeClass = ageClass;
                    be.Name = name;
                    be.FacilityNumber = facilityNumber;
                    be.FacilityID = facilityID;
                    be.FacilityBuiltDate = facilityBuiltDate;
                    if (specialArea != null)
                        be.SpecialArea = new SpecialAreaDao(Session).Get(specialArea);
                }
            }

            be.HistoricName = historicName;
            be.HistoricDistrictName = historicDistrictName;
            be.HistoricStatusCode = historicStatusCode;
            be.RegisterPropertyType = registerPropertyType;
            be.RegisterDate = registerDate;
            be.RegisterStatusRemarks = nationalRegisterRemarks;

            var files = new FileBll(Session).AttachUploadedFiles(be, this.Request, "attachments");
            be.Documentation = files.FirstOrDefault();
            
            new BuiltEnvironmentDao(Session).Save(be);
            Session.Flush();
            
            return new RedirectResult(GetReturnUrl());
        }
    }
}
