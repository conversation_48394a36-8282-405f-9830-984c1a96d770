using System;
using System.Collections.Generic;
using System.Web.Mvc;
using Drg.M3.Bll;
using Drg.M3.Client.Validation.Security.Users;
using Drg.M3.Client.Views.Admin.Security.Users;
using Drg.M3.Controls.CheckTree;
using Drg.M3.Controls.Grid;
using Drg.M3.Dao;
using Drg.M3.Domain;
using Drg.Core;
using System.Security;
using System.Linq;
using Drg.M3.Security;
using Drg.M3.Dal;
using NHibernate.Criterion;
using Drg.M3.Client.Security.ProfileMenus;
using Drg.M3.Client.Configuration;
using Drg.M3.Domain.QA;
using Drg.M3.Client.Validation;
using Drg.M3.Client.Validation.Security;
using ProjectBase.Cache;
using Drg.M3.Client;
using Drg.M3.Client.Models.Admin.Users;
using Dapper;
using System.Data.SqlClient;
using Drg.M3.Bll.Reports;

namespace Drg.M3.Client.Controllers.Admin.Security
{
    public class UsersController : AbstractController
    {
        public OrganizationDao OrgDao;
        public PermissionDao GroupFunctionDao;
        public UserGroupDao UserGroupDao;

        public UsersController()
        {
            OrgDao = new OrganizationDao(Session);
            GroupFunctionDao = new PermissionDao(Session);
            UserGroupDao = new UserGroupDao(Session);
        }
        /// <summary>
        /// GET action for admin/security/users.
        /// Creates a new grid containing all users for the current organization
        /// </summary>
        /// <returns></returns>
        [RequiresPermission(AccessType.View, typeof(User))]
        public ActionResult Index()
        {
            ViewData["groups"] = new UserGroupDao(Session).GetAll(M3Context.Current.Organization, false).OrderBy(o => o.Name).AsEnumerable();

            ViewData["UsersDataSource"] = new EventHandler<Drg.M3.Controls.Grid.GridEventArgs>(delegate(object sender, Drg.M3.Controls.Grid.GridEventArgs e)
            {
                IDaoResult<User> result = new UserDao(Session).Search(
                    e.Grid.GetFilterValue("quickSearch"),
                    M3Context.Current.Organization, M3Context.Current.User.CurrentRecursive,
                    Parse.NullableBoolean(e.Grid.GetFilterValue("inactive")),
                    Parse.NullableBoolean(e.Grid.GetFilterValue("locked")),
                    null,
                    e.Grid.GetFilterValue("userGroups"),
                    e.Grid.GetFilterValue("program"),
                    e.Grid.GetFilterValue("organizationName"),
                    e.Grid.GetFilterValue("userName")
                );
                e.Grid.SetDataSource(result);
            });

            return View();
        }
        /// <summary>
        /// Returns form with all filtered users email addresses for sending batch email
        /// </summary>
        [ValidationSet(typeof(Drg.M3.Client.Validation.Security.Users.EmailAllValidationSet))]
        public ActionResult EmailAll(string cacheKey, int[] ids)
        {
            Grid grid = AppCache.Get(cacheKey) as Grid;
            var items = ids;
            if (ids == null)
            {
                items = (grid.DataSource as IDaoGridDataSource).DaoResult.ExecuteIdList(Session).ToArray();
            }
            
            List<string> emailAddresses = new List<string>();

            foreach (int i in items)
            {
                emailAddresses.Add(new UserDao(Session).Get(i).EmailAddress);
            }

            return EmailAllSend(emailAddresses.ConcatToString(s => s, "; "));
        }

        [HttpGet]
        [ValidationSet(typeof(Drg.M3.Client.Validation.Security.Users.EmailAllValidationSet))]
        public ActionResult EmailAllSend(string emails)
        {
            ViewData["emails"] = emails;

            return View("~/Views/Admin/Security/Users/<USER>");
        }

        [HttpPost]
        [ValidationSet(typeof(Drg.M3.Client.Validation.Security.Users.EmailAllValidationSet))]
        [ValidateInput(false)]
        public ActionResult EmailAllSend(string emails, string subject, string body)
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return EmailAllSend(emails);
            }

            string emailBody = new CmsBll(Session).EncodeHtml(body);

            var to = emails.Split(";").Select(s => new EmailRecipient(s)).ToArray();
            new EmailBll(Session, CurrentAssociation).SendEmail(to, subject, new CmsBll(Session).DecodeHtml(emailBody), CurrentOrganization);

            return RedirectToAction("Index");
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        public ActionResult SetStaff(int id)
        {
            var dao = new PersonDao(Session);
            Person entity = dao.Get(id);

            new SecurityBll(Session).ValidateRecord<User>(entity.User, M3Context.Current.User, AccessType.Edit);

            entity.IsStaff = true;
            dao.Save(entity);
            Session.Flush();

            return RedirectToAction("Profile", new { Id = entity.User.Id });
        }
       
        void AssignDefaultUserGroups(User u, Organization o)
        {
            if (o.DefaultUserGroup != null)
                new UserBll(Session).AddUserToGroup(u, o.DefaultUserGroup);
            if (o.ParentOrganization != null)
                AssignDefaultUserGroups(u, o.ParentOrganization);
        }

        /// <summary>
        /// GET action for admin/security/users/profile.
        /// Creates a new grid containing all groups the current user is assigned to
        /// </summary>
        /// <param name="id">ID of the current user</param>
        /// <param name="entityId">ID of the contact associated with the current user</param>
        /// <returns></returns>
        [RequiresPermission(AccessType.View, typeof(User))]
        [ProfileAction(typeof(User))]
        public new ActionResult Profile(int id)
        {
            User user =  new UserDao(Session).Get(id);
            
            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.View);
            
            if (!M3Context.Current.Permissions.HasPermission(M3MenuItem.GetMenu(M3Context.Current.ActionDescriptor)))
            {
                throw new NotAuthorizedException();
            }

            ViewData["Title"] = user.Person.Name;
            if (M3Context.Current.Association.Name != Association.HazWaste)
                ViewData["profileMenu"] = new PersonMenu(user.Person);
            
            ViewData.Model = user;
            ViewData["EditingMyself"] = user == M3Context.Current.User;

            ViewData["groups"] = new UserGroupDao(Session).GetAll(user.Organization, false, false, true, null).Execute(Session);

            ViewData["GroupsDataSource"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                User u = new UserDao(Session).Get(id);

                e.Grid.SetDataSource(u.UserGroups.Where(ug => !ug.IsUserSpecific));
            });

            ViewData["media"] = new EventHandler<GridEventArgs>(delegate (object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(Session.Get<User>(id).Media);
            });

            ViewData["medias"] = new ReferenceDao(Session).GetAll(ReferenceType.Media, M3Context.Current.Organization, false, user.Media.Select(x => x.Media).Select(x => x.Id));

            return View("Profile");
        }

        [RequiresPermission(AccessType.Edit, typeof(User))]
        public ActionResult Edit(int? id)
        {
            return EditInternal(new EditModel() { Id = id });
        }

        public ActionResult EditInternal(EditModel model, bool hydrated = false)
        {
            var u = Session.Get<User>(model.Id);

            if (u != null && !hydrated)
            {
                model.EmailAddress = u.Person.w_DefaultEmailAddress;
                model.FirstName = u.Person.FirstName;
                model.LastName = u.Person.LastName;
                model.UserName = u.UserLogin.UserName;
                model.Address = new Models.AddressModel(u.Entity.DefaultAddress);
                model.Locked = u.IsLocked;
                model.IsProgramManager = u.IsProgramManager;
                model.IsOrgAdmin = u.IsOrgAdmin;
                model.SuppressEmails = u.SuppressEmails;

                if (u.Entity.Phones.Any())
                    model.Phones = u.Entity.Phones.Select(x => new EditModel.UserEditPhoneModel(x)).ToList();
            }

            var org = u?.Organization ?? M3Context.Current.Organization;

            BuildSelectList("phoneTypes", new ReferenceDao(Session).GetAll(ReferenceType.PhoneType, org, false, u?.Entity.DefaultPhone?.PhoneType, recursiveUp: true), true, null, false);

            if (u != null && M3Context.Current.Association.Name != Association.HazWaste)
                ViewData["profileMenu"] = new PersonMenu(u.Person);

            return View(model);
        }

        [HttpPost]
        [RequiresPermission(AccessType.Edit, typeof(User))]
        public ActionResult Edit(EditModel model)
        {
            if (model.UserName.Contains("+"))
                ModelState.AddModelError("UserName", "Invalid character (+) in email address - please use a standard email address. Note that duplicate email addresses at different organizations are now possible.");

            var user = Session.Get<User>(model.Id);

            //Check for matching account in EPR Portal
            if ((M3SettingsSection.Current.IsProduction() || M3SettingsSection.Current.IsDevelopment()) && !model.SkipEprPortalUserCheck
                && (user == null || model.UserName != user.UserLogin.UserName))
            {
                try
                {
                    using (var conn = new SqlConnection(Session.Connection.ConnectionString)) //Using a separate connection prevents any errors here from zombie-ing the parent transaction
                    {
                        conn.Open();

                        bool hasMatchingEprWebAccount = conn.ExecuteScalar<int>("select count(*) from eprwebnet.dbo.[User] u where u.Email=@userName and u.Enabled=1", new { model.UserName }) > 0;
                        if (!hasMatchingEprWebAccount)
                        {
                            ModelState.AddModelError("UserName", "User does not exist in EPR Portal");

                            ViewBag.EprUsersMatchingLastName = conn.Query<EprUserModel>("select Email,FirstName,LastName from eprwebnet.dbo.[User] u where u.LastName=@lastName and u.Enabled=1", new { model.LastName }).ToList();
                        }
                    }
                }
                catch (SqlException ex)
                {
                    //Eat exception if EPR Portal database is down/inaccessable
                }
            }

            if (!ModelState.IsValid)
                return EditInternal(model, true);


            new SecurityBll(Session).ValidateRecord(user, M3Context.Current.User, AccessType.Edit);

            if (user == null)
            {
                var ul = Session.Query<UserLogin>().Where(x => x.UserName == model.UserName).FirstOrDefault();
                var org = model.OrganizationId != null ? Session.Get<Organization>(model.OrganizationId) : M3Context.Current.Organization;

                if (ul != null && ul.Users.Any(x => x.Organization == org))
                    ModelState.AddModelError("UserName", string.Format("A user account already exists for {0} at {1}", model.UserName, org.Name));

                if (!ModelState.IsValid)
                    return EditInternal(model, true);

                user = new UserBll(Session).Create(model.UserName, model.FirstName, model.LastName, org, ul);

                new SecurityBll(Session).ValidateRecord(user, M3Context.Current.User, AccessType.Add);

                new UserDao(Session).Save(user);

                AssignDefaultUserGroups(user, user.Organization);
            }
            else
            {
                if (model.UserName != user.UserLogin.UserName)
                {
                    var oldLogin = user.UserLogin;

                    var existingLogin = Session.Query<UserLogin>().Where(x => x != user.UserLogin && x.DD == null && x.UserName == model.UserName).FirstOrDefault();
                    if (existingLogin != null)
                    {
                        //move to the other login
                        oldLogin.Users.Remove(user);
                        user.UserLogin = existingLogin;
                        existingLogin.Users.Add(user);

                        //delete original login if no users left in it
                        if (oldLogin.Users.Count == 0)
                        {
                            oldLogin.DD = NiftyDate.Now;
                            oldLogin.DeletedBy = M3Context.Current.User;
                        }
                    }
                    else if (user.UserLogin.Users.Count > 1)
                    {
                        //create a new login
                        var newLogin = new UserLogin() { UserName = model.UserName, Password = oldLogin.Password };
                        Session.Save(newLogin);

                        //move to the other login
                        oldLogin.Users.Remove(user);
                        user.UserLogin = newLogin;
                        newLogin.Users.Add(user);
                    }
                    else
                    {
                        //just rename the login
                        user.UserLogin.UserName = model.UserName;
                    }
                }

                user.Person.FirstName = model.FirstName;
                user.Person.LastName = model.LastName;
                new PersonDao(Session).Save(user.Person);
                new UserDao(Session).Save(user);
            }

            user.IsLocked = model.Locked;
            user.IsOrgAdmin = model.IsOrgAdmin;
            user.IsProgramManager = model.IsProgramManager;
            user.SuppressEmails = model.SuppressEmails;

            if (model.Address?.Street != null)
            {
                var addr = user.Entity.DefaultAddress ?? new Address();
                model.Address.MapTo(addr);
                Session.Save(addr);
                user.Entity.DefaultAddress = addr;
            }
            else if(user.Entity.DefaultAddress != null)
            {
                user.Entity.DefaultAddress.DD = NiftyDate.Now;
                user.Entity.DefaultAddress.DeletedBy = M3Context.Current.User;
                user.Entity.DefaultAddress = null;
            }

            if (user.Entity.DefaultEmailAddress == null)
                user.Entity.DefaultEmailAddress = new EmailAddress() { Entity = user.Entity, Address = model.EmailAddress };

            user.Entity.DefaultEmailAddress.Address = model.EmailAddress;
            new EmailAddressDao(Session).Save(user.Entity.DefaultEmailAddress);

            {
                var existingPhones = user.Entity.Phones.ToList();
                //add/update Phones
                foreach (var phoneModel in model.Phones.Where(x => !string.IsNullOrEmpty(x.Number)))
                {
                    Phone p = existingPhones.SingleOrDefault(x => x.Id == phoneModel.Id);
                    if (p == null)
                        p = Session.Save(new Phone() { Entity = user.Entity, Number = phoneModel.Number });
                    p.Number = phoneModel.Number;
                    p.PhoneType = Session.Get<Reference>(phoneModel.PhoneTypeId);
                }
                //delete missing phones
                foreach (var p in existingPhones.Where(x => !model.Phones.Any(cm => cm.Id == x.Id)).ToList())
                {
                    p.DD = NiftyDate.Now;
                    p.DeletedBy = M3Context.Current.User;
                    new PhoneDao(Session).Save(p);
                }
            }
            
            Session.Flush();
            
            //reset permissions, in case organization was changed, which would change permissions
            M3Context.Current.ClearPermissions(user);
            UserDao.UpdateUserName(user);

            return RedirectToAction("Profile", new { user.Id });
        }
        
        /// <summary>
        /// Deletes selected user record
        /// </summary>
        /// <param name="id">ID of current user</param>
        /// <returns>Redirects to primary list of users</returns>
        [RequiresPermission(AccessType.Delete, typeof(User))]
        [HttpPost]
        public ActionResult Delete(int id)
        {
            var Dao = new UserDao(Session);
            var user = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Delete);

            try
            {
                new SecurityBll(Session).ValidateOperation(SecurityOperation.DeleteUser, user);

                user.DD = NiftyDate.Now;
                user.DeletedBy = M3Context.Current.User;
                Dao.Save(user);
            }
            catch
            {
            }

            Session.Flush();

            return new RedirectResult("~/Admin/Security/Users");
        }

        [ValidationSet(typeof(SetPasswordValidationSet))]
        [RequiresPermission(AccessType.Edit, typeof(User))]
        public ActionResult SetPassword(int id)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            ViewData.Model = user;
            ViewData["title"] = user.Person.Name;
            return View();
        }

        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        [ValidationSet(typeof(SetPasswordValidationSet))]
        public ActionResult SetPassword(int id, string newPassword1, string newPassword2)
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return SetPassword(id);
            }

            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            if (newPassword1 == newPassword2)
            {
                user.UserLogin.Password = new Bll.SecurityBll(Session).GetPasswordHash(newPassword2);

                Session.Flush();
            }
            return new RedirectResult("~/Contacts/Profile/" + user.Entity.Id);
        }

        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        public ActionResult ResetGridSettings(int id)
        {
            var u = new UserDao(Session).Get(id);
            u.Settings.GridSettings = new Dictionary<Tuple<string, string, int>, string>();
            u.Settings.Save();
            Session.Save(u);
            Session.Flush();
            return new RedirectResult("~/Contacts/Profile/" + u.Entity.Id);
        }



        /// <summary>
        /// GET action for admin/security/users/effectivepermissions
        /// </summary>
        /// <param name="id">ID of current user</param>
        /// <returns></returns>
		[RequiresPermission(AccessType.Edit, typeof(User))]
		[HttpGet]
		public ActionResult EditPermissions(int id)
		{
            var Dao = new UserDao(Session);
			var user = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

			ViewData["Title"] = user.Person.Name;
            ViewData["Titledetail"] = "Permissions";
            ViewData["profileMenu"] = new PersonMenu(user.Person);

            //if (id == M3Context.Current.User.Id)
            //{
            //    ViewData["Message"] = "You cannot edit your own permissions.";
            //    return View("~/Views/Error/NotAuthorized.aspx");
            //}

            var person = user.Person;
			if (person != null)
			{
				ViewData["Breadcrumbs"] = new ActionLinkList{
                    new ActionLink(person.Name, "~/Admin/Security/Users/<USER>/{0}", new object[]{ id }),
                    new ActionLink("Edit Permissions")
                };
			}

			ViewData.Model = user;

			int groupId = UserGroupDao.GetUsersGroup(id).Id;

            ViewData["permissions"] = new PermissionDao(Session).GetGroupPermissionNodes(Session.Get<UserGroup>(groupId));

			return View();
		}

        /// <summary>
        /// POST action for admin/security/users/editpermissions
        /// </summary>
        /// <param name="id">ID of current user</param>
        /// <returns></returns>
		[RequiresPermission(AccessType.Edit, typeof(User))]
		[HttpPost]
		public ActionResult EditPermissionsSubmit(int id)
		{
			List<CheckNode> changedNodes = CheckTree.GetChangedNodes("ctl00_MainContent_permissionTree", Request.Form);

            var Dao = new UserDao(Session);
			var gc = new GroupsController();

			int groupId = UserGroupDao.GetUsersGroup(id).Id;

			gc.UpdateGroupPermissions(groupId, changedNodes);

			M3Context.Current.ClearPermissions(Dao.Get(id));

			return RedirectToAction("EffectivePermissions", new { id = id });
		}
        [RequiresPermission(AccessType.View, typeof(User))]
        public ActionResult EffectivePermissionsP(int id)
        {
            var person = new PersonDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<User>(person.User, M3Context.Current.User, AccessType.Edit);

            return RedirectToAction("EffectivePermissions", new { Id = person.User.Id });
        }

		[RequiresPermission(AccessType.View, typeof(UserGroup))]
		public ActionResult EffectivePermissions(int id)
		{
			//SetMenuItem(MenuItem.Admin.Security.Users.Id);

            var Dao = new UserDao(Session);
			var user = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.View);

            ViewData["profileMenu"] = new PersonMenu(user.Person);

            if (!M3Context.Current.Permissions.HasPermission(M3MenuItem.GetMenu(M3Context.Current.ActionDescriptor)))
            {
                if (user == null || M3Context.Current.User != user)
                    throw new NotAuthorizedException();

                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(HomeController));
                ViewData["profileMenu"] = new DashboardMenu(user.Person);
                ViewData["readonly"] = true;
            }
            
            ViewData["Title"] = user.Person.Name;
            ViewData["Titledetail"] = "Permissions";
            ViewData.Model = user;

            M3Context.Current.ClearPermissions(user); //Reset the cache for this user
            ViewData["permissions"] = new PermissionDao(Session).GetUserEffectivePermissionNodes(user);

			return View();
		}

        [RequiresPermission(AccessType.View, typeof(User))]
        public ActionResult UserGroups(int id)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.View);

            ViewData["groups"] = new UserGroupDao(Session).GetAll(user.Organization, false, false, true, null).Execute(Session);

            ViewData["groupsDataSource"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs gea)
            {
                var u = Session.Get<User>(id);
                gea.Grid.SetDataSource(u.UserGroups.Where(ug => !ug.IsUserSpecific && (ug.IsShared || ug.Organization == u.Organization)));
            });

            ViewData["profileMenu"] = new PersonMenu(user.Person);
            ViewData["Title"] = user.Person.Name;
            ViewData["Titledetail"] = "User Groups";
            ViewData.Model = user;

            return View();
        }

        [RequiresPermission(AccessType.View, typeof(User))]
        public ActionResult AssignedUsers(int id)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.View);

            ViewData.Model = user;
            ViewData["title"] = user.FullName;
            ViewData["titleDetail"] = "Assigned Users";
            ViewData["profileMenu"] = new PersonMenu(user.Person);

            ViewData["users"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(Session.Get<User>(id).AssignedUsers);
            });

            return View();
        }
        /// <summary>
        /// Add user to the assigned users of the current user
        /// </summary>
        /// <param name="id">ID of current user</param>
        /// <param name="userId">ID of user to be assigned</param>
        /// <returns></returns>
        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        public ActionResult AssignUser(int id, int userId)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            if(!user.AssignedUsers.Any(u=>u.Id==userId))
                user.AssignedUsers.Add(Session.Get<User>(userId));
            Session.Save(user);
            Session.Flush();
            return Json(true);
        }

        /// <summary>
        /// Remove selected users from the assigned users of the current user
        /// </summary>
        /// <param name="id">ID of current user</param>
        /// <param name="ids">ID(s) of user(s) to be removed</param>
        /// <returns></returns>
        [RequiresPermission(AccessType.Edit, typeof(User))]
        [Transaction]
        [HttpPost]
        public ActionResult UnassignUsers(int id, int[] ids)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            user.AssignedUsers = user.AssignedUsers.Where(u => !ids.Contains(u.Id)).ToSet();
            Session.Save(user);
            Session.Flush();
            return Json(true);
        }

        [RequiresPermission(AccessType.View, typeof(User))]
        public ActionResult AssignedMedia(int id)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            ViewData.Model = user;
            ViewData["title"] = user.FullName;
            ViewData["titleDetail"] = "Assigned Media";
            ViewData["profileMenu"] = new PersonMenu(user.Person);

            ViewData["media"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(Session.Get<User>(id).Media);
            });

            BuildSelectList("mediaId", new ReferenceDao(Session).GetAll(ReferenceType.Media, M3Context.Current.Organization, false, user.Media.Select(x => x.Media).Select(x => x.Id)), true);

            return View();
        }

        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        public ActionResult AssignMedia(int userId, [ModelBinder(typeof(CommaSeparatedModelBinder))]int[] mediaId)
        {
            var user = Session.Get<User>(userId);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            var medias = Session.GetAll<Reference>(mediaId);

            foreach (var media in medias)
            {
                if (!user.Media.Any(m => m.Media == media))
                {
                    var u2m = new User2Media()
                    {
                        User = user,
                        Media = media,
                        CanView = true,
                        CanEdit = true
                    };
                    Session.Save(u2m);
                    Session.Flush();
                }
            }
            return Json(true);
        }

        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        public ActionResult UnassignMedia(int id, int[] ids)
        {
            var user = Session.Get<User>(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            var media = user.Media.Where(m => ids.Contains(m.Media.Id)).ToList();
            foreach (var m in media)
            {
                Session.Delete(m);
            }
            
            return Json(true);
        }

        class MissingAccountModel
        {
            public long Id { get; set; }
            public string UserName { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string Organization { get; set; }
            public bool IsLocked { get; set; }
            public bool IsInactive { get; set; }
        }

        public ActionResult EprMissingAccountsReport()
        {
            string sql = @"
select 
	e.Id,
	ul.UserName,
	e.FirstName, 
	e.LastName,
	o.Name Organization,
    u.IsLocked,
	case when e.DI is not null then 1 else 0 end IsInactive
from 
	COR_User u
	join SEC_UserLogin ul on u.UserLoginId=ul.Id
	join COR_Entity e on u.EntityId=e.Id
	join CON_Organization o on o.Id=e.OrganizationId
	left join [eprwebnet].dbo.[User] eu on eu.email=ul.UserName and eu.enabled=1
where 
	eu.email is null
	and u.DD is null
	and (o.Id=@orgId or o.KeyChain like @orgKey)
";

            var results = Session.Connection.Query<MissingAccountModel>(sql, new { orgId = CurrentOrganization.Id, orgKey = CurrentOrganization.Key + ".%" }, transaction: Session.GetDbTransaction());

            var def = results.GetExportData()
                .Column("Id", x => x.Id)
                .Column("User Name", x => x.UserName)
                .Column("First Name", x => x.FirstName)
                .Column("Last Name", x => x.LastName)
                .Column("Organization", x => x.Organization)
                .Column("Locked", x => x.IsLocked)
                .Column("Inactive", x => x.IsInactive);

            return ExcelBll.GenerateExportResult(def, results, "Missing Accounts in EPR Portal.xlsx");
        }

        #region Ajax

        [RequiresPermission(AccessType.Edit, typeof(User), typeof(UserGroup))]
		[Transaction]
        [HttpPost]
		public ActionResult RemoveFromGroups(int id, int[] ids)
		{
			object feedback = true;

			User u = new UserDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<User>(u, M3Context.Current.User, AccessType.Edit);

			if (u.Id == M3Context.Current.User.Id)
				return Json("You cannot remove yourself from a group");

            var bll = new UserBll(Session);

			foreach (int gid in ids)
			{
				var group = new UserGroupDao(Session).Get(gid);
                
                if (!group.IsShared && group.Organization.IsOutside(M3Context.Current.User.Organization))
				{
					feedback = "You cannot remove users from groups outside your organization";
				}
				else
				{
                    u.UserGroups.Remove(group);
					//bll.RemoveUserFromGroup(u, group);
				}
			}

            Session.Flush();
			M3Context.Current.ClearPermissions(u);

			return Json(feedback);
		}

		[RequiresPermission(AccessType.Edit, typeof(User), typeof(UserGroup))]
        [HttpPost]
		public ActionResult AddToGroup(int id, [ModelBinder(typeof(CommaSeparatedModelBinder))]int[] groupId)
		{
            var Dao = new UserDao(Session);
			var user = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

            var groups = Session.GetAll<UserGroup>(groupId);

            foreach (var group in groups)
            {
                try
                {
                    new SecurityBll(Session).ValidateOperation(SecurityOperation.AddUserToGroup, user, group);

                    if (!user.UserGroups.Contains(group))
                        user.UserGroups.Add(group);
                }
                catch (SecurityException ex)
                {
                    return Json(ex.Message);
                }
            }

            Dao.Save(user);
            Session.Flush();

            M3Context.Current.ClearPermissions(user);

            return Json(true);
        }

        [RequiresPermission(AccessType.Delete, typeof(User))]
        [HttpPost]
        public ActionResult DeleteUsers(string[] ids)
        {
            var Dao = new UserDao(Session);
            HashSet<string> errors = new HashSet<string>();

            foreach (string id in ids)
            {
                var user = Dao.Get(Parse.Integer(id));

                new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Delete);

                try
                {
                    new SecurityBll(Session).ValidateOperation(SecurityOperation.DeleteUser, user);

                    user.DD = NiftyDate.Now;
                    user.DeletedBy = M3Context.Current.User;
                    Dao.Save(user);
                }
                catch (SecurityException ex)
                {
                    errors.Add(ex.Message);
                }
            }
            Session.Flush();

            return Json(errors);
        }

        [RequiresPermission(AccessType.Edit, typeof(User))]
        [HttpPost]
        public ActionResult LockUsers(string[] ids)
        {
            var Dao = new UserDao(Session);
            HashSet<string> errors = new HashSet<string>();

            foreach (string id in ids)
            {
                var user = Dao.Get(Parse.Integer(id));

                new SecurityBll(Session).ValidateRecord<User>(user, M3Context.Current.User, AccessType.Edit);

                try
                {
                    new SecurityBll(Session).ValidateOperation(SecurityOperation.LockUser, user);

                    user.IsLocked = !user.IsLocked;
                    new UserDao(Session).Save(user);
                }
                catch (SecurityException ex)
                {
                    errors.Add(ex.Message);
                }
            }
            Session.Flush();

            return Json(errors);
        }

        #endregion
    }
}
