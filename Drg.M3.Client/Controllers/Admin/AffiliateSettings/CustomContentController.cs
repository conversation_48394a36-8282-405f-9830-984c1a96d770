using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Drg.M3.Controls.Grid;
using Drg.M3.Domain;
using Drg.M3.Dal;
using Drg.M3.Security;
using Drg.M3.Client.Validation;
using Drg.M3.Client.Validation.Admin.AffiliateSettings.CustomContent;
using System.Collections;
using Drg.M3.Dao;
using Drg.Core;
using Drg.M3.Bll;

namespace Drg.M3.Client.Controllers.Admin.AffiliateSettings
{

    public class CustomContentController : AbstractController
    {
        /// <summary>
        /// GET action for admin/affiliatesettings/customcontent/index.
        /// Creates a grid with all the Custom Content Pages for the current organization.
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            ViewData["customContent"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(new CustomContentDao(Session).GetAll(
                    M3Context.Current.Organization, 
                    M3Context.Current.User.CurrentRecursive,
                    e.Grid.GetFilterValue("quickSearch"),
                    Parse.NullableBoolean(e.Grid.GetFilterValue("shared"))
                ));
            });

            return View();
        }

        /// <summary>
        /// GET action for admin/affiliatesettings/customcontent/edit.
        /// </summary>
        /// <param name="id">The ID of the custom content page to edit</param>
        /// <param name="originalId">The ID of the page we are copying</param>
        /// <returns></returns>
        /// 
        [RequiresPermission(AccessType.Edit, "Drg.M3.Client.Controllers.Admin.AffiliateSettings.CustomContentController.Index")]
        [ValidationSet(typeof(EditValidationSet))]
        public ActionResult Edit(int? id, int? originalId)
        {
            var cc = Session.Get<CustomContent>(id);
            var original = Session.Get<CustomContent>(originalId);

            //new SecurityBll(Session).ValidateRecord<CustomContent>(original, M3Context.Current.User, AccessType.Edit);

            ViewData["id"] = id;
            ViewData.Model = cc ?? original;
            if (ViewData.Model != null)
                ViewData["original"] = original ?? cc.Original;

            if (cc != null)
            {
                ViewData["title"] = cc.Name;
                //ViewData["menu"] = cc.MenuKey;
            }
            else
                ViewData["title"] = "New Custom Content";

            //List<NameIdPair<string>> menus = new List<NameIdPair<string>>
            //menus.Add(new NameIdPair<string>("",""));
            //foreach (var rootMenu in M3MenuItem.RootMenus.Where(mi => mi.ModuleType == null || M3Context.Current.Modules.Contains(mi.ModuleType)))
            //{
            //    menus.Add(new NameIdPair<string>(rootMenu.Key, rootMenu.Name));
            //    foreach (var subMenu in (rootMenu as M3MenuItemCollection).Where(mi => mi.ModuleType == null || M3Context.Current.Modules.Contains(mi.ModuleType)))
            //    {
            //        menus.Add(new NameIdPair<string>(subMenu.Key, "--" + subMenu.Name));
            //    }
            //}
            //BuildSelectList("menu", menus);

            var org=cc!=null?cc.Organization:original!=null?original.Organization:M3Context.Current.Organization;

            if ((cc ?? original) != null && (cc ?? original).PageType != null)
                ViewData["pageType"] = (cc ?? original).PageType.Id.ToString();

            BuildSelectList("pageType", new ReferenceDao(Session).GetAll(ReferenceType.ContentPageType, org, false, cc==null?null:cc.PageType), true);
            ViewData["selectedMenu"] = M3MenuItem.GetMenu(GetType(), "Index");
            ViewData["allUserGroups"] = new UserGroupDao(Session).GetAll(org, false, false, true, null).Execute(Session);

            return View();
        }
        /// <summary>
        /// POST action for admin/affiliatesettings/customcontent/edit.
        /// </summary>
        /// <param name="id">ID of the custom content page to edit </param>
        /// <param name="originalId"></param>
        /// <param name="name">Name of the custom content page </param>
        /// <param name="html">URL for links in document</param>
        /// <param name="menu"></param>
        /// <param name="isShared">Shares page with child organizations</param>
        /// <param name="allowOverride">Allows Child organizations to override page</param>
        /// <returns>Redirect to custom content grid or new custom content (if copied)</returns>
        [HttpPost]
        [RequiresPermission(AccessType.Edit, "Drg.M3.Client.Controllers.Admin.AffiliateSettings.CustomContentController.Index")]
        [ValidationSet(typeof(EditValidationSet))]
        [ValidateInput(false)]
        public ActionResult Edit(int? id, int? originalId, string name, string html, string menu, bool? isShared, bool? isPublic, bool? allowOverride, string scripts, string reference, int? pageType, string parameters, int[] userGroups)
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return Edit(id, originalId);
            }

            var cc = Session.Get<CustomContent>(id);

            //new SecurityBll(Session).ValidateRecord<CustomContent>(cc, M3Context.Current.User, AccessType.Edit);

            if (cc == null)
            {
                cc = new CustomContent() 
                { 
                    Organization = M3Context.Current.Organization, 
                };
                if(originalId.HasValue)
                {
                    var original = Session.Get<CustomContent>(originalId);
                    cc.Original = original.Original ?? original;
                }
            }
            
            cc.Name = name;
            cc.Reference = reference;
            cc.Html = new CmsBll(Session).EncodeHtml(html.Replace("%7B","{").Replace("%7D","}"));
            cc.Scripts = scripts;
            cc.PageType = Session.Get<Reference>(pageType);
            cc.Parameters = parameters;

            if (cc.Original == null)
            {
                cc.IsShared = isShared.Value;
                cc.IsPublic = isPublic.Value;
                cc.AllowOverride = allowOverride.Value;
                cc.MenuKey = menu;
            }
            else
            {
                cc.IsShared = cc.Original.IsShared;
                cc.IsPublic = cc.Original.IsPublic;
                cc.AllowOverride = cc.Original.AllowOverride;
                cc.MenuKey = cc.Original.MenuKey;
            }

            UpdateList<UserGroup>(cc.UserGroups, userGroups);
            cc.w_UserGroupCount = cc.UserGroups.Count;

            Session.Save(cc);
            Session.Flush();

            //foreach (var ug in new UserGroupDao(Session).GetAll(cc.Organization, false, false, true, null).Execute(Session))
            //{
            //    var p = new PermissionDao(Session).Get(ug.Id, typeof(CustomContent).FullName + "." + cc.Id, AccessType.View);
            //    if (cc.UserGroups.Contains(ug) && p == null)
            //    {
            //        p = new Permission() { AccessType = AccessType.View, FunctionKey = typeof(CustomContent).FullName + "." + cc.Id, PermissionType = PermissionType.Grant, UserGroup = ug };
            //        Session.Save(p);
            //    }
            //    else if (!cc.UserGroups.Contains(ug) && p != null)
            //        Session.Delete(p);
            //}

            //Session.Flush();

            CustomContentDao.Invalidate();

            if (GetReturnUrl() != null)
                return new RedirectResult(GetReturnUrl());
            else if (originalId == null)
                return new RedirectResult("~/Admin/AffiliateSettings/CustomContent");
            else
                return new RedirectResult("~/Common/CustomContent/" + cc.Id);
        }
        /// <summary>
        /// GET action for admin/affiliatesettings/customcontent/edit.
        /// Gets all custom content pages for the current organization
        /// </summary>
        /// <returns></returns>
        public ActionResult CcpLink()
        {
            ViewData["customContent"] = new CustomContentDao(Session).GetAll(M3Context.Current.Organization, false, includePublic: true).Execute(Session);
                //Session.GetAll<CustomContent>(M3Context.Current.Organization, false);
            return View();
        }
        /// <summary>
        /// GET action for admin/affiliatesettings/customcontent/edit.
        /// Properties for document link/upload in Custom Content Page
        /// </summary>
        /// <param name="image"></param>
        /// <param name="id">ID of the document being created/inserted into the page</param>
        /// <param name="isPublic">Specifies whether or not the document is available to other Organization levels</param>
        /// <returns></returns>
        public ActionResult DocumentLink(bool? image, int? id, bool? isPublic)
        {
            ViewData["label"] = Session.Get<Label>(id);

            ViewData["items"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                IDaoResult<Document> result = null;
                int? labelId = id;
                if (e.FilterValues != null)
                {
                    string label = null;
                    if (id == null)
                    {
                        label = e.FilterValues["label"];
                        labelId = Parse.NullableInteger(e.FilterValues["labelId"]);
                    }
                    var name = e.FilterValues["fileName"];
                    var category = Parse.NullableInteger(e.FilterValues["category"]);
                    var media = Parse.NullableInteger(e.FilterValues["media"]);
                    var iso14001 = Parse.NullableInteger(e.FilterValues["iso14001"]);

                    result = new DocumentDao(Session).Search(M3Context.Current.Organization, false, name, labelId, label, null, isPublic, false, category, media, iso14001, null, null, null, null, null);
                }
                else
                {
                    result = new DocumentDao(Session).Search(M3Context.Current.Organization, false, null, labelId, null, null, isPublic, false);
                }
                e.Grid.SetDataSource(result);
            });
            BuildSelectList("media", new ReferenceDao(Session).GetAll(ReferenceType.Media, M3Context.Current.Organization, false), true);
            ViewData["isPublic"] = isPublic;
            return View();
        }
        /// <summary>
        /// GET action for admin/affiliatesettings/customcontent/edit.
        /// Properties for image upload in Custom Content Page
        /// </summary>
        /// <param name="isPublic">Specifies whether or not the image is available to other Organization levels</param>
        /// <returns></returns>
        public ActionResult Image(bool? isPublic)
        {
            ViewData["documents"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                IDaoResult<Document> result = null;
                if (e.FilterValues != null)
                {
                    var label = e.FilterValues["label"];
                    var labelId = Parse.NullableInteger(e.FilterValues["labelId"]);
                    var name = e.FilterValues["fileName"];
                    result = new DocumentDao(Session).Search(M3Context.Current.Organization, false, name, labelId, label, true, isPublic, false);
                }
                else
                {
                    result = new DocumentDao(Session).Search(M3Context.Current.Organization, false, null, null, null, true, isPublic, false);
                }
                e.Grid.SetDataSource(result);
            });
            return View();
        }
        [HttpPost]
        public JsonResult Document(int[] ids)
        {
            var documents = Session.GetAll<Document>(ids);

            return Json(new
            {
                documents = documents.Select(d => new
                {
                    id = d.Id,
                    url = M3Context.Current.ResolveAbsoluteUrl("~/Document/Download/" + d.Id),
                    text = d.Name
                })
            });
        }
        /// <summary>
        /// GET action for Admin/AffiliateSettings/CustomContent/edit.
        /// Gets list of all documents in current organization
        /// </summary>
        /// <param name="id">ID of document being inserted</param>
        /// <returns></returns>
        public ActionResult InsertDocument(int id)
        {
            ViewData.Model = Session.Get<Document>(id);
            return View();
        }
    }
}
