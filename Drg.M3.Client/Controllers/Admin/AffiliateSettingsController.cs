using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using Drg.M3.Domain;
using Drg.M3.Dao;
using Drg.M3.Client.Configuration;
using Drg.Core;
using Drg.M3.Security;
using Drg.M3.Bll.Security;
using Drg.M3.Dal;
using Drg.M3.Bll.Reports;
using System.Reflection;

namespace Drg.M3.Client.Controllers.Admin
{
    public class AffiliateSettingsController : AbstractController
    {
        /// <summary>
        /// Action handler for admin/AffiliateSettings/index.
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            var org = M3Context.Current.Organization;
            
            ViewData["onlyStaffMayEditPictures"] = org.Settings.OnlyStaffMayEditPictures;

            return View();
        }

        [HttpPost]
        public ActionResult Index(string submit, bool onlyStaffMayEditPictures)
        {
            var org = M3Context.Current.Organization;

            org.Settings.OnlyStaffMayEditPictures = onlyStaffMayEditPictures;

            Session.Save(org);
            Session.Flush();

            return RedirectToAction("Index");
        }
    }
}
