using Drg.M3.Client.Models.Shared.Controls;
using Drg.M3.Domain;
using Drg.M3.Modules;
using System;
using System.Linq;
using System.Web.Mvc;

namespace Drg.M3.Client.Controllers.HW
{
    [Module(typeof(Modules.HW))]
    public class SidebarNotificationsController : AbstractController
    {
        // GET: SidebarNotifications
        public ActionResult Index()
        {
            var wpsReviewDateNifty = NiftyDate.Now.AddYearsInCurrentZone(-M3Context.Current.Organization.WasteProfileReviewInterval);
            var model = new SidebarNotificationsModels
            {
                WpsDueForReview = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteProfile>()
                    .Where(x => !x.IsInactive && x.Organization == M3Context.Current.Organization && (x.LastReviewDate.Date < DateTime.Now.AddYears(-M3Context.Current.Organization.WasteProfileReviewInterval)
                        || (x.LastReviewDate == null && x.DC < wpsReviewDateNifty)))
                    .Count(),
                WrsApproachingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteRecord>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.RemovalDate == null
                    && DateTime.Now.AddDays(x.Organization.WasteRecordApproachingDays) > x.AccumulationStartDate.Value.AddDays(x.StorageLocation.Facility.StorageMaxDays.Value)
                    && x.AccumulationStartDate.Value.AddDays(x.StorageLocation.Facility.StorageMaxDays.Value) > DateTime.Now && x.w_Status != "Shipped" && x.ConsolidatedTo == null)
                    .Count(),
                WrsExceedingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteRecord>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.RemovalDate == null
                    && DateTime.Now > x.AccumulationStartDate.Value.AddDays(x.StorageLocation.Facility.StorageMaxDays.Value) && x.w_Status != "Shipped" && x.ConsolidatedTo == null)
                    .Count(),
                WrsShipped = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteRecord>()
                   .Where(x => x.Organization == M3Context.Current.Organization && x.w_Status == "Shipped")
                   .Count(),
                WrsShippedExceedingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteRecord>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.w_Status == "Shipped" && x.Form1348.DeliveryOrder.Date.Value.AddDays(x.Organization.WasteRecordShippedDeadlineDays) < DateTime.Now)
                    .Count(),
                WrsDraft = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteRecord>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.w_Status == "Draft")
                    .Count(),
                WrsReadyTo1348 = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.WasteRecord>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.ReadyFor1348 != null && x.Form1348 == null && x.RemovalDate == null)
                    .Count(),
                ManifestsAwaitingClosure = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.Manifest>()
                    .Where(x => x.Type == Drg.M3.Domain.HW.ShippingType.Outgoing && x.Organization == M3Context.Current.Organization && x.RemovalDate != null && x.Returned != null && x.Closed == null)
                    .Count(),
                MsApproachingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.Manifest>()
                    .Where(x => x.Type == Drg.M3.Domain.HW.ShippingType.Outgoing && x.Organization == M3Context.Current.Organization && x.RemovalDate != null && x.Returned == null
                    && DateTime.Now.Date.AddDays(x.Organization.ManifestApproachingDays) > x.RemovalDate.Value.AddDays(x.Organization.ManifestTerm)
                    && x.RemovalDate.Value.AddDays(x.Organization.ManifestTerm) > DateTime.Now)
                    .Count(),
                MsExceedingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.Manifest>()
                    .Where(x => x.Type == Drg.M3.Domain.HW.ShippingType.Outgoing && x.Organization == M3Context.Current.Organization && x.RemovalDate != null && x.Returned == null
                    && x.RemovalDate.Value.AddDays(x.Organization.ManifestTerm) < DateTime.Now)
                    .Count(),
                PuApproachingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.Pickup>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.Scheduled != null && x.PickupDateTime == null
                    && DateTime.Now.AddDays(x.Organization.PickupApproachingDays) > x.Scheduled
                    && DateTime.Now < x.Scheduled)
                    .Count(),
                PuExceedingDeadline = Drg.M3.Dal.M3SessionSingleton.Instance.Query<Drg.M3.Domain.HW.Pickup>()
                    .Where(x => x.Organization == M3Context.Current.Organization && x.Scheduled != null && x.PickupDateTime == null
                    && DateTime.Now >= x.Scheduled)
                    .Count()
            };

            return PartialView("~/Views/Shared/Controls/SidebarNotifications.cshtml", model);
        }
    }
}