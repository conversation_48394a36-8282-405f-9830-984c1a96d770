using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using Drg.M3.Domain;
using Drg.M3.Dao;
using Drg.M3.Client.Configuration;
using Drg.Core;
using Drg.M3.Client.Validation.Contacts.Company;
using Drg.M3.Client.Views.Contacts.Company;
using Drg.M3.Controls.Grid;
using Drg.M3.Bll;
using Drg.M3.Client.Views.Shared;

using System.Data.OleDb;
using System.Data;
using System.IO;
using Drg.M3.Security;
using Drg.M3.Modules;
using Drg.M3.Dal;
using System.Reflection;
using Drg.M3.Client.Security.ProfileMenus;

namespace Drg.M3.Client.Controllers.Contacts
{
    [Module(typeof(Modules.Core))]
    public class CompanyController : AbstractController
    {
        void SetMenuItem(Entity entity)
        {
            if (entity is Person)
            {
                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(PersonController));
                ViewData["profileMenu"] = new PersonMenu(entity as Person);
            }
            else
            {
                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(CompanyController));
                ViewData["profileMenu"] = new CompanyMenu(entity as Company);
            }
        }

        /// <summary>
        /// Action handler for contacts/companies/index
        /// </summary>
        /// <returns>Redirects to company search</returns>
        [RequiresPermission(AccessType.View, typeof(Company))]
        public ActionResult Index()
        {
            ViewData["companies"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.SetDataSource(new CompanyDao(Session).GetAll(M3Context.Current.Organization, M3Context.Current.User.CurrentRecursive,
                        e.Grid.GetFilterValue("quickSearch"),
                        Parse.NullableInteger(e.Grid.GetFilterValue("type")),
                        Parse.NullableBoolean(e.Grid.GetFilterValue("isInactive")),
                        Parse.NullableInteger(e.Grid.GetFilterValue("region")),
                        e.Grid.GetFilterValue("address"),
                        e.Grid.GetFilterValue("group")
                    )
                );
            });

            ViewData["types"] = new ReferenceDao(Session).GetAll(ReferenceType.CompanyType, M3Context.Current.Organization, M3Context.Current.User.CurrentRecursive);

            return View();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [RequiresPermission(AccessType.View, typeof(Company))]
        public new ActionResult Profile(int id)
        {
            return RedirectToAction("UnifiedProfile", "Contacts", new { id = id });
        }

        [HttpGet]
        [RequiresPermission(AccessType.View, typeof(Company))]
        public ActionResult CustomFields(int id)
        {
            using (var sess = M3SessionSingleton.Create())
            {
                var c = sess.Get<Company>(id);
                var o2q = new CustomFieldBll(sess).GetObject2Quiz(c, c.Organization, true);
                sess.Flush();
                return new RedirectResult("~/Quiz/Edit/" + o2q.Quiz.Id);
            }
        }

        //[HttpGet]
        //[RequiresPermission(AccessType.View, typeof(Company))]
        //public ActionResult CustomFields(int id)
        //{
        //    var Dao = new CompanyDao(Session);
        //    var company = Dao.Get(id);

        //    if (company.Organization.IsOutside(M3Context.Current.User.Organization))
        //        throw new NotAuthorizedException("You may not edit shared items from another organization");

        //    //SetTitle("Custom Fields", company);

        //    ViewData["title"] = company.Name;
        //    ViewData["titlelink"] = "~/Contacts/Profile/" + company.Id;
        //    ViewData["titledetail"] = "Custom Fields";

        //    SetMenuItem(company);


        //    var list1 = new CustomFieldDao(Session).GetCustomListForObjectType(typeof(Company), company.Organization);
        //    var list2 = new CustomFieldDao(Session).GetCustomListForObjectType(typeof(Company), null);

        //    var item1 = new CustomFieldBll(Session).GetObject2Item(company, company.Organization, false);
        //    var item2 = new CustomFieldBll(Session).GetObject2Item(company, null, false);


        //    if (list1 != null)
        //        ViewData["list1"] = list1.List;
        //    if (list2 != null)
        //        ViewData["list2"] = list2.List;

        //    if (item1 != null)
        //        ViewData["item1"] = item1.Item;
        //    if (item2 != null)
        //        ViewData["item2"] = item2.Item;


        //    return View("CustomFields");
        //}

        //[HttpPost]
        //[RequiresPermission(AccessType.Edit, typeof(Person))]
        //public ActionResult CustomFields(int id, string button)
        //{
        //    var Dao = new CompanyDao(Session);
        //    var company = Dao.Get(id);
        //    new CustomFieldBll(Session).Save(company, Request.Form, company.Organization);
        //    new CustomFieldBll(Session).Save(company, Request.Form, null);

        //    AxiSession.Commit();
        //    return new RedirectResult("~/Contacts/Company/Profile/" + id);
        //}

        [RequiresPermission(AccessType.View, typeof(Company))]
        public ActionResult Groups(int id)
        {
            var Dao = new CompanyDao(Session);
            var company = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.View);

            SetTitle("Groups", company);

            SetMenuItem(company);
			ViewData["Breadcrumbs"] = company.ToString();

            return View("NotImplemented");
        }

        [RequiresPermission(AccessType.Add, typeof(Company))]
        [Drg.M3.Client.Validation.ValidationSet(typeof(CreateValidationSet))]
        public ActionResult Create()
        {
            //SetMenuItem(MenuItem.Contacts.Companies.Id);
            //ViewData["BreadCrumbs"] = "Add Company";
            ViewData["Title"] = "Add Company";

            ViewData["mailOk"] = Parse.Boolean(Request.Form["mailOk"], true);
            ViewData["faxOk"] = Parse.Boolean(Request.Form["faxOk"], true);
            ViewData["phoneOk"] = Parse.Boolean(Request.Form["phoneOk"], true);
            ViewData["emailOk"] = Parse.Boolean(Request.Form["emailOk"], true);
			//ViewData["phoneType"] = (int)PhoneType.Work;
			//ViewData["emailAddressType"] = 2; //TODO: make emailaddresstype an enum

            BuildSelectList("phoneType", new ReferenceDao(Session).GetAll(ReferenceType.PhoneType, M3Context.Current.Organization, false, recursiveUp: true));
            BuildSelectList("emailAddressType", new ReferenceDao(Session).GetAll(ReferenceType.EmailAddressType, M3Context.Current.Organization, false));
            BuildSelectList("companyType", new ReferenceDao(Session).GetAll(ReferenceType.CompanyType, M3Context.Current.Organization, false), false);
            //BuildSelectList("companyStatus", nesw ReferenceDao(Session).GetAll<CompanyStatus>(M3Context.Current.Organization));


            return View();
        }

        [RequiresPermission(AccessType.Add, typeof(Company))]
        [HttpPost]
        [Drg.M3.Client.Validation.ValidationSet(typeof(CreateValidationSet))]
        [Transaction]
        public ActionResult Create(
            [Bind(Include = "Name, AltName, WebPage, IsMember", Prefix = "company")] Company company,
            int[] companyType,
            string phone, int? phoneType, string emailAddress, int? emailAddressType,
            [Bind(Include = "Country, Street, Street2, City, StateProvince, ZipCode, AddressType", Prefix = "address")] Address address,
            bool mailOk, bool faxOk, bool phoneOk, bool emailOk, string note, bool? acceptDuplicate, string button, bool shared,
            bool? contractor, decimal? bondAmount, DateTime? bondExpirationDate, int? agent, bool? bondRequired, bool? cbaSignatory
        )
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return Create();
            }
            //if (!this.ValidateForm(typeof(Create)))
            //    return Create();

            var Dao = new CompanyDao(Session);
            var similarCompanies = Dao.GetSimilarCompanies(
                M3Context.Current.Organization, company.Name);

            if (similarCompanies.Count() > 0 && !acceptDuplicate.GetValueOrDefault())
            {
                var top = similarCompanies.First();
                ViewData["duplicate"] = top.Name;
                return Create();
            }
            /*if (companyStatus == 3 && companyType == 3) //pending prospect
            {
                AddError("companyStatus", "You cannot add a \"Prospect\" company with a \"Payment Pending\" status");
                return Create();
            }*/

			if (string.IsNullOrEmpty(address.Street))
				address = null;

            Reference phoneReference = null;
            if (phoneType != null) 
                phoneReference = new ReferenceDao(Session).Get(phoneType.Value);

            new CompanyBll(Session).Create(M3Context.Current.Organization, companyType, address, phone, phoneReference, company, shared);

            company.NoMail = !mailOk;
            company.NoFax = !faxOk;
            company.NoEmail = !emailOk;
            company.NoPhone = !phoneOk;

            Reference emailType = null;
            if (emailAddressType != null) 
                emailType = new ReferenceDao(Session).Get(emailAddressType.Value);
            if (!string.IsNullOrEmpty(emailAddress))
                company.EmailAddresses.Add(new EmailAddressBll(Session).Create(company, emailAddress, emailType));

            Dao.Save(company);

            if (!string.IsNullOrEmpty(note))
                new NoteBll(Session).CreateNote(null, note, M3Context.Current.User.Id,null, company);

            Session.Flush();

            if (button.ToLower() == "save and add another")
                return new RedirectResult(Request.Url.PathAndQuery);
            else
                return new RedirectResult("~/Contacts/Company/Profile/" + company.Id);
        }

        [RequiresPermission(AccessType.Edit, typeof(Company))]
        [Drg.M3.Client.Validation.ValidationSet(typeof(EditValidationSet))]
        public ActionResult Edit(int id)
        {
            var Dao = new CompanyDao(Session);
            var company = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);

            SetMenuItem(company);
            ViewData["tab"] = "Company";

            if (!M3Context.Current.Permissions.HasPermission(M3MenuItem.GetMenu(M3Context.Current.ActionDescriptor)))
            {
                if (M3Context.Current.User.Person.DefaultCompany != company)
                    throw new NotAuthorizedException();

                ViewData["profileMenu"] = new DashboardMenu(M3Context.Current.User.Person);
                ViewData["selectedmenu"] = M3MenuItem.GetMenu(typeof(HomeController));
            }

            ViewData["title"] = company.ToString();
            ViewData["titledetail"] = "Edit";

			ViewData["Breadcrumbs"] = company.Name;

            ViewData.Model = company;
            ViewData["Id"] = id;
            ViewData["shared"] = company.IsShared;

            BuildMultiSelectList("groups", new GroupDao(Session).GetActive(company.Organization, false), company.ActiveGroups, true);

            BuildMultiSelectList("companyType", 
                new ReferenceDao(Session).GetAll(ReferenceType.CompanyType, company.Organization, false, company.CompanyTypes.ToList().ConvertAll(o => o.Id)),
                company.CompanyTypes);

            return View("~/Views/Contacts/Edit.aspx");
        }

        [RequiresPermission(AccessType.Edit, typeof(Company))]
        [HttpPost]
        [Drg.M3.Client.Validation.ValidationSet(typeof(EditValidationSet))]
        [Transaction]
        public ActionResult Edit(
            int id, int[] companyType,
            string description, bool? shared, int? region, string button, int[] groups,
            string name, string altName, string webPage
        )
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return Edit(id);
            }

            var Dao = new CompanyDao(Session);
            var company = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);

            company.Name = name;
            company.AltName = altName;
            company.WebPage = webPage;
            company.Description = description;
            new ReferenceBll(Session).UpdateReferences(company.CompanyTypes, companyType);

            if (shared != null)
                company.IsShared = shared.Value;

            new GroupBll(Session).SetEntityGroups(company, groups != null ? Session.GetAll<Group>(groups) : new List<Group>());

            company.ImageFile = ImageField.GetFile(Session, "imageFile", company.Organization, company.ImageFile);

            Dao.Save(company);
            Session.Flush();

            if (button.ToLower() == "save")
            {
                ViewData["confirm"] = "Successfully saved.";
                return RedirectToAction("Edit", "Company", new { id = id });
            }
            else
                return new RedirectResult("~/Contacts/Company/Profile/" + id);
        }        
        
        [RequiresPermission(AccessType.Edit, typeof(Company))]
        public ActionResult CommunicationSettings(int id)
        {
            var Dao = new CompanyDao(Session);
            var company = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);
            
            SetMenuItem(company);

            if (!M3Context.Current.Permissions.HasPermission(M3MenuItem.GetMenu(M3Context.Current.ActionDescriptor)))
            {
                if (M3Context.Current.User.Person.DefaultCompany != company)
                    throw new NotAuthorizedException();

                ViewData["profileMenu"] = new DashboardMenu(M3Context.Current.User.Person);
                ViewData["selectedmenu"] = M3MenuItem.GetMenu(typeof(HomeController));
            }

            //SetTitle("Communication Settings", company);
            ViewData["title"] = company.ToString();
            ViewData["titledetail"] = "Communication Settings";

			//ViewData["Breadcrumbs"] = company.ToString();

            ViewData["mailOk"] = !company.NoMail;
            ViewData["faxOk"] = !company.NoFax;
            ViewData["emailOk"] = !company.NoEmail;
            ViewData["phoneOk"] = !company.NoPhone;


            return View();
        }

        [RequiresPermission(AccessType.Edit, typeof(Company))]
        [HttpPost]
        public ActionResult CommunicationSettings(
            int id,
            bool mailOk, bool faxOk, bool phoneOk, bool emailOk, bool newsletterOk
        )
        {
            var Dao = new CompanyDao(Session);
            var company = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);
            
            company.NoMail = !mailOk;
            company.NoFax = !faxOk;
            company.NoEmail = !emailOk;
            company.NoPhone = !phoneOk;
            
            Dao.Save(company);
            Session.Flush();
            return new RedirectResult("~/Contacts/Company/Profile/" + id);
        }

        [RequiresPermission(AccessType.Delete, typeof(Company))]
        [HttpPost]
        public ActionResult Delete(int id)
        {
            var Dao = new CompanyDao(Session);
            var c = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(c, M3Context.Current.User, AccessType.Delete);

            new CompanyBll(Session).Delete(c);

            Session.Flush();

            return new RedirectResult("~/");
        }

        #region Ajax
        [RequiresPermission(AccessType.View, typeof(PersonCompany))]
        [HttpPost]
        public ActionResult AddPerson(int id, int personId)
        {
            var Dao = new CompanyDao(Session);
            var company = Dao.Get(id);
            var person = new PersonDao(Session).Get(personId);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.View);

            new PersonBll(Session).JoinCompany(person, company);

            Session.Flush();

            return Json(true);
        }
        #endregion

        [RequiresPermission(AccessType.Edit, typeof(Company))]
        [HttpPost]
        public ActionResult SetPrimaryUser(int[] ids, int companyId)
        {
            if (ids.Length > 1)
                return Json("Only one primary person may be selected.");
            else if (ids.Length == 1)
            {
                var company = new CompanyDao(Session).Get(companyId);

                new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);

                var person = new UserDao(Session).Get(ids[0]).Person;
                company.PrimaryPerson = person;
                var Dao = new CompanyDao(Session);
                Dao.Save(company);
                Session.Flush();
            }

            return Json(true);
        }
        [RequiresPermission(AccessType.Edit, typeof(Company))]
        [HttpPost]
        public ActionResult SetPrimaryPerson(int[] ids, int companyId)
        {
            if (ids.Length > 1)
                return Json("Only one primary person may be selected.");
            else if (ids.Length == 1)
            {
                var company = new CompanyDao(Session).Get(companyId);

                new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);

                var person = new PersonCompanyDao(Session).Get(ids[0]).Person;
                company.PrimaryPerson = person;
                var Dao = new CompanyDao(Session);
                Dao.Save(company);
                Session.Flush();
            }

            return Json(true);
        }

        #region ByPerson Redirects
        // redirects to the profile of the persons default company
        public new ActionResult ProfileByPerson(int id)
        {
            Company c = new PersonDao(Session).Get(id).DefaultCompany;

            if (c != null)
                return RedirectToAction("Profile", "Company", new { Id = c.Id });
            else
                throw new ArgumentException("id");
        }

        // redirects to the profile of the persons default company
        public ActionResult AddressesByPerson(int id)
        {
            Company c = new PersonDao(Session).Get(id).DefaultCompany;

            if (c != null)
                return RedirectToAction("Addresses", "Communication", new { Id = c.Id });
            else
                throw new ArgumentException("id");
        }

        // redirects to the profile of the persons default company
        public ActionResult CommSettingsByPerson(int id)
        {
            Company c = new PersonDao(Session).Get(id).DefaultCompany;

            if (c != null)
                return RedirectToAction("CommunicationSettings", "Company", new { Id = c.Id });
            else
                throw new ArgumentException("id");
        }

        // redirects to the profile of the persons default company
        public ActionResult EmailsByPerson(int id)
        {
            Company c = new PersonDao(Session).Get(id).DefaultCompany;

            if (c != null)
                return RedirectToAction("EmailAddresses", "Communication", new { Id = c.Id });
            else
                throw new ArgumentException("id");
        }

        // redirects to the profile of the persons default company
        public ActionResult PhonesByPerson(int id)
        {
            Company c = new PersonDao(Session).Get(id).DefaultCompany;

            if (c != null)
                return RedirectToAction("Phones", "Communication", new { Id = c.Id });
            else
                throw new ArgumentException("id");
        }

        // redirects to the profile of the persons default company
        public ActionResult TasksByPerson(int id)
        {
            Company c = new PersonDao(Session).Get(id).DefaultCompany;

            if (c != null)
                return RedirectToAction("Index", "Tasks", new { eId = c.Id });
            else
                throw new ArgumentException("id");
        }
        #endregion

        [RequiresPermission(AccessType.Edit, typeof(Company))]
        public ActionResult Roles(int id)
        {
            var Dao = new CompanyDao(Session);
            Company entity = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Company>(entity, M3Context.Current.User, AccessType.Edit);

            ViewData["title"] = entity.ToString();
            ViewData["titledetail"] = "Roles";
            SetMenuItem(entity);
            ViewData.Model = entity;

            return View();
        }
        
        [HttpPost]
        public ActionResult MakeOrgCompany(int id)
        {
            var company = Session.Get<Company>(id);

            new SecurityBll(Session).ValidateRecord<Company>(company, M3Context.Current.User, AccessType.Edit);

            company.Organization.Company = company;
            company.IsOrganization = true;
            Session.Save(company.Organization);
            Session.Flush();

            return new RedirectResult("~/Contacts/Company/Roles/" + id);
        }
    }
}
