using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

using Drg.M3.Domain;
using Drg.M3.Dao;
using Drg.M3.Client.Configuration;
using Drg.M3.Controls.Grid;
using Drg.Core;
using System.Text.RegularExpressions;
using Drg.M3.Bll;
using System.IO;
using Drg.M3.Client.Validation.Contacts;
using Drg.M3.Client.Views.Contacts;

using System.Security;

using System.Collections.Specialized;
using Drg.M3.Security;
using Drg.M3.Dal;
using Drg.M3.Client.Controllers.Contacts;
using Drg.M3.Modules;
using System.Reflection;
using Drg.M3.Client.Security.ProfileMenus;
using Drg.M3.Client.Validation;
using NHibernate.Criterion;
using Drg.M3.Client.Controllers.Admin.Security;

namespace Drg.M3.Client.Controllers
{
    [Module(typeof(Modules.Core))]
    public class ContactsController : AbstractController
    {
        [RequiresPermission(AccessType.View, typeof(Person), typeof(Company))]
        public ActionResult Index()
        {
            return this.FirstAvailableChild(M3MenuItem.RootMenus["Contacts"]);
        }

        
        [RequiresPermission(AccessType.Edit, typeof(Entity))]
        public ActionResult Edit(int id, string tab)
        {
            Entity e = new EntityDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(e, M3Context.Current.User, AccessType.Edit);

            if (string.IsNullOrWhiteSpace(tab))
            {
                string editUrl = e.DefaultRole.GetEditUrl(e);
                if (!string.IsNullOrEmpty(editUrl))
                    return new RedirectResult(editUrl);
                else if (e is Person)
                    return RedirectToAction("Edit", "Person", new { e.Id });
                else
                    return RedirectToAction("Edit", "Company", new { e.Id });
            }

            switch (tab.ToLower())
            {
                case "person":
                    return RedirectToAction("Edit", "Person", new { e.Id });
                case "company":
                    return RedirectToAction("Edit", "Company", new { id = e.Id });
                case "organization":
                    return RedirectToAction("Edit", "Organizations", new { id = e.Organization.Id });
                case "user":
                    return RedirectToAction("Edit", "Users", new { id = (int?)null, entityId = e.Id });
                default:
                    return RedirectToAction("EditContactInfo", "Contacts", new { id = e.Id });
            }
        }

        [RequiresPermission(AccessType.Edit, new[] { typeof(Entity), typeof(Organization) })]
        [HttpGet]
        public ActionResult EditContactInfo(int id, bool? confirm)
        {
            Entity e = new EntityDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(e, M3Context.Current.User, AccessType.Edit);

            if (confirm != null && confirm.Value) ViewData["confirm"] = "Successfully saved.";

            if (e is Person)
                ViewData["Title"] = (e as Person).FullName;
            else
                ViewData["Title"] = e.ToString();

            ViewData["titleDetail"] = "Edit";
            if (e.DefaultAddress != null && e.DefaultAddress.AddressType != null)
                ViewData["addressType"] = e.DefaultAddress.AddressType.Id;

            BuildSelectList("addresstype", new ReferenceDao(Session).GetAll(ReferenceType.AddressType, e.Organization, false, e.DefaultAddress != null ? e.DefaultAddress.AddressType : null));
            BuildSelectList("phonetype", new ReferenceDao(Session).GetAll(ReferenceType.PhoneType, e.Organization, false, e.DefaultPhone != null ? e.DefaultPhone.PhoneType : null, recursiveUp: true), true, null, false);
            BuildSelectList("emailtype", new ReferenceDao(Session).GetAll(ReferenceType.EmailAddressType, e.Organization, false, e.DefaultEmailAddress != null ? e.DefaultEmailAddress.EmailAddressType : null));

            if (e is Person)
            {
                if(e.IsUser)
                    ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(UsersController));
                else
                    ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(PersonController));

                ViewData["profileMenu"] = new PersonMenu(e as Person);
            }
            else
            {
                var c = e as Company;
                if (c.IsOrganization)
                {
                    ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(OrganizationsController));
                }
                else
                {
                    ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(CompanyController));
                }
                ViewData["profileMenu"] = new CompanyMenu(e as Company);

            }
            
            if (e.DefaultAddress != null)
            {
                ViewData["street"] = e.DefaultAddress.Street;
                ViewData["street2"] = e.DefaultAddress.Street2;
                ViewData["zipCode"] = e.DefaultAddress.ZipCode;
                ViewData["city"] = e.DefaultAddress.City;
                ViewData["stateProvince"] = e.DefaultAddress.StateProvince;
                ViewData["country"] = e.DefaultAddress.Country;
            }

            foreach (Phone p in e.Phones)
                ViewData["phone_" + p.Id] = p.ToString();

            foreach (EmailAddress ea in e.EmailAddresses)
                ViewData["email_" + ea.Id] = ea.Address;

            ViewData["tab"] = "Contact";
            ViewData.Model = e;

            return View("EditContactInfo");
        }

        [RequiresPermission(AccessType.Edit, new[] { typeof(Entity), typeof(Organization) })]
        [HttpPost]
        public ActionResult EditContactInfo(int id, string street, string street2, string city, string stateProvince, string zipCode, string button, int? addressType, 
            int? emailtype, int? phonetype, string phoneDescription,
            string personFirstName, string personLastName, string personTitle, string personPhone, string personBuilding)
        {
            // TODO: Validate

            // THIS IS COMPLETELY WRONG!!!!

            Entity e = new EntityDao(Session).Get(id);
            if (stateProvince != null && zipCode != null)//street != null && city != null && stateProvince != null && zipCode != null)
            {
                if (e.DefaultAddress == null)
                {
                    Address a = new Address();
                    e.DefaultAddress = a;
                }

                e.DefaultAddress.Entity = e;
                e.DefaultAddress.Street = street ?? "";
                e.DefaultAddress.Street2 = street2;
                e.DefaultAddress.City = city ?? "";
                e.DefaultAddress.StateProvince = stateProvince;
                e.DefaultAddress.ZipCode = zipCode;

                Session.Save(e.DefaultAddress);

                e.DefaultAddress.AddressType = Session.Get<Reference>(addressType);

                new AddressDao(Session).Save(e.DefaultAddress);
            }

            if (e is Company && (e as Company).IsOrganization
                   && M3Context.Current.Modules.Contains(typeof(Modules.HW))
                   && (personFirstName != null || personLastName != null))
            {
                var person = (e as Company).PrimaryPerson;
                if (person == null)
                {
                    person = Session.Save(new Person() { Organization = e.Organization, FirstName = personFirstName, LastName = personLastName });
                    new PersonBll(Session).JoinCompany(person, (Company)e, personTitle);
                    (e as Company).PrimaryPerson = person;
                }
                else
                {
                    person.DefaultPersonCompany.Title = personTitle;
                    person.FirstName = personFirstName;
                    person.LastName = personLastName;
                }
                if (personPhone != null)
                {
                    var phone = person.DefaultPhone;
                    if (phone == null)
                        phone = new PhoneBll(Session).Create(person, personPhone);
                    else
                        phone.Number = personPhone;
                    person.DefaultPhone = phone;
                }
                else if (person.DefaultPhone != null)
                {
                    Session.Delete(person.DefaultPhone);
                    person.DefaultPhone = null;
                }

                person.Building = personBuilding;

                Session.Save(person);
            }


            foreach (string s in Request.Form.AllKeys.Where(w => w.StartsWith("phone_")))
            {
                Phone p = null;
                var pId = int.Parse(s.Split('_')[1]);

                if (!string.IsNullOrWhiteSpace(Request.Form[s].ToString()))
                {
                    if (pId != 0)
                        p = new PhoneDao(Session).Get(pId);
                    else
                    {
                        p = new Phone();
                        p.PhoneType = Session.Get<Reference>(phonetype);
                        p.Description = phoneDescription;
                        p.Entity = e;
                    }

                    p.Number = Request.Form[s].ToString();
                    new PhoneDao(Session).Save(p);
                }
                else
                {
                    if (pId != 0)
                    {
                        p = new PhoneDao(Session).Get(pId);
                        p.DD = NiftyDate.Now;
                        p.DeletedBy = M3Context.Current.User;
                        new PhoneDao(Session).Save(p);
                    }
                }
            }

            foreach (string s in Request.Form.AllKeys.Where(w => w.StartsWith("email_")))
            {
                EmailAddress ea = null;
                var eId = int.Parse(s.Split('_')[1]);

                if (!string.IsNullOrWhiteSpace(Request.Form[s].ToString()))
                {
                    if (eId != 0)
                        ea = new EmailAddressDao(Session).Get(eId);
                    else
                    {
                        ea = new EmailAddress();
                        ea.EmailAddressType = Session.Get<Reference>(emailtype);
                        ea.Entity = e;
                    }

                    ea.Address = Request.Form[s].ToString();
                    new EmailAddressDao(Session).Save(ea);
                }
                else
                {
                    if (eId != 0)
                    {
                        ea = new EmailAddressDao(Session).Get(eId);
                        ea.DD = NiftyDate.Now;
                        ea.DeletedBy = M3Context.Current.User;
                        new EmailAddressDao(Session).Save(ea);
                    }
                }
            }

            new EntityDao(Session).Save(e);
            Session.Flush();

            if (button == "Save")
                return RedirectToAction("EditContactInfo", new { id = id, confirm = true });
            else
                return Profile(id);
        }

		private Entity SetNoteContext(int eid)
		{
			var entity = new EntityDao(Session).Get(eid);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.Edit);

            SetProfile(entity);
			ViewData["EntityType"] = entity.GetType().Name;
			ViewData["Entity"] = entity;
            ViewData["organizationId"] = entity.Organization.Id;

			if (entity is Person)
			{
                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(PersonController));
                ViewData["profileMenu"] = new PersonMenu(entity as Person);
                //ViewData["selectedmenuitemid"] = MenuItem.Contacts.People.Notes.Id;
				ViewData["Breadcrumbs"] = ((Person)entity).ToString();
			}
			else
			{
                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(CompanyController));
                ViewData["profileMenu"] = new CompanyMenu(entity as Company);
				//ViewData["selectedmenuitemid"] = MenuItem.Contacts.Companies.Notes.Id;
				ViewData["Breadcrumbs"] = ((Company)entity).ToString();
			}

			return entity;
		}

        public ActionResult Notes(int id)
        {
			var entity = SetNoteContext(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.View);

            ViewData.Model = entity;
            //SetTitle("Notes", entity);
            ViewData["Title"] = entity.ToString();
            ViewData["titlelink"] = "~/Contacts/Profile/" + entity.Id;
            ViewData["TitleDetail"] = "Notes";

            ViewData["GridDataSource"] = new EventHandler<Drg.M3.Controls.Grid.GridEventArgs>(delegate(object sender, Drg.M3.Controls.Grid.GridEventArgs e)
            {
                e.Grid.DataSource = new DaoGridDataSource<Note>(
                    new NoteDao(Session).GetNotesForObject(M3Context.Current.User, entity));
            });

            ViewData["readOnly"] = entity.Organization.IsOutside(M3Context.Current.User.Organization) && !M3Context.Current.User.UserLogin.DrgPersonnel;

            return View("~/Views/Comm/Notes.aspx");
        }

        public ActionResult NotesForCompanyAndPeople(int id)
        {
            var company = SetNoteContext(id) as Company;
            ViewData["includePeople"] = true;
            ViewData.Model = company;
            SetTitle("Notes", company);

            ViewData["GridDataSource"] = new EventHandler<Drg.M3.Controls.Grid.GridEventArgs>(delegate(object sender, Drg.M3.Controls.Grid.GridEventArgs e)
            {
                e.Grid.SetDataSource(new NoteDao(Session).GetNotesForCompanyAndPeople(M3Context.Current.User, company));
            });

            ViewData["readOnly"] = company.Organization.IsOutside(M3Context.Current.User.Organization) && !M3Context.Current.User.UserLogin.DrgPersonnel;

            return View("~/Views/Comm/Notes.aspx");
        }

        [Drg.M3.Client.Validation.ValidationSet(typeof(Drg.M3.Client.Validation.Shared.Comm.CreateNoteValidationSet))]
        public ActionResult CreateNote(int id)
        {
			var entity = SetNoteContext(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.Add);

            //SetTitle("Create Note", entity);
            ViewData["title"] = entity.ToString();
            ViewData["titlelink"] = "~/Contacts/Profile/" + entity.Id;
            ViewData["titleDetail"] = "Notes";

            //ViewData["authorText"] = M3Context.Current.User.Person.ToString();
            //ViewData["author"] = M3Context.Current.User.Id;

            BuildSelectList("author", new List<User>() { M3Context.Current.User }, false);
            BuildSelectList("noteTypeId", new ReferenceDao(Session).GetAll(ReferenceType.NoteType, M3Context.Current.Organization, false), true);

            return View("~/Views/Comm/CreateNote.aspx");
        }

        [Drg.M3.Client.Validation.ValidationSet(typeof(Drg.M3.Client.Validation.Shared.Comm.CreateNoteValidationSet))]
        [HttpPost]
        public ActionResult CreateNote(int id, string subject, string body, int author, int? noteTypeId, string button, bool important)
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return CreateNote(id);
            }
            //if (!this.ValidateForm(typeof(Drg.M3.Client.Views.Shared.Comm.CreateNote)))
            //    return CreateNote(id);
            var e = new EntityDao(Session).Get(id);
            var noteDao = new NoteDao(Session);
            var note = new NoteBll(Session).CreateNote(subject, body, author,
                noteTypeId, e);
            note.Important = important;
            noteDao.Save(note);
            Session.Flush();

            if (button == "Save - Add Task")
                return new RedirectResult("~/Contacts/" + e.w_EntityType + "/Tasks/" + id + "/CreateTask");
            return new RedirectResult("~/Contacts/Notes/" + id);
        }

        [RequiresPermission(AccessType.Edit, typeof(Entity))]
        [Drg.M3.Client.Validation.ValidationSet(typeof(Drg.M3.Client.Validation.Shared.Comm.EditNoteValidationSet))]
        public ActionResult EditNote(int id, int entityId)
        {
            var note = new NoteDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Note>(note, M3Context.Current.User, AccessType.Edit);
            
            new NoteBll(Session).ValidateOperation(note, AccessType.Edit);
			var entity = SetNoteContext(entityId);
            //SetTitle("Edit Note", entity);
            ViewData["title"] = entity.ToString();
            ViewData["titleDetail"] = "Notes";

            ViewData.Model = note;
            ViewData["body"] = note.Message;
            ViewData["subject"] = note.Subject;
            ViewData["authorText"] = note.Author == null ? "" : note.Author.Person.ToString();
            ViewData["author"] = note.Author == null ? (int?)null : note.Author.Id;
            ViewData["important"] = note.Important;
            
            if (note.NoteType != null)
                ViewData["noteTypeId"] = note.NoteType.Id;

            BuildSelectList("noteTypeId", new ReferenceDao(Session).GetAll(ReferenceType.NoteType, entity.Organization, false, note.NoteType), true);
            if (note.Author != null)
                BuildSelectList("author", new List<User>() { note.Author });

            return View("~/Views/Comm/EditNote.aspx");
        }

        [RequiresPermission(AccessType.Edit, typeof(Entity))]
        [Drg.M3.Client.Validation.ValidationSet(typeof(Drg.M3.Client.Validation.Shared.Comm.EditNoteValidationSet))]
        [HttpPost]
        public ActionResult EditNote(int id, int entityId, string subject, string body, int author, int? noteTypeId, bool important)
        {
            if (!Validate())
            {
                CopyFormToViewData();
                return EditNote(id, entityId);
            }
            //if (!this.ValidateForm(typeof(Drg.M3.Client.Views.Shared.Comm.EditNote)))
            //    return EditNote(id, entityId);

            var noteDao = new NoteDao(Session);
            var note = noteDao.Get(id);
            new NoteBll(Session).UpdateNote(note, subject, body, author, noteTypeId);
            note.Important = important;

            Session.Flush();

            return new RedirectResult("~/Contacts/Profile/" + entityId);
        }

        [RequiresPermission(AccessType.Delete, typeof(Entity))]
        [HttpPost]
        public ActionResult DeleteEntities(string[] ids)
        {
            HashSet<string> errors = new HashSet<string>();
            
            foreach (string id in ids)
            {
                var entity = new EntityDao(Session).Get(Parse.Integer(id));
                try
                {
                    if (entity is Person)
                        new PersonBll(Session).Delete(entity as Person);
                    else if (entity is Company)
                        new CompanyBll(Session).Delete(entity as Company);
                }
                catch (SecurityException ex)
                {
                    errors.Add(ex.Message);
                }
            }
            Session.Flush();

            return Json(errors);
        }

        #region Files
        public ActionResult Files(int id)
        {
            var entity = new EntityDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.View);

            ViewData.Model = entity;

            SetProfile(entity);
            //SetTitle("Files", entity);
            ViewData["Title"] = entity.ToString();
            ViewData["TitleDetail"] = "Files";
            
            ViewData["files"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                e.Grid.DataSource = new DaoGridDataSource<Object2File>(new FileDao(Session).GetForObject(entity));
            });

            return View();
        }

        [HttpPost]
        public ActionResult DeleteFiles(int[] ids)
        {
            //todo:permissions
            foreach (var id in ids)
            {
                var o2f = Session.Get<Domain.Object2File>(id);
                if (o2f.File.Document == null)
                {
                    o2f.File.DD = NiftyDate.Now;
                    o2f.File.DeletedBy = M3Context.Current.User;
                }

                //Also delete the document
                else if (o2f.File.Document.IsAttachment && o2f.File == o2f.File.Document.DefaultFile)
                {
                    o2f.File.Document.DD = o2f.File.DD;
                    o2f.File.Document.DeletedBy = o2f.File.DeletedBy;
                }

                Session.Delete(o2f);
            }
            Session.Flush();

            return Json(true);
        }
        #endregion

        #region Quick navigation
        //[RequiresPermission(AccessType.View, typeof(Entity))]
        //[Obsolete("Use Profile instead")]
        //public ActionResult Entity(int id)
        //{
        //    var entity = new EntityDao(Session).Get(id);
        //    return new RedirectResult("~/Contacts/Profile/" + id);
        //}

        [RequiresPermission(AccessType.View, typeof(Company))]
        public ActionResult DefaultCompany(int id)
        {
            var entity = new EntityDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.View);

            if (entity is Person)
                if ((entity as Person).DefaultCompany != null && (entity as Person).DefaultCompany.DD == null)
                    entity = (entity as Person).DefaultCompany;
            return RedirectToAction("Profile", new { entity.Id });
        }

        [RequiresPermission(AccessType.View, typeof(Person))]
        public ActionResult PrimaryPerson(int id)
        {
            var entity = new EntityDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.View);

            if (entity.w_EntityType == "Company")
                entity = (entity as Company).PrimaryPerson;
            return RedirectToAction("Profile", new { entity.Id });
        }
        #endregion

        #region Utility
        IEnumerable<NameIdPair> GetScopes(Organization org)
        {
            bool hasSysPermission = M3Context.Current.Organization.ParentOrganization == null && M3Context.Current.Permissions.HasPermission(FunctionKeys.Reporting.Queries.SystemScope, AccessType.Edit);
            bool hasOrgPermission = M3Context.Current.Permissions.HasPermission(FunctionKeys.Reporting.Queries.OrganizationScope, AccessType.Edit);

            return typeof(Scope).EnumToEnumerable().Where(r => r.Id > 1 && r.Id < 5 && (r.Id != 3 || hasOrgPermission) && (r.Id != 4 || hasSysPermission))
                .ToList().ConvertAll<NameIdPair>(s => new NameIdPair(s.Id,
                    s.Id == 2 ? "My Searches" :
                    s.Id == 3 ? org.Name + " Searches" :
                    "System Searches"));
        }
        void SetProfile(Entity entity)
        {
            ViewData["selectedMenu"] = M3MenuItem.GetMenu(entity is Person ? typeof(PersonController) : typeof(CompanyController));
            if (entity is Person)
                ViewData["profileMenu"] = new PersonMenu(entity as Person);
            else
                ViewData["profileMenu"] = new CompanyMenu(entity as Company);
        }
        #endregion

        #region Entities
        [RequiresPermission(AccessType.View, typeof(Entity))]
        [ProfileAction(typeof(Person),"Index", typeof(PersonController))]
        [ProfileAction(typeof(Company), "Index", typeof(CompanyController))]
        [ProfileAction(typeof(Entity))]
        public ActionResult UnifiedProfile(int id)
        {
            return Profile(id);
        }

        public new ActionResult Profile(int id)
        {
            var entity = Session.Get<Entity>(id);
            string profileUrl = entity.DefaultRole.GetProfileUrl(entity);
            if (!string.IsNullOrEmpty(profileUrl))
                return new RedirectResult(profileUrl);
            else
                throw new InvalidOperationException("Role is missing");
        }


        [RequiresPermission(AccessType.Delete, typeof(Entity))]
        [HttpPost]
        public ActionResult Delete(int id)
        {
            if (!M3Context.Current.Permissions.HasPermission(M3MenuItem.GetMenu(this.GetType()), AccessType.Delete))
                throw new NotAuthorizedException();

            Entity entity = new EntityDao(Session).Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.Delete);

            new EntityBll(Session).Delete(entity, M3Context.Current.User);
            Session.Flush();

            if (entity is Person)
                return RedirectToAction("Index", "Person");
            else
                return RedirectToAction("Index", "Company");
        }

        [HttpPost]
        public ActionResult Activate(int id)
        {
            var Dao = new EntityDao(Session);
            var entity = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.Edit);

            entity.IsInactive = false;
            Dao.Save(entity);
            Session.Flush();
            return RedirectToProfile(entity);
        }

        [HttpPost]
        public ActionResult Deactivate(int id)
        {
            var Dao = new EntityDao(Session);
            var entity = Dao.Get(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.Delete);

            entity.IsInactive = true;

            Dao.Save(entity);
            Session.Flush();
            return new RedirectResult("~/Contacts/Profile/" + id);
        }

        ActionResult RedirectToProfile(Entity entity)
        {
            return new RedirectResult(entity.DefaultRole.GetProfileUrl(entity));
        }
        #endregion

        public ActionResult ActionHistory(int id)
        {
            var entity = Session.Get<Entity>(id);

            new SecurityBll(Session).ValidateRecord<Entity>(entity, M3Context.Current.User, AccessType.View);

            ViewData["title"] = entity.ToString();
            ViewData["titleLink"] = "~/Contacts/Profile/" + entity.Id;
            ViewData["titleDetail"] = "Action History";

            if (entity is Person)
            {
                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(PersonController));
                ViewData["profileMenu"] = new PersonMenu(entity as Person);
            }
            else
            {
                ViewData["selectedMenu"] = M3MenuItem.GetMenu(typeof(CompanyController));
                ViewData["profileMenu"] = new CompanyMenu(entity as Company);
            }

            ViewData["DataSource"] = new EventHandler<GridEventArgs>(delegate(object sender, GridEventArgs e)
            {
                var notes = new NoteDao(Session).GetNotesForObject(M3Context.Current.User, entity).Execute(Session)
                    .Select(n => new
                    {
                        Type = "Note",
                        Date = n.DC,
                        Summary = n.Summary,
                        Link = "~/Contacts/EditNote/" + n.Id + "?entityId=" + id
                    });

                var files = new FileDao(Session).GetForObject(entity).Execute(Session)
                    .Select(f => new
                    {
                        Type = "File",
                        Date = f.File.DC,
                        Summary = f.File.OriginalFileName,
                        Link = "~/Resource/File/" + f.Id
                    });

                var combined = notes.Union(files);

                if (entity is Person)
                {
                    var person = entity as Person;

                    var employmentHistory = person.PersonCompanies
                        .Select(pc => new
                        {
                            Type = "Employment History",
                            Date = pc.StartDate,
                            Summary = pc.Company.Name,
                            Link = "~/Contacts/Person/EditEmployment/" + pc.Id
                        });

                    combined = combined.Union(employmentHistory);
                }
                else if (entity is Company)
                {
                    var company = entity as Company;

                    var employmentHistory = company.PersonCompanies
                        .Select(pc => new
                        {
                            Type = "Employment History",
                            Date = pc.StartDate,
                            Summary = pc.Person.Name,
                            Link = "~/Contacts/Profile/" + pc.Person.Id
                        });

                    combined = combined.Union(employmentHistory);
                }

                e.Grid.SetDataSource(combined);
            });

            return View();
        }
    }
}
