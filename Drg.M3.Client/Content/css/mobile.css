html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}
article, aside, details, figcaption, figure,footer, header, hgroup, menu, nav, section {display: block;}
ol,ul{list-style:none;}
blockquote,q{quotes:none;}
blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}
table{border-collapse:collapse;border-spacing:0;}

.clearFloat{clear:both;}
a{text-decoration:none;}

body{
	font-family:Arial, Helvetica, sans-serif;
}
/*
header
*/
header{
	font-size:12px;
	background:#fff;
}
.logo{
	padding:10px 10px;
}
.logo img{
	max-width:100%;
}
/*
content
*/
.content{
	font-size:12px;
	background:#fff;
	margin:0 0 20px 0;
}
.content h2{
	font-size:18px;
	font-weight:bold;
	text-shadow:0 0 5px #eee;
	letter-spacing:.5px;
	padding:5px 0 10px 0;
	margin:10px 15px 0 15px;
	border-bottom:1px dotted #eee;
}
.content h2,
.content h2 a{
	color:#5d5d5d;
}

/*
sidebar one
*/
div.one.sidebar{
	font-family:Arial, Helvetica, sans-serif;
	font-size:16px;
	margin:0 20px 20px 20px;
	z-index:1;
}
.one .widget{
	padding:0;
	-moz-border-radius:.5em;
	-webkit-border-radius:.5em;
	border-radius:.5em;
}
.one a{
	color:#7d7d7d;
}
.one .widget li{
	display:block;
	border:none;
	padding:0;
	margin:0;
}
.one .widget li a{
	padding:10px 10px 10px 10px;
	background-position:10px;
	margin:0;
}
/*
sidebar
*/
.sidebar{
	margin:20px 0 0 0;
}
.sidebar-one,
.sidebar-two{
	width:300px;
	float:left;
	margin:0 0 0 10px;
}
.widget{
	border:1px solid #eee;
	background:#ffffff;
	-moz-box-shadow:0 0 10px #f2f2f2;
	-webkit-box-shadow:0 0 10px #f2f2f2;
	box-shadow:0 0 10px #f2f2f2;
	margin:0 0 10px 0;
}
.widget a{
	display:block;
	color:#7d7d7d;
	padding:10px;
}
.widget a:hover{
	background-color:#f9f9f9;
}
.widget_archive{
	margin:0 10px 0 0;
	width:128px;
	float:left;
}
.widget .title{
	font-size:14px;
	font-weight:bold;
	color:#5d5d5d;
	text-transform:uppercase;
	padding:10px 0 10px 10px;
}

/*
footer
*/
footer{
	border-top:1px solid #eee;
	background:#fff;
	color:#7d7d7d;
}
footer ul{
}
footer li{
	float:left;
	display:inline-block;
	width:50%;
	text-align:center;
}
footer .title{
	background:#1d1d1d;
	color:#4d4d4d;
	padding:10px 0;
	text-transform:capitalize;
	font-weight:bold;
}
footer li a{
	display:block;
	padding:10px;
	color:#7d7d7d;
	border-right:1px solid #eee;
}
.copy{
	font-size:.8em;
	color:#bbb;
	padding:10px 0 10px 10px;
	background:#2d2d2d;
}
.copy a{
	color:#06f;
}



.form{
	font-size:12px;
	margin:10px 20px 0 20px;
}
.form label{
	font-weight:bold;
	color:#5d5d5d;
	padding-bottom:5px;
}
.form div{
	border-bottom:1px dashed #f2f2f2;
	padding:5px;
}
.form div:last-child{
	border-bottom:none;
}
.form span{
	display:block;
	color:#aaa;
}
.form input[type=text],
.form input[type=email],
.form input[type=password],
.form textarea,
.form select,
.form input[type=text].error,
.form input[type=email].error,
.form input[type=password].error,
.form select.error{
	padding:5px;
	width:200px;
	font-size:1em;
	margin:10px 0;
	border:1px solid #e5e5e5;
	-moz-box-shadow:0 0 5px #dcf5fe;
	-webkit-box-shadow:0 0 5px #dcf5fe;
	box-shadow:0 0 5px #dcf5fe;
	color:#5d5d5d;
}
.form input[type=submit]{
	display:block;
	padding:10px;
	font-size:1em;
	color:#7d7d7d;
	border:1px solid #ddd;
	cursor:pointer;
}
.select li a,
.form input[type=submit]{
	background: #ffffff;
	background: -moz-linear-gradient(top,  #ffffff 0%, #f2f2f2 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#f2f2f2));
	background: -webkit-linear-gradient(top,  #ffffff 0%,#f2f2f2 100%);
	background: -o-linear-gradient(top,  #ffffff 0%,#f2f2f2 100%);
	background: -ms-linear-gradient(top,  #ffffff 0%,#f2f2f2 100%);
	background: linear-gradient(top,  #ffffff 0%,#f2f2f2 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f2f2f2',GradientType=0 );
}
.form textarea{
	height:100px;
	width:200px;
}
.form .error{
	display:inline;
	font-size:12px;
	font-weight:normal;
	padding:10px 0 0 10px;
	color:#FF0000;
}
select.error{
	padding:0;
}

div.utilmenu
{
    float:right;
    color:#aaa;
    font-size:12px;
}
div.utilmenu>div
{
    margin-top:8px;
    font-size:12px;
}
div.utilmenu a
{
    text-decoration:underline;
}