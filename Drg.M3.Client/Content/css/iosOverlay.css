.ui-ios-overlay {
  z-index: 99999;
  position: fixed;
  top: 50%;
  left: 50%;
  width: 200px;
  height: 200px;
  margin-left: -100px;
  margin-top: -100px;
  filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#cc000000,endColorstr=#cc000000);
  background: rgba(0,0,0,0.8);
    -webkit-border-radius: 20px;
    -moz-border-radius: 20px;
    border-radius: 20px;
}
.ui-ios-overlay .title {
  color: #FFF;
  font-weight: bold;
  text-align: center;
  display: block;
  font-size: 26px;
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%;
}
.ui-ios-overlay img {
  display: block;
  margin: 20% auto 0 auto;
}
.ui-ios-overlay .spinner {
  left: 50% !important;
  top: 40% !important;
}

.ios-overlay-show {
  -webkit-animation-name: ios-overlay-show;
  -webkit-animation-duration: 750ms;
  -moz-animation-name: ios-overlay-show;
  -moz-animation-duration: 750ms;
  -ms-animation-name: ios-overlay-show;
  -ms-animation-duration: 750ms;
  -o-animation-name: ios-overlay-show;
  -o-animation-duration: 750ms;
  animation-name: ios-overlay-show;
  animation-duration: 750ms;
}

@-webkit-keyframes ios-overlay-show {
  0% { opacity: 0; }
  100% { opacity: 1; }
}
@-moz-keyframes ios-overlay-show {
  0% { opacity: 0; }
  100% { opacity: 1; }
}
@-ms-keyframes ios-overlay-show {
  0% { opacity: 0; }
  100% { opacity: 1; }
}
@-o-keyframes ios-overlay-show {
  0% { opacity: 0; }
  100% { opacity: 1; }
}
@keyframes ios-overlay-show {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.ios-overlay-hide {
  -webkit-animation-name: ios-overlay-hide;
  -webkit-animation-duration: 750ms;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-name: ios-overlay-hide;
  -moz-animation-duration: 750ms;
  -moz-animation-fill-mode: forwards;
  -ms-animation-name: ios-overlay-hide;
  -ms-animation-duration: 750ms;
  -ms-animation-fill-mode: forwards;
  -o-animation-name: ios-overlay-hide;
  -o-animation-duration: 750ms;
  -o-animation-fill-mode: forwards;
  animation-name: ios-overlay-hide;
  animation-duration: 750ms;
  animation-fill-mode: forwards;
}

@-webkit-keyframes ios-overlay-hide {
  0% { opacity: 1; }
  100% { opacity: 0; }
}
@-moz-keyframes ios-overlay-hide {
  0% { opacity: 1; }
  100% { opacity: 0; }
}
@-ms-keyframes ios-overlay-hide {
  0% { opacity: 1; }
  100% { opacity: 0; }
}
@-o-keyframes ios-overlay-hide {
  0% { opacity: 1; }
  100% { opacity: 0; }
}
@keyframes ios-overlay-hide {
  0% { opacity: 1; }
  100% { opacity: 0; }
}