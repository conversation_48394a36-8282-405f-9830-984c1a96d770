/* variables */
/* functions */
.utility ul,
.mainmenu ul,
.mainmenusub ul,
.utilmenu ul,
.grid ul:not(.qq-upload-list),
.PrintButton ul,
.page-header ul {
  padding: 0;
  margin-bottom: 10px;
}
th.griddly-select {
  width: 32px;
}
#stageBanner {
  margin: 10px 5px;
}
.page {
  width: 100%;
  position: relative;
  min-width: 950px;
}
.logo {
  position: absolute;
  top: 0;
  left: 0;
  width: 210px;
  height: 100px;
  padding: 10px;
  z-index: 1;
}
.logo a {
  display: block;
  width: 600px;
  height: 90px;
  background-repeat: no-repeat;
}
.utility {
  position: absolute;
  top: 0;
  right: 0;
  text-align: right;
  height: 85px;
  padding: 10px;
  z-index: 20;
  min-width: 500px;
  font-size: 11px;
  color: #333;
}
.utility .help {
  cursor: pointer;
  display: block;
  float: right;
  margin-left: 10px;
  background: url(data:image/png;base64,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) no-repeat;
  -o-background-size: 32px 32px;
  -moz-background-size: 32px 32px;
  -webkit-background-size: 32px 32px;
  background-size: 32px 32px;
  width: 32px;
  height: 32px;
}
.utility .links {
  height: 32px;
}
.utility .links a.unread {
  color: red;
  font-weight: bold;
}
.utility #org {
  margin-top: 10px;
  margin-right: 40px;
}
.utility #org div {
  text-align: left;
}
.utility #org input[type=text] {
  width: 300px;
  padding: 2px 3px;
  height: 26px;
}
.utility #org input[type=button] {
  border: none;
  font-weight: bold;
  margin-right: 0;
  font-size: 12px;
  color: #333;
}
.mainmenusub {
  position: relative;
  z-index: 10;
}
@media screen {
  .body-container {
    position: absolute;
    top: 90px;
    width: 100%;
  }
}
.utilmenu {
  float: right;
  top: 0;
  left: 0;
  width: 220px;
  padding: 3px;
  margin: 5px 10px;
  position: relative;
  font-size: 12px;
}
.mobile .utilmenu {
  z-index: 1;
}
.utilmenu .subsection {
  position: relative;
  margin-bottom: 7px;
}
.utilmenu .subsection .panel-heading {
  padding-top: 3px;
  padding-bottom: 3px;
}
.utilmenu .subsection .panel-heading .title {
  font-weight: bold;
  white-space: nowrap;
  margin-right: 50px;
  overflow-x: hidden;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.utilmenu .links {
  font-style: italic;
  color: gray;
  display: inline-block;
  float: right;
}
.utilmenu .links a {
  color: gray;
}
.utilmenu ul {
  list-style: none;
  margin-bottom: 0;
}
.sidebar-collapsed .utilmenu {
  display: none;
}
/* notes */
.utilmenu .notes .note {
  margin: 7px 0;
  padding-top: 7px;
  border-top: solid 1px #ccc;
}
.utilmenu .notes .meta {
  text-align: right;
  color: #888;
  padding-top: 4px;
  font-size: 0.95em;
}
.utilmenu .notes #side-note {
  margin-top: 7px;
  text-align: right;
}
.utilmenu .notes #side-note-inprogress {
  padding-top: 15px;
  text-align: left;
}
.utilmenu .notes #side-note-form {
  display: none;
  text-align: left;
  margin-bottom: 10px;
}
.utilmenu .notes #side-note-form input {
  margin-bottom: 2px;
  width: 100%;
}
.utilmenu .notes #side-note-form textarea {
  height: 40px;
  width: 100%;
}
.utilmenu .notes #side-note-recent {
  clear: both;
}
/* side search */
.main {
  /*z-index:5;*/
  position: absolute;
  left: 10px;
  right: 240px;
  font-size: 12px;
}
.sidebar-collapsed .main {
  right: 10px;
}
.hazwaste .sidebar-collapsed .main {
  left: 10px;
}
.sidebar-expand {
  display: none;
}
.sidebar-collapsed .sidebar-expand,
.sidebar-collapse {
  display: block;
  float: right;
}
.sidebar-collapsed .sidebar-collapse {
  display: none;
}
.footer {
  font-size: 11px;
  clear: both;
  text-align: center;
  line-height: normal;
  padding: 0 10px;
  margin-top: 30px;
  margin-bottom: 35px;
}
.footer div.inner {
  padding: 5px 0;
}
.footer p {
  margin: 0;
}
/* Menus */
.mainmenu {
  padding-left: 12px;
  background: #627eba;
  /* IE8/IE9 */
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#627eba', EndColorStr='#485d7c')";
  background-image: linear-gradient(#627eba, #485d7c);
  height: 36px;
}
.mainmenu ul.menu li,
.mainmenusub ul.submenu li {
  display: inline;
  list-style: none;
  float: left;
}
.mainmenu ul.menu li {
  margin: 5px 1px;
  text-align: center;
}
.mainmenu ul.menu li a {
  color: white;
  font: 16px Georgia, 'Times New Roman', Serif;
  cursor: pointer;
  display: block;
  padding: 3px 12px;
  z-index: 2;
}
.mainmenu ul.menu li.sel {
  margin: 0 1px 0 1px;
}
.mainmenu ul.menu li.sel a {
  background-color: #8394be;
  box-shadow: 0 0 6px -2px #000000;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  height: 32px;
  padding-top: 5px;
  margin-top: 4px;
  background-image: linear-gradient(#8394be, #e2e7ed);
  color: white;
  text-shadow: gray 0.1em 0.1em 0.2em;
}
.mainmenusub {
  color: #737373;
  font: 14px Georgia, 'Times New Roman', Serif;
  background: #e2e7ed;
  border-bottom: 1px black;
  box-shadow: 0 0 2px transparent;
  margin-top: -10px;
}
.mainmenusub ul.submenu {
  padding-left: 15px;
}
.mainmenusub ul.submenu li.submenu {
  position: relative;
}
.mainmenusub ul.submenu li {
  display: block;
  float: left;
}
.mainmenusub ul.submenu li a {
  cursor: pointer;
  position: relative;
  padding: 4px 10px;
  line-height: 22px;
  display: block;
  color: #737373;
}
.mainmenusub ul.submenu li a:hover {
  text-decoration: underline;
}
.mainmenusub ul.submenu li.sel a.submenu {
  font-weight: bold;
  /*background-color: rgb(207, 207, 27);*/
  background-image: linear-gradient(#c1c12b, #dcdc15);
  border-radius: 5px;
  margin: 4px;
  padding: 0 10px;
  box-shadow: 0 0 6px -2px gray;
  text-shadow: gray 0.1em 0.1em 0.2em;
  color: white;
}
.mainmenusub ul.dropdownmenu {
  display: none;
  list-style-type: none;
  margin: 0;
  top: 28px;
  position: absolute;
  background-color: white;
  z-index: 10;
  box-shadow: 0 0 3px -1px black;
  border-radius: 3px;
  font: 12px Arial;
}
.mainmenusub ul.dropdownmenu li.dropdownmenu {
  display: block;
  float: none;
  border: none;
  white-space: nowrap;
}
.mainmenusub ul.dropdownmenu li.dropdownmenu.sel a,
.mainmenusub ul.dropdownmenu li.dropdownmenu a,
.mainmenusub ul.dropdownmenu li.dropdownmenu a:hover {
  color: #737373;
}
.mainmenusub ul.dropdownmenu li.dropdownmenu.sel a {
  font-weight: bold;
}
/* header */
.breadcrumb {
  font-size: 11px;
  margin-top: 9px;
}
h1 {
  font-size: 23px;
  margin-bottom: 0;
}
#profileIcon {
  margin-top: -18px;
  margin-right: 5px;
  float: left;
}
#profileIcon a {
  padding: 5px;
}
.page-header {
  border-bottom-width: 2px;
  margin-top: 25px;
  margin-bottom: 10px;
}
/* Images */
.icon16 {
  width: 16px;
  height: 16px;
}
.icon20 {
  width: 20px;
  height: 20px;
}
.icon24 {
  width: 24px;
  height: 24px;
}
.icon28 {
  width: 28px;
  height: 28px;
}
.icon32 {
  width: 32px;
  height: 32px;
}
.icon16,
.icon20,
.icon24,
.icon28,
.icon32 {
  overflow: hidden;
  display: inline-block;
  vertical-align: middle;
  position: relative;
}
.icon16 img,
.icon20 img,
.icon24 img,
.icon28 img,
.icon32 img {
  left: 0;
  position: absolute;
  display: none;
}
.icon16.checkbox,
.icon20.checkbox,
.icon16.table,
.icon20.table {
  min-height: 0;
  margin: 0;
  padding: 0;
}
/*Hack to fix glyphicon for on CNIC network which does not allow font download*/
.glyphicon-ok:before,
.glyphicon-record:before,
.glyphicon-minus:before,
.glyphicon-plus:before,
.glyphicon-home:before,
.glyphicon-stats:before,
.glyphicon-check:before,
.glyphicon-unchecked:before,
.glyphicon-ban-circle:before,
.glyphicon-remove:before,
.glyphicon-calendar:before {
  content: "" !important;
}
.glyphicon {
  width: 16px!important;
  height: 16px!important;
}
.glimpse-nowrap {
  z-index: 7;
}
.glimpse-icon {
  display: none;
}
.glimpse-toggle {
  position: fixed;
  right: 0;
  background-color: #3c454f;
  padding: 10px;
  z-index: 6;
}
.glimpse-toggle a {
  color: white;
}
.glimpse-toggle a.enable:hover::after {
  content: " Enable statistics";
}
@media print {
  .glimpse {
    display: none;
  }
}
.glyphicon-ok,
.glyphicon-record {
  background-image: url(../images/icons/16/check.png);
}
.glyphicon-check {
  background-image: url(../images/icons/16/checkbox.png);
}
.glyphicon-unchecked {
  background-image: url(../images/icons/16/checkbox_unchecked.png);
}
.glyphicon-minus {
  background-image: url(../images/icons/16/navigate_down.png);
}
.glyphicon-plus {
  background-image: url(../images/icons/16/navigate_right.png);
}
.glyphicon-home {
  background-image: url(../images/icons/16/house.png);
}
.glyphicon-stats {
  background-image: url(../images/icons/16/office_building.png);
}
.glyphicon-ban-circle {
  background-image: url(../images/icons/16/sign_forbidden.png);
}
.glyphicon-remove {
  background-image: url(../images/icons/16/delete.png);
}
.glyphicon-calendar {
  background-image: url(../images/icons/16/calendar.png);
}
.editly-editable {
  background-color: #c8ffc8 !important;
}
/*Haz-waste specific styles*/
body.hazwaste .main {
  right: 10px;
  left: 260px;
}
body.hazwaste .utilmenu {
  float: left;
  width: 240px;
}
html {
  -ms-overflow-style: scrollbar;
}
.pre-wrap {
  white-space: pre-wrap;
}
.card-label {
  font-size: 0.9em;
  color: #999;
  font-weight: bold;
  line-height: 1.5;
}
.card-label::content {
  vertical-align: -webkit-baseline-middle;
}
.panel-heading {
  padding: 5px 15px;
}
.tooltip .tooltip-inner {
  max-width: 300px !important;
}
.spacer-16,
.treeview span.icon.spacer-16 {
  display: inline-block;
  width: 16px;
}
.treeview svg.icon {
  width: 12px;
  margin-right: 5px;
}
/*Bootstrap 4 Toast back-port*/
.toast {
  max-width: 350px;
  overflow: hidden;
  /*font-size: 0.875rem;*/
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  opacity: 0;
  border-radius: 0.25rem;
}
.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}
.toast.showing {
  opacity: 1;
}
.toast.show {
  display: block;
  opacity: 1;
}
.toast.hide {
  display: none;
}
.toast-header {
  display: flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #888;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.toast-body {
  padding: 0.75rem;
}
.m-2 {
  margin: 0.5rem !important;
}
.mr-auto {
  margin-right: auto;
}
.input-group-range .input-group-addon {
  border-left: none;
  border-right: none;
}
.form-control {
  font-size: inherit;
}
/* legacy forms */
.form .form-control {
  display: inline-block;
}
/* General Forms */
img.datepick-trigger {
  margin-left: -24px;
  margin-right: 6px;
}
.filterForm label,
.form label {
  width: 200px;
  margin-top: 2px;
  margin-bottom: 2px;
  line-height: 25px;
  float: left;
}
.filterForm label:not(.checkbox-inline)::after,
.form label:not(.checkbox-inline)::after {
  content: ":";
}
.filterForm input + label::after,
.form input + label::after,
.filterForm label:empty::after,
.form label:empty::after,
.filterForm div.checkbox label::after,
.form div.checkbox label::after,
form.quiz .form label::after,
.form .errors label.invalid::after,
.filterForm .errors label.invalid::after {
  content: none!important;
}
.filterForm .multiselect-container label,
.form .multiselect-container label {
  float: none;
}
.filterForm label span,
.form label span {
  margin-left: 2px;
}
.filterForm input + label,
.form input + label {
  line-height: normal;
  width: auto;
  margin-left: 4px;
  float: none;
}
.filterForm label + div,
.form label + div {
  padding-top: 4px;
  padding-left: 4px;
}
.filterForm input.date.is-datepick,
.form input.date.is-datepick {
  background-image: url(../images/calendar.png);
  background-repeat: no-repeat;
  background-position: 225px center;
}
.filterForm input.date.is-datepick.small,
.form input.date.is-datepick.small {
  background-position-x: 90px;
}
#quicksearchbox input[type=text]:not(.form-control),
table.grid input[type="text"]:not(.form-control):not(.pageNumber),
table.grid textarea:not(.form-control),
table.grid select:not(.form-control),
div.griddly .griddly-filters-form input[type="text"]:not(.form-control):not(.pageNumber),
div.griddly .griddly-filters-form textarea:not(.form-control),
div.griddly .griddly-filters-form select:not(.form-control),
.form input[type=text],
.form input[type=number],
.form input[type=tel],
.form input[type=email],
.form input[type=password],
.form textarea,
.form select {
  width: 245px;
  padding: 2px 3px;
  height: 26px;
  border-radius: 3px;
  border: 1px solid #aaa;
  color: #555555;
}
#quicksearchbox input[type=text]:not(.form-control)[disabled],
table.grid input[type="text"]:not(.form-control):not(.pageNumber)[disabled],
table.grid textarea:not(.form-control)[disabled],
table.grid select:not(.form-control)[disabled],
div.griddly .griddly-filters-form input[type="text"]:not(.form-control):not(.pageNumber)[disabled],
div.griddly .griddly-filters-form textarea:not(.form-control)[disabled],
div.griddly .griddly-filters-form select:not(.form-control)[disabled],
.form input[type=text][disabled],
.form input[type=number][disabled],
.form input[type=tel][disabled],
.form input[type=email][disabled],
.form input[type=password][disabled],
.form textarea[disabled],
.form select[disabled],
#quicksearchbox input[type=text]:not(.form-control)[readonly]:not(.selectFilter):not(.ddFilter),
table.grid input[type="text"]:not(.form-control):not(.pageNumber)[readonly]:not(.selectFilter):not(.ddFilter),
table.grid textarea:not(.form-control)[readonly]:not(.selectFilter):not(.ddFilter),
table.grid select:not(.form-control)[readonly]:not(.selectFilter):not(.ddFilter),
div.griddly .griddly-filters-form input[type="text"]:not(.form-control):not(.pageNumber)[readonly]:not(.selectFilter):not(.ddFilter),
div.griddly .griddly-filters-form textarea:not(.form-control)[readonly]:not(.selectFilter):not(.ddFilter),
div.griddly .griddly-filters-form select:not(.form-control)[readonly]:not(.selectFilter):not(.ddFilter),
.form input[type=text][readonly]:not(.selectFilter):not(.ddFilter),
.form input[type=number][readonly]:not(.selectFilter):not(.ddFilter),
.form input[type=tel][readonly]:not(.selectFilter):not(.ddFilter),
.form input[type=email][readonly]:not(.selectFilter):not(.ddFilter),
.form input[type=password][readonly]:not(.selectFilter):not(.ddFilter),
.form textarea[readonly]:not(.selectFilter):not(.ddFilter),
.form select[readonly]:not(.selectFilter):not(.ddFilter),
fieldset[disabled] #quicksearchbox input[type=text]:not(.form-control),
fieldset[disabled] table.grid input[type="text"]:not(.form-control):not(.pageNumber),
fieldset[disabled] table.grid textarea:not(.form-control),
fieldset[disabled] table.grid select:not(.form-control),
fieldset[disabled] div.griddly .griddly-filters-form input[type="text"]:not(.form-control):not(.pageNumber),
fieldset[disabled] div.griddly .griddly-filters-form textarea:not(.form-control),
fieldset[disabled] div.griddly .griddly-filters-form select:not(.form-control),
fieldset[disabled] .form input[type=text],
fieldset[disabled] .form input[type=number],
fieldset[disabled] .form input[type=tel],
fieldset[disabled] .form input[type=email],
fieldset[disabled] .form input[type=password],
fieldset[disabled] .form textarea,
fieldset[disabled] .form select {
  cursor: not-allowed;
  background-color: #e1e1e1;
  opacity: 1;
}
table.grid input.selectFilter,
div.griddly .griddly-filters-form input.selectFilter,
.form input.selectFilter,
table.grid input.ddFilter,
div.griddly .griddly-filters-form input.ddFilter,
.form input.ddFilter {
  width: 245px;
  padding: 2px 3px;
  height: 26px;
  border-radius: 3px;
  border: 1px solid #aaa;
}
table.grid input.selectFilter[disabled],
div.griddly .griddly-filters-form input.selectFilter[disabled],
.form input.selectFilter[disabled],
table.grid input.ddFilter[disabled],
div.griddly .griddly-filters-form input.ddFilter[disabled],
.form input.ddFilter[disabled],
table.grid input.selectFilter[readonly],
div.griddly .griddly-filters-form input.selectFilter[readonly],
.form input.selectFilter[readonly],
table.grid input.ddFilter[readonly],
div.griddly .griddly-filters-form input.ddFilter[readonly],
.form input.ddFilter[readonly],
fieldset[disabled] table.grid input.selectFilter,
fieldset[disabled] div.griddly .griddly-filters-form input.selectFilter,
fieldset[disabled] .form input.selectFilter,
fieldset[disabled] table.grid input.ddFilter,
fieldset[disabled] div.griddly .griddly-filters-form input.ddFilter,
fieldset[disabled] .form input.ddFilter {
  cursor: auto;
  background-color: inherit;
}
/* legacy input focus styles to match bootstrap focus */
#quicksearchbox input:focus,
table.grid input:focus,
table.grid textarea:focus,
table.grid select:focus,
div.griddly .griddly-filters-form input:focus,
div.griddly .griddly-filters-form textarea:focus,
div.griddly .griddly-filters-form select:focus,
.form input:focus,
.form textarea:focus,
.form select:focus,
.selectFilter input:focus {
  border-color: #66afe9;
  outline: 0;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form textarea {
  width: 400px;
  height: 80px;
}
table.grid input.small[type=text]:not(.form-control):not(.pageNumber),
table.grid select.small:not(.form-control),
div.griddly .griddly-filters-form input.small[type=text]:not(.form-control):not(.pageNumber),
div.griddly .griddly-filters-form select.small:not(.form-control),
.form input.small[type=text]:not(.form-control),
.form select.small:not(.form-control) {
  width: 110px;
  font-size: 100%;
}
/* checkboxes */
.form input[type=checkbox],
.form input[type=radio] {
  float: left;
  clear: left;
  margin-top: 3px;
  margin-bottom: 2px;
}
.form .checkbox-inline input {
  margin-top: 6px;
}
.form input[type=checkbox] + label,
.form input[type=radio] + label {
  float: left;
  clear: right;
  margin-top: 3px;
  margin-bottom: 2px;
}
.form .upload-button,
.form input,
.form textarea,
.form select,
.distribution-view {
  margin: 2px 4px 2px 0;
}
.form select {
  padding-left: 0;
}
.form .address {
  padding-bottom: 5px;
}
.form .address .edit,
.form .address .read {
  margin-left: 200px;
}
.form .address .read {
  padding-left: 4px;
}
.form .address .read.select {
  padding-top: 0;
  padding-left: 0;
  margin-left: 200px;
}
.form .address .read.select .edit {
  margin-left: 0;
}
.form input.city {
  width: 139px;
  margin-right: 0;
}
.form input.state {
  width: 40px;
  margin-right: 0;
}
.form input.zip {
  width: 60px;
}
/* General Form Layout */
.form {
  border: solid 1px #aaa;
  border-radius: 5px;
  padding: 10px 30px;
}
.form div.top {
  height: 28px;
  background: #627eba;
  /* IE8/IE9 */
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#627eba', EndColorStr='#485d7c')";
  background-image: linear-gradient(#627eba, #485d7c);
  font-weight: bolder;
  font-size: 13px;
  color: white;
  padding: 3px 8px;
  margin: -10px -30px 30px -30px;
  border-radius: 4px 4px 0 0;
}
.form div.category {
  margin-left: -30px;
  margin-top: 20px;
  text-align: right;
  font-weight: bold;
  font-size: 14px;
  border-bottom: 2px solid #485d7c;
  width: 200px;
}
.form div.group {
  overflow: auto;
  margin: 10px 0;
}
.filterForm > div:not([hidden]),
.form div.group > div:not([hidden]) {
  display: block;
  overflow: auto;
  clear: both;
}
.form div.group label + div {
  /*overflow:auto; -JH: Add more specifically if needed! Messes up select filters*/
  padding-top: 10px;
  padding-bottom: 7px;
}
.form div.group div.form-group {
  margin-bottom: 0;
}
.form div.group .info {
  padding-top: 5px;
  overflow: auto;
}
.filterForm div.buttons,
.form div.buttons {
  padding: 10px 0;
}
/* this style is designed to match the "btn btn-sm btn-primary" bootstrap style */
.filterForm div.buttons input,
.form div.buttons input {
  padding: 5px 10px;
  border: 1px solid #357ebd;
  border-radius: 3px;
  font-size: 12px;
  background-color: #428bca;
  color: white;
  vertical-align: middle;
  height: 30px;
}
div.buttons .btn {
  border-radius: 3px;
}
.filterForm div.buttons input:hover,
.form div.buttons input:hover {
  border-color: #285e8e;
  background-color: #3276b1;
  color: white;
}
.filterForm div.buttons input:active,
.form div.buttons input:active {
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  outline: 0;
}
.form .errors,
form .panel .errors {
  color: red;
  margin-left: 40px;
  display: block;
}
.form .errors label,
form .panel .errors label {
  width: auto;
  float: none;
}
#quicksearchbox form {
  border: 0;
  padding: 0;
}
#quicksearchbox form input[type=text] {
  width: 148px;
  margin: 0;
  display: inline;
  float: left;
}
#quicksearchbox form input[type=submit] {
  padding: 3px 6px;
  margin: 0;
  margin-left: 3px;
  float: left;
}
/* GRID Form */
.griddly-filters form,
.filterForm {
  padding: 10px 20px;
}
table.grid input.quickSearch {
  margin-top: -2px;
  margin-right: 1px;
  margin-bottom: -2px;
  padding: 1px 4px 1px 4px;
  height: 24px;
}
.griddly-filter-popover {
  z-index: 1100;
}
.griddly-filter-popover .popover-content {
  padding: 0;
  max-width: 200px;
  width: 200px;
}
.filter-content {
  padding: 0;
  display: none;
}
.filter-content .input-group,
.filter-content .dropdown-menu {
  padding: 4px;
}
.filter-content .dropdown-menu {
  display: block;
  border: none;
  box-shadow: none;
  position: relative;
  margin: 0;
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
  top: auto;
}
.filter-content .dropdown-menu li a {
  padding: 3px 6px;
  white-space: normal;
}
.filter-content .dropdown-menu li a input {
  display: none;
}
.filter-content .griddly-filter-buttons {
  margin-right: -1px;
}
.filter-content .griddly-select-all,
.filter-content .griddly-clear {
  width: 100%;
}
.griddly-filter-selected-indicator {
  float: right;
  display: none;
}
.griddly-filter-selected .griddly-filter-selected-indicator {
  display: block;
}
.multiselect-container label {
  padding: 0;
  margin: 0;
  font-weight: normal;
}
.multiselect-container label.radio {
  margin-left: 10px;
  cursor: pointer;
}
.multiselect-container input[type=checkbox] {
  display: none;
}
.nav.nav-pills {
  padding: 5px;
  background-color: #eee;
}
.modal-footer {
  background-color: #fafafa;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
.modal-body:empty {
  display: none;
}
.modal-body:empty + .modal-footer {
  border-top: none;
  margin-top: 0;
}
.panel-container {
  margin-top: 20px;
  overflow: hidden;
}
.panel-container .panel {
  float: left;
  min-width: 300px;
  margin-right: 20px;
}
.panel-container dl dt {
  width: 120px;
}
.panel-container dl dd {
  margin-left: 120px;
}
.panel-container .panel-body dl {
  margin: 0;
}
.panel-container dl.dl-horizontal dt {
  text-align: left;
}
.panel-container .panel-heading {
  font-weight: bold;
  padding: 8px 12px !important;
}
.panel-container .panel-heading .btn.pull-right,
.panel-container .panel-heading .pull-right .btn {
  font-weight: normal;
  margin-top: -2px;
  margin-right: -6px;
}
#batchUpdateCounter {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 5px 10px;
  display: none;
  z-index: 1000;
}
.bootbox-error pre {
  width: 850px;
}
.table td .glyphicon {
  top: -2px;
}
form.form-horizontal div.form div.group {
  overflow: hidden;
}
.form-actions {
  padding: 19px 20px 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  margin-right: 0;
  background-color: #f5f5f5;
  border-top: 1px solid #e5e5e5;
}
.top-space {
  margin-top: 15px;
}
.table thead th {
  background-color: #eee;
  border-bottom-width: 1px !important;
}
.table th.deemphasize {
  color: #666;
}
.table td.deemphasize {
  color: #999;
}
tr.warning.warning-hash {
  background: transparent url(../images/back_red_stripes.png) 0 0;
}
tr.warning.warning-hash td {
  background-color: transparent !important ;
}
.checkTree {
  margin-bottom: 20px;
}
/* ROLE SECTIONS
----------------------------------------------------------*/
.roles {
  /*margin-top:15px;
    padding-top:5px;
    border-top:solid 1px #666;*/
}
.rolesection {
  width: 320px;
}
.rolesection div {
  overflow: auto;
  margin: 7px 14px 7px 0;
  padding-bottom: 3px;
  background-color: #f0f0f0;
  margin-bottom: 10px;
  border-radius: 3px;
}
.rolesection div h3 {
  font-size: 18px;
  border-bottom: solid 1px #777;
  color: #333;
  font-weight: bold;
  margin: 10px;
  margin-top: 5px;
  padding: 5px 0;
}
.rolesection div p {
  margin: 10px 15px;
}
.rolesection div li {
  color: #333;
  display: block;
  border: solid 2px #ccd;
  border-radius: 3px;
  margin: 10px 15px;
  padding: 10px;
  font-size: large;
}
.rolesection div li a {
  display: block;
  float: right;
  margin: 5px 3px;
  font-size: small;
  padding-top: 6px;
}
.rolesection div li:hover {
  background-color: #fff;
}
.rolesection div li a.added {
  text-decoration: none;
}
.rolesection div li a.added span {
  display: none;
}
.rolesection div li span.icon32 {
  vertical-align: bottom;
  width: 32px;
  height: 32px;
}
/* DRILL DOWN FILTER
----------------------------------------------------------*/
div.ddFilter .row .navigate {
  background-image: url(../images/selectfilter/flyright.png);
  background-repeat: no-repeat;
  background-position: right;
  width: 16px;
  height: 16px;
  padding: 0;
  margin: 0;
  cursor: pointer;
}
div.ddFilter .row.sel .navigate {
  background-image: url(../images/selectfilter/flyright_sel.png);
}
div.ddFilter .row .navigate:hover {
  background-image: url(../images/selectfilter/flyright_hover.png);
}
div.ddFilter .breadCrumbs {
  margin-left: 2px;
  overflow: hidden;
  /*white-space:nowrap;*/
}
div.ddFilter .breadCrumbs a {
  cursor: pointer;
}
/* SELECT FILTER
----------------------------------------------------------*/
input[type=text].selectFilter,
input.text.selectFilter,
input[type=text].ddFilter,
input.text.ddFilter {
  cursor: default;
  background-image: url(../images/selectfilter/dropdown.gif);
  background-repeat: no-repeat;
  background-position: right center;
  padding-right: 18px;
  background-color: inherit;
}
input[type=text].selectFilter:hover,
input.text.selectFilter:hover,
input[type=text].ddFilter:hover,
input.text.ddFilter:hover {
  background-image: url(../images/selectfilter/dropdown_hover.gif);
}
input[type=button].selectFilter,
input.button.selectFilter,
input[type=button].ddFilter,
input.button.ddFilter {
  color: Black;
  cursor: pointer;
  border: solid 1px #aaaaae;
  border-radius: 3px;
  padding: 1px;
  padding-right: 16px;
  height: 28px;
  line-height: 16px;
  background-image: url(../images/selectfilter/dropdown.gif);
  background-repeat: no-repeat;
  background-position: right center;
  text-align: left;
}
input[type=button].selectFilter:hover,
input.button.selectFilter:hover,
input[type=button].selectFilter.disabled,
input.button.selectFilter.disabled,
input[type=button].ddFilter:hover,
input.button.ddFilter:hover,
input[type=button].ddFilter.disabled,
input.button.ddFilter.disabled {
  background-image: url(../images/selectfilter/dropdown_hover.gif);
  outline: none;
  border: solid 1px #777;
}
input.selectFilter[readonly],
input.ddFilter[readonly],
.form input.selectFilter[readonly],
.form input.ddFilter[readonly] {
  background-color: #fff;
}
input.selectFilter[disabled],
input.selectFilter.disabled,
input.ddFilter[disabled],
input.ddFilter.disabled {
  background-color: #fff;
  color: #000 !important;
}
div.selectFilter,
div.ddFilter {
  margin-top: 4px;
  z-index: 9999;
  border-radius: 3px;
  background-color: White;
  border: solid 1px #88888e;
  max-width: 600px;
  min-width: 245px;
}
div.selectFilter input[type=text],
div.ddFilter input[type=text] {
  background-image: url(../images/selectfilter/filter.png);
  background-repeat: no-repeat;
  background-position: right center;
  width: 200px;
  margin: 0;
  outline: none;
  border: none;
  border-bottom: solid 1px #ccc;
  border-radius: 0;
}
.input-group-sm > .input-group-addon {
  padding: 3px 10px!important;
}
div.selectFilter div.loading,
div.ddFilter div.loading {
  color: #777;
  padding: 2px 5px;
  font-style: italic;
}
div.selectFilter div.empty,
div.ddFilter div.empty {
  color: #333;
  text-align: center;
  padding: 2px 5px;
  font-style: italic;
}
div.selectFilter div.clear,
div.ddFilter div.clear {
  text-align: center;
  padding: 2px 5px;
}
div.selectFilter div.selectAll,
div.ddFilter div.selectAll {
  text-align: center;
  padding: 2px 5px;
}
div.selectFilter ul {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
}
div.selectFilter .row,
div.ddFilter .row {
  display: block;
  margin: 0;
  padding: 1px 4px;
  cursor: pointer;
}
div.selectFilter .row.sel,
div.ddFilter .row.sel {
  background-color: #39f;
  color: #fff;
}
div.selectFilter table,
div.ddFilter table {
  /*width:100%;*/
  border-spacing: 0;
  overflow-x: hidden;
  overflow-y: auto;
  border: none;
}
div.selectFilter tr.row,
div.ddFilter tr.row {
  display: table-row;
}
div.selectFilter tr.row td,
div.ddFilter tr.row td {
  padding: 2px 4px;
  color: Gray;
  border: none;
}
div.selectFilter tr.row.sel td,
div.ddFilter tr.row.sel td {
  color: #222;
}
div.selectFilter tr.row td.first,
div.ddFilter tr.row td.first {
  color: Black;
}
div.selectFilter tr.row.sel td.first,
div.ddFilter tr.row.sel td.first {
  color: White;
}
div.selectFilter.multiple .row .checkbox,
div.ddFilter.multiple .row .checkbox {
  background-image: url(../images/selectfilter/unchecked.png);
  background-repeat: no-repeat;
  background-position: 4px 4px;
  width: 18px;
  height: 16px;
  margin: -1px 0;
  margin-left: -4px;
  float: left;
}
div.selectFilter.multiple .row.checked .checkbox,
div.ddFilter.multiple .row.checked .checkbox {
  background-image: url(../images/selectfilter/checked.png);
}
div.selectFilter.multiple div.apply,
div.ddFilter.multiple div.apply {
  margin: 3px;
  margin-bottom: 0;
  padding: 2px;
  border-top: solid 1px #ccc;
}
div.selectFilter.multiple div.apply a,
div.ddFilter.multiple div.apply a {
  cursor: pointer;
  display: block;
  padding: 2px 5px;
}
div.selectFilter.multiple div.apply a:hover,
div.ddFilter.multiple div.apply a:hover {
  border-radius: 3px;
  background-color: #ccc;
  color: #333;
}
/* PROFILE MENU 
----------------------------------------------------------*/
#profileMenu.profileMenuCategories {
  min-width: 450px;
  overflow: hidden;
}
#profileMenu.profileMenuNoCategories {
  padding: 10px;
  min-width: 350px;
}
#profileMenu ul {
  margin: 0;
}
#profileMenu li {
  list-style: none;
  padding: 3px 6px;
  border-radius: 3px;
  margin: 3px 0;
}
#profileMenu li.sel {
  font-weight: bold;
}
#profileMenu .profileMenuCategories {
  width: 180px;
  border-right: 1px solid lightgray;
  margin-left: 10px;
  cursor: default;
}
#profileMenu .profileMenuItem {
  width: 175px;
}
#profileMenu .profileMenuItem.activeProfileSubMenu {
  background-color: lightgray;
  padding: 3px 6px;
  border-radius: 3px;
  margin: 3px 0;
}
#profileMenu .profileSubMenu {
  float: right;
  position: absolute;
  left: 190px;
  display: none;
  top: 0;
  padding: 10px;
}
/* PROFILE CHANGER
----------------------------------------------------------*/
.viewChanger {
  /*clear: both;*/
  margin-bottom: 10px;
}
.viewChanger > span {
  display: block;
}
.viewChanger > span,
ul.nav.nav-pills,
ul.nav.nav-tabs {
  line-height: 20px;
  border: solid 1px #ccc;
  background-color: white;
  border-radius: 18px;
  width: fit-content;
  padding: 3px;
}
.viewChanger span a,
.viewChanger span label,
ul.nav.nav-pills li a,
ul.nav.nav-tabs li a {
  padding: 5px 15px;
  display: inline-block;
  border-radius: 14px;
  cursor: pointer;
  margin: 0;
  font-weight: normal;
}
.viewChanger span a:hover,
ul.nav.nav-pills li a:hover,
ul.nav.nav-tabs li a:hover {
  text-decoration: none;
  background-color: #eee;
}
.viewChanger span a.sel,
ul.nav.nav-pills li.active a,
ul.nav.nav-tabs li.active a,
ul.nav.nav-pills li a.active,
ul.nav.nav-tabs li a.active {
  font-weight: bold;
  color: #555;
  background-color: #ddd;
}
/* RECORD MENU
----------------------------------------------------------*/
div.recordmenu {
  font-size: 12px;
  padding: 10px;
  width: 400px;
}
div.recordmenu ul {
  margin: 0;
}
div.recordmenu li {
  list-style: none;
  padding: 3px 6px;
  border-radius: 3px;
  margin: 3px 0;
}
div.recordmenu a {
  padding-left: 10px;
}
ul.recordmenu li.sel a {
  font-weight: bold;
}
div.recordmenu li.category {
  cursor: pointer;
}
div.recordmenu li.category:hover {
  background-color: #eee;
}
div.recordmenu li.category:hover:after {
  content: "";
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #444;
  display: block;
  position: relative;
  top: 7px;
  float: right;
}
div.recordmenu li.category.slid {
  background-color: #eee;
}
div.recordmenu li.category.slid:after {
  border: none;
  display: none;
}
/* PRINT BUTTON EMAIL FORM
----------------------------------------------------------*/
div.printButton_form {
  padding: 1em;
}
.printButton_form select {
  width: 228px;
}
.printButton_form input[type=text] {
  width: 220px;
  clear: both;
}
.printButton_form input[type=submit],
.printButton_form input[type=reset] {
  margin-left: 10px;
}
/* MULTISELECT
----------------------------------------------------------*/
.multiSelect {
  width: 182px;
  border: solid 1px #BBB;
  background: #FFF url(../images/multiselect/dropdown.gif) right center no-repeat;
  padding: 2px 4px;
  padding-right: 20px;
  display: inline;
}
.multiSelect.hover {
  background: url(../images/multiselect/dropdown_hover.gif) right center no-repeat;
}
.multiSelect.active,
.multiSelect.focus {
  /*border: inset 1px #000;*/
}
.multiSelect.active {
  background: url(../images/multiselect/dropdown_active.gif) right center no-repeat;
}
input.multiSelect {
  width: 243px !important;
}
.multiSelectOptions {
  width: 248px;
  max-height: 150px;
  margin-top: -1px;
  overflow-y: auto;
  overflow-x: hidden;
  border: solid 1px #B2B2B2;
  background: #FFF;
}
.multiSelectOptions LABEL {
  padding: 2px 5px;
  display: block;
  width: 248px !important;
}
.multiSelectOptions LABEL.checked {
  background: #E6E6E6;
}
.multiSelectOptions LABEL.selectAll {
  border-bottom: dotted 2px #CCC;
}
.multiSelectOptions LABEL.hover {
  background: #CFCFCF;
}
/* GRID
----------------------------------------------------------*/
form .group.grid {
  overflow: visible;
}
table.grid tbody.data td span.highlight {
  color: Blue;
}
table.grid {
  margin-bottom: 20px;
  color: Black;
  width: 100%;
  border-collapse: collapse;
  background-color: White;
}
.griddly table.grid {
  margin-bottom: 0;
}
.griddly .griddly-selection {
  white-space: nowrap;
  vertical-align: top;
  font-weight: normal;
}
.griddly .griddly-selection:before {
  content: " - ";
}
.griddly .griddly-selection a {
  color: #ccc;
}
.griddly .griddly-selection-count {
  margin: 0;
  font-weight: bold;
}
.griddly .manage,
.pagebuttons .manage,
.grid .manage {
  color: #999;
}
.griddly tr.header td input.quick-search {
  background-color: transparent;
  font-size: 11px;
  color: white;
  float: right;
  margin-top: -2px;
  margin-right: 1px;
  margin-bottom: -2px;
  padding: 1px 4px 1px 4px;
  height: 24px;
  border-radius: 3px;
  border: 1px solid #aaa;
}
.griddly tr.header input.quick-search::placeholder,
.griddly tr.header input.quick-search::-webkit-input-placeholder {
  color: white;
}
.griddly tr.header input.quick-search::-moz-placeholder {
  color: white;
}
.griddly tr.header input.quick-search:-ms-input-placeholder {
  color: white;
}
table.grid td.right,
table.grid th.right {
  text-align: right;
}
table.grid td.center,
table.grid th.center {
  text-align: center;
}
table.grid td.left,
table.grid th.left {
  text-align: left;
}
table.grid td.justify,
table.grid th.justify {
  text-align: justify;
}
table.grid thead tr.filters .saveForm {
  display: none;
  border: none;
}
table.grid thead tr.header td {
  font-size: 13px;
  font-weight: bold;
  padding: 5px;
  text-align: left;
  color: white;
  height: 25px;
  background: #627eba;
  /* IE8/IE9 */
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#627eba', EndColorStr='#485d7c')";
  background-image: linear-gradient(#627eba, #485d7c);
}
table.grid thead a.reset,
table.grid thead a.navigate {
  color: white;
}
table.grid thead tr.header td input.quickSearch[type=text] {
  background-color: transparent;
  font-size: 11px;
  color: white;
  float: right;
}
table.grid thead tr.header td input.quickSearch::-webkit-input-placeholder {
  color: white;
}
tr.header td .watermark {
  color: White;
}
tr.payment-error,
tr.payment-pending {
  color: #f00;
}
table.grid thead tr.header td span.status {
  font-style: italic;
  font-weight: normal;
  padding: 0 8px;
}
table.grid thead tr.filters > td,
table.grid thead tr.printing > td {
  border: solid 1px #dadada;
  text-align: left;
}
table.grid thead td .filterForm {
  margin-bottom: 0;
}
table.grid thead tr.filters .filterForm {
  float: left;
}
table.grid thead tr.filters .saveLinks {
  float: right;
  margin: 10px 20px;
}
table.grid thead tr.filters .saveLinks a {
  cursor: pointer;
}
table.grid thead tr.filters div.errors label {
  width: auto;
}
table.grid thead tr.filters.hidden,
table.grid thead tr.printing.hidden {
  display: none;
}
table.grid thead th {
  font-weight: bolder;
  padding: 5px 4px;
  background: #f4e8d3;
  /* IE8/IE9 */
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#f4e8d3', EndColorStr='#e3dabb')";
  background-image: linear-gradient(#f4e8d3, #e3dabb);
  border: 1px solid #dadada;
  font-size: 11px;
}
table.grid thead th.sortable {
  cursor: pointer;
}
table.grid thead th.sorted_a,
table.grid thead th.sorted_d {
  font-weight: bold;
  background: #e3dabb;
  /* IE8/IE9 */
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#e3dabb', EndColorStr='#f4e8d3')";
  background-image: linear-gradient(#e3dabb, #f4e8d3);
}
table.grid.grid-old thead th.sorted_a span.icon:after,
table.grid.grid-old thead th.sorted_d span.icon:after {
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  width: 0;
  height: 0;
  content: "";
  display: inline-block;
  position: relative;
  top: -1px;
  margin-left: 4px;
}
table.grid thead th.sorted_a span.icon:after {
  border-bottom: 5px solid #666;
}
table.grid thead th.sorted_d span.icon:after {
  border-top: 5px solid #666;
}
table.grid tbody.data > tr > td {
  padding: 4px 4px;
  border: solid 1px #dadada;
  height: 28px;
}
table.grid tbody.data tr td img,
table.grid tbody.data tr td span.icon16,
table.grid tbody.data tr td span.icon20 {
  vertical-align: middle;
}
table.grid.rowclick tbody.data tr {
  cursor: pointer;
}
table.grid.rowhover tbody.data tr {
  cursor: default;
}
tr.drag > td,
table.grid.rowclick > tbody.data > tr:hover > td,
table.grid.rowhover > tbody.data > tr:hover > td {
  background-color: #ffffb4;
}
td.grippy {
  width: 20px;
  overflow: hidden;
  line-height: 5px;
  padding-bottom: 3px;
  cursor: move;
  vertical-align: middle;
  font-size: 12px;
  font-family: sans-serif;
  letter-spacing: 2px;
  color: #cccccc;
  text-shadow: 1px 0 1px black;
}
table.grid tfoot td {
  padding: 5px;
  text-align: right;
  border: solid 1px #dadada;
  background-color: #e7e7e7;
}
.griddly-footer {
  min-height: 33px;
  text-align: center;
  border: 1px solid #dadada;
  border-top: none;
  background-color: #e7e7e7;
  padding: 5px;
}
table.grid tfoot td span.summary,
.griddly-summary {
  float: left;
  margin-top: 3px;
}
.griddly-pagesize {
  float: right;
}
table.grid tfoot td input[type=text],
.griddly-footer input {
  margin-top: -2px;
  width: 25px;
  text-align: center;
  vertical-align: middle;
}
table.grid tfoot td a.prev,
table.grid tfoot td a.next,
.griddly-footer a.prev,
.griddly-footer a.next {
  margin-right: 9px;
  margin-left: 9px;
}
table.grid tr.buttons > td {
  padding: 3px 0 10px 0;
  border: none;
}
table.grid tr.buttons a.btn {
  line-height: 20px;
  margin-right: 5px;
}
table.grid tr.buttons a.btn span.caret {
  margin-bottom: 1px;
}
table.grid tr.buttons a.btn span.icon20 {
  margin-right: 5px;
}
table.grid tr.buttons .sys a.btn {
  margin-right: 0;
}
table.grid tr.buttons .sys a.btn span.icon20:last-child {
  margin-right: 0;
}
table.grid tr.buttons .sys .btn-group {
  margin-left: 5px;
}
table.grid tr.buttons .sys .FilterButton .btn {
  height: 33px;
}
table.grid tr.buttons div.ButtonTemplate {
  display: inline-block;
  vertical-align: middle;
}
table.grid tr.buttons div.ButtonTemplate a span.icon20 {
  vertical-align: middle;
  margin-left: 2px;
}
table.grid tr.buttons .quickentry {
  display: block;
  float: left;
  border: solid 1px transparent;
  padding: 1px;
  margin-right: 4px;
}
table.grid tr.buttons .quickentry input {
  margin-right: 0;
}
table.grid tfoot div.grid-footer {
  display: table;
  width: 100%;
}
table.grid tr.buttons .quickentry input,
table.grid tr.buttons .quickentry select {
  vertical-align: middle;
  width: 250px;
}
table.grid tr.buttons .quickentry a span.icon20 {
  vertical-align: middle;
}
table.grid tfoot div.grid-footer span.row {
  display: table-row;
}
table.grid tfoot span.summary-left {
  text-align: left;
  display: table-cell;
  width: 200px;
}
table.grid tfoot span.summary-right {
  text-align: right;
  display: table-cell;
  width: 200px;
}
table.grid tfoot span.pager {
  text-align: center;
  display: table-cell;
}
table.grid tfoot td input {
  width: 25px;
  text-align: center;
  vertical-align: middle;
}
table.grid tr.settings form input[type=text] {
  width: 50px;
}
table.grid select.operator,
table.grid thead td .filterForm div select.operator {
  width: 100px;
}
table.grid select.junction,
table.grid td.junction,
table.grid thead td .filterForm div select.junction {
  width: 55px;
}
table.grid table.advanced th {
  border: none;
  border-bottom: solid 1px #aaa;
  background: none;
}
table.grid table.advanced td {
  border: none;
  padding: 2px;
}
table.grid table.advanced td.not {
  text-align: center;
}
table.grid a.advanced {
  cursor: pointer;
}
table.grid div.advanced {
  margin: 10px 0;
}
table.grid table.advanced {
  margin-left: 5px;
  margin-top: 5px;
}
table.grid .sys ul.dropdown-menu {
  font-size: 12px;
  width: 300px;
}
table.grid .sys ul.dropdown-menu li {
  list-style: none;
  padding: 3px 6px;
}
table.grid .sys ul.dropdown-menu a {
  padding-left: 5px;
  cursor: pointer;
}
/* Table - EDIT
----------------------------------------------------------*/
table.edit th {
  text-align: left;
  padding: 3px 10px;
}
table.edit {
  margin: 0;
  padding: 0;
}
/* ERROR
----------------------------------------------------------*/
.success {
  color: #00009f;
}
.form input.invalid,
.form select.invalid,
.form textarea.invalid,
.form select.jqddfilter.invalid ~ input,
form.unobtrusive .input-validation-error {
  border: solid 1px #f50000;
  /*padding: 1px;*/
}
select.invalid {
  padding: 0 0 0 1px;
}
label.invalid {
  color: #f50000;
  display: block;
}
div.errorDetails {
  border: solid 1px #f50000;
  padding: 10px;
  padding-top: 30px;
  color: #f50000;
  position: absolute;
  z-index: 50;
  background-color: #ffffd7;
  display: block;
  left: -5px;
  /*opacity:0.1;
    -moz-opacity:0.1;
    filter:alpha(opacity=10);*/
}
/* ACTIONS - buttons
-------------------------------------------------------------------*/
div.pagebuttons {
  background-color: #fff;
  z-index: 3;
  /*position: absolute;
    right:0;
    top: 22px;
    margin:0;*/
  margin-top: -5px;
}
div.pagebuttons.output {
  margin-top: -50px;
}
div.pagebuttons a {
  line-height: 20px;
}
div.pagebuttons a span.icon20 {
  vertical-align: middle;
  margin-right: 5px;
}
div.pagebuttons a span.icon20.right {
  vertical-align: middle;
  margin-right: 0;
  margin-left: 5px;
}
div.pagebuttons ul.dropdown-menu {
  font-size: 12px;
  width: 300px;
}
div.pagebuttons ul li {
  list-style: none;
  padding: 1px 5px;
  font-weight: bold;
}
div.pagebuttons ul a {
  font-weight: normal;
}
/* Folder tree defaults
------------------------------------------------------------*/
ul.foldertree,
ul.foldertree ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
ul.foldertree li ul {
  display: none;
}
ul.foldertree li.expanded ul.expanded {
  display: block;
}
ul.foldertree li {
  padding-left: 18px;
  background-repeat: no-repeat;
  background-position: 1px 2px;
  white-space: nowrap;
  text-overflow: ellipsis;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow-x: hidden;
}
ul.foldertree li.hasNodes {
  background-image: url(../images/checktree/tree_plus.gif);
}
ul.foldertree li.hasNodes.expanded {
  background-image: url(../images/checktree/tree_minus.gif);
}
ul.foldertree li.sel > a {
  font-weight: bold;
  color: black;
  font-style: italic;
}
/* FullCalendar defaults
------------------------------------------------------------*/
#calendar .fc-event-skin {
  font-size: 1em;
}
#calendar .fc-event {
  margin-bottom: 2px;
  color: white;
  font-size: 1em;
}
/* Calendar Items */
#calendar .fc-event.task {
  border-color: #d96666;
  background-color: #d96666;
  color: white;
}
#calendar .fc-event.audititem {
  border-color: #4cb052;
  background-color: #4cb052;
  color: white;
}
#calendar .fc-event.milestone {
  border-color: #c4a883;
  background-color: #c4a883;
  color: white;
}
#calendar .fc-event.milestone #calendar .fc-event.poam {
  border-color: #668cd9;
  background-color: #668cd9;
  color: white;
}
#calendar .fc-event.event {
  border-color: #59bfb3;
  background-color: #59bfb3;
  color: white;
}
#calendar .fc-event.assignment {
  border-color: #e6804d;
  background-color: #e6804d;
  color: white;
}
#calendar .fc-event.extra {
  border-color: #aaa;
  background-color: #aaa;
  color: White;
}
#calendar .fc-event-inner.committee,
#calendar .committee .fc-event-inner {
  border-color: #d9810f;
  background-color: #d9810f;
  color: white;
}
/*
Free colors:
rgb(224,194,64)
rgb(140,102,217)
*/
.fc .fc-event.important {
  background-image: url(../images/icons/16/sign_warning.png);
  background-position: top left;
  background-repeat: no-repeat;
}
.fc .fc-event.complete {
  background-image: url(../images/icons/16/check.png);
  background-position: top left;
  background-repeat: no-repeat;
}
.fc .fc-event.complete span:first-child,
.fc .fc-event.important span:first-child {
  padding-left: 18px;
}
.fc-past {
  background-color: #f0f0f0;
}
/* DASHBOARD BOXES
----------------------------------------------------------*/
.dashboardbox {
  width: 400px;
  font-size: 11px;
}
.dashboardbox > div {
  padding: 10px;
  overflow: hidden;
  margin: 0 20px 20px 0;
  border: 1px solid #ddd;
  border-radius: 4px;
}
/*.dashboardbox .infobox
    {
        padding: 3px 4px 3px 7px;
    }*/
.dashboardbox .infobox th {
  vertical-align: top;
}
.dashboardbox .title {
  font-size: 13px;
  font-weight: bold;
  padding: 3px 0 0 8px;
  margin: -10px -10px 10px -10px;
  color: white;
  height: 25px;
  background: #627eba;
  /* IE8/IE9 */
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#627eba', EndColorStr='#485d7c')";
  background-image: linear-gradient(#627eba, #485d7c);
}
.dashboardbox .title a {
  color: white;
  font-style: italic;
}
#dashboardextra {
  padding: 10px;
}
.dashboardbox table:not(.simplegrid):not(.grid) td {
  min-width: 90px;
  padding: 3px 6px;
}
.dashboardbox table:not(.simplegrid):not(.grid) th {
  min-width: 90px;
  padding: 3px 6px 3px 0;
}
.dashboardbox .editable {
  /*border:1px dashed transparent;*/
  position: relative;
}
.dashboardbox .editable:hover {
  background-color: #f0f0f0;
  box-shadow: 0 0 10px #bbb;
}
.editableblock-edit {
  padding: 0 3px 0 5px;
  border-left: solid 1px #ccc;
  border-bottom: solid 1px #ccc;
  background-color: #fdfdfd;
}
.hr {
  display: block;
  width: 99%;
  margin: 5px 0 5px 0;
  border-top: 1px solid #d8d8d8;
  border-bottom: 1px solid #fff;
}
.highcharts-container hr {
  margin: 0;
}
/* SIMIPLE GRID
----------------------------------------------------------*/
.simplegrid {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
}
.simplegrid th {
  text-align: left;
  border: solid 1px #999;
  padding: 2px 3px;
}
.simplegrid td {
  vertical-align: top;
  border: solid 1px #ccc;
  padding: 1px 3px;
}
.simplegrid .number {
  text-align: center;
}
.simplegrid .date {
  text-align: center;
}
/* CREDIT CARD SPRITE
----------------------------------------------------------*/
span.creditcard {
  background-image: url(../images/creditcards.png);
  display: inline-block;
  height: 18px;
  vertical-align: text-bottom;
  margin-right: 5px;
}
span.creditcard.visa {
  background-position: 0;
  width: 41px;
}
span.creditcard.mastercard {
  background-position: -45px;
  width: 38px;
}
span.creditcard.americanexpress,
span.creditcard.amex {
  background-position: -87px;
  width: 56px;
}
span.creditcard.discover {
  background-position: -150px;
  width: 80px;
}
span.creditcard.jcb {
  background-position: -237px;
  width: 22px;
}
/* SKILLS INFO BOX
----------------------------------------------------------*/
.skillBox.expired {
  color: Red;
}
.skillBox a {
  float: right;
}
#skillsmasonry h5 {
  margin: 2px 0;
}
/*********************
    twitter bootstrap fixes
*********************/
/*.btn-group
{
    overflow: inherit;
    padding-bottom: 0;
    margin-bottom: 0;
}
.btn-group.pull-right
{
    margin-left:5px;
}*/
.griddly {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  clear: both;
}
.griddly div.griddly-filters.griddly-filters-form > form.filterForm {
  border: 1px solid #dadada;
}
.griddly .griddly-filters-inline {
  background-color: #fafafa;
  background-image: -moz-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f2f2f2));
  background-image: -webkit-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: -o-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2);
  background-repeat: repeat-x;
  border: 1px solid #d4d4d4;
  border-radius: 4px;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fff2f2f2', GradientType=0);
  *zoom: 1;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.065);
}
/*.griddly .griddly-filters-inline select,
.griddly .griddly-filters-inline input[type="text"]
{
    width: 100% !important;
}*/
.griddly .griddly-filters-inline input[type="text"] {
  min-height: 30px;
  margin-bottom: 0;
}
.griddly .griddly-filters-inline td {
  padding: 4px 3px;
  border: none;
}
.griddly .griddly-filters-inline select {
  display: none;
}
.griddly input,
.griddly select,
.griddly tbody td {
  -webkit-user-select: text;
  -ms-user-select: text;
  -moz-user-select: text;
  user-select: text;
}
.griddly td.sorted_a,
.griddly td.sorted_d {
  background-color: #f0f0f0;
}
.griddly-select {
  padding: 0 2px !important;
  min-width: 24px;
}
.griddly-select input {
  width: 14px;
  height: 14px;
  margin: 0;
  margin-top: 5px;
}
.align-center {
  text-align: center;
}
.align-right {
  text-align: right;
}
/*
.griddly-scrollable-container
{
    overflow-x:auto;
    clear:both;
}
.griddly-scrollable-container::-webkit-scrollbar {  
    width: 8px;  
    height:8px;
}  
.griddly-scrollable-container::-webkit-scrollbar-track {  
    background: #fafafa;
}  
.griddly-scrollable-container::-webkit-scrollbar-thumb {  
    background: #888;
}  
.griddly-scrollable-container::-webkit-scrollbar-thumb:hover {  
    background-color: #aaa;  
} 
*/
div.griddly.griddly-filter-form .griddly-scrollable-container,
div.griddly.griddly-filter-none .griddly-scrollable-container {
  overflow-x: inherit;
  clear: both;
  border: none;
}
.buttons,
.griddly-buttons {
  margin-top: 10px;
  margin-bottom: 10px;
  min-height: 34px;
}
.buttons button.dropdown-toggle {
  height: 32px;
}
.m3-print ~ ul.dropdown-menu,
.m3-search ~ ul.dropdown-menu {
  width: 300px;
}
.attachment-field {
  padding: 0;
}
.attachment-field .qq-upload-button div {
  padding: 0;
}
.attachment-field .qq-uploader {
  padding: 0;
}
select.hide-radio + .btn-group input[type=radio] {
  display: none;
}
.griddly tfoot .btn-group {
  text-align: left;
  float: left;
  margin: 2px 4px;
}
.qq-upload-button {
  text-align: left;
  width: auto;
  background: inherit;
  border-bottom: none;
  color: inherit;
  padding: 0;
  /*display: block;
    width: 105px;
    padding: 7px 0;
    text-align: center;
    background: #880000;
    border-bottom: 1px solid #DDD;
    color: #FFF;*/
}
.grid-old .buttons .qq-upload-button-hover {
  background-color: #eeeef0;
  /*border: solid 1px #999999;
    border-radius: @border-radius-base;*/
}
.grid-old .buttons .qq-upload-button-hover a {
  text-decoration: underline !important;
  padding: 3px !important;
}
.dropzone {
  clear: both;
  display: none;
  border: 3px dashed #ccc;
  border-bottom-left-radius: 0.25em 0.5em;
  border-bottom-right-radius: 0.25em 0.5em;
  border-top-left-radius: 0.25em 0.5em;
  border-top-right-radius: 0.25em 0.25em;
  /*Undo styles from .qq-upload-extra-drop-area*/
  position: inherit;
  margin-top: inherit;
  font-size: inherit;
  padding-top: inherit;
  height: inherit;
  min-height: inherit;
  top: inherit;
  left: inherit;
  z-index: inherit;
  background: inherit;
  text-align: inherit;
}
.dropzone div {
  position: relative;
  top: 35%;
  font-size: 150%;
  margin: 1.5em;
  text-align: center;
  color: #666;
}
.dropzone.qq-upload-drop-area-active {
  background-color: #f0f0f0;
}
.griddly .dropzone {
  position: absolute;
  width: 100%;
  height: 100%;
  display: none;
  background: #444;
  color: #fff;
  opacity: 0.5;
  z-index: 50;
}
.qq-upload-drop-area-active {
  background: #444;
}
/*.dropZone.qq-upload-drop-area-active 
{
    display:block;
}*/
.griddly .dropzone div {
  font-size: 300%;
  position: relative;
  top: 50%;
  text-align: center;
  opacity: 1;
}
.form div.group .tastyupload {
  padding-top: 1px;
}
.tastyupload .selected-file {
  display: none;
}
.tastyupload .upload-files li {
  list-style: none;
}
/* DISTRIBUTION
----------------------------------------------------------*/
.form div.group .distribution-container {
  /*width: 500px;*/
  float: left;
  padding: 3px 0;
}
.distribution-container .distribution-view {
  padding: 3px 5px 1px 5px;
  margin-bottom: 2px;
  border: solid 1px #aaaaae;
  border-radius: 3px;
  line-height: normal;
  width: 400px;
  display: inline-block;
  float: left;
}
.distribution-container.invalid .distribution-view {
  border: solid 2px red;
  padding: 2px 4px 0 4px;
}
.distribution-container .distribution-view span {
  padding: 2px 3px;
  border-radius: 3px;
  display: inline-block;
  margin-right: 3px;
  margin-bottom: 3px;
}
.distribution-container .selectFilter.button {
  width: 50px;
  float: right;
  display: inline-block;
}
.distribution-view span.selected {
  background-color: #e9e9e9;
}
.distribution-view span .remove {
  margin: 1px 4px;
}
.distribution-view span .remove:before {
  content: "x";
}
.distribution-view span.none {
  font-style: italic;
}
/*Typeahead*/
.tt-dropdown-menu {
  min-width: 300px;
  width: 100%;
  margin-top: 2px;
  padding: 5px 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  *border-right-width: 2px;
  *border-bottom-width: 2px;
  border-radius: 6px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
}
.tt-suggestions {
  width: 300px;
}
.tt-suggestion {
  display: block;
  padding: 3px 20px;
  font-size: 14px;
}
.tt-suggestion.tt-cursor {
  color: #262626;
  background-color: #f5f5f5;
}
.tt-suggestion.tt-cursor a {
  color: #fff;
}
.tt-suggestion p {
  margin: 0;
}
.tt-hint {
  font-size: 12px;
  line-height: 1.42857143;
  width: 100%;
}
span.twitter-typeahead {
  width: 100%;
}
.tt-suggestion div.imageholder {
  width: 32px;
  height: 32px;
  float: left;
  background-color: #fff;
  margin-right: 10px;
  margin-top: 5px;
}
.tt-suggestion div.imageholder img {
  width: 32px;
  height: 32px;
}
/* datepick styling */
.datepick-popup {
  z-index: 1080;
}
.datepick {
  margin-left: 3px;
  background-color: white;
  border-color: #aaa;
}
.datepick a {
  color: #428bca;
}
.datepick a:hover {
  color: #2a6496;
  text-decoration: underline;
}
.datepick-cmd:hover {
  background-color: transparent;
}
.datepick-nav {
  background-color: transparent;
  padding: 2px 0;
  color: #222;
}
.datepick-ctrl {
  padding-top: 4px;
  background-color: transparent;
}
.datepick-ctrl .datepick-cmd:hover {
  background-color: transparent;
}
.datepick-month {
  border: none;
}
.datepick-month thead {
  border: none;
}
.datepick-month .datepick-month-header {
  background-color: transparent;
  color: #666;
}
.datepick-month td {
  border: none;
}
.datepick-month td .datepick-highlight {
  background-color: #609DCC;
  color: white;
}
.datepick-month th {
  background-color: transparent;
  color: #666;
  border: none;
}
.datepick-month td .datepick-today {
  background-color: #A8C7DF;
}
/* Fixes for TinyMCE */
.contentBody .o2k7Skin .mceListBox .mceText {
  height: 22px;
}
.content .panel.panel-default {
  border-color: #627eba;
}
.content .panel.panel-default table thead th {
  background: #f4e8d3;
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#f4e8d3', EndColorStr='#e3dabb')";
  background-image: linear-gradient(#f4e8d3, #e3dabb);
}
.content .panel.panel-default .panel-heading {
  background: #627eba;
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorStr='#627eba', EndColorStr='#485d7c')";
  background-image: linear-gradient(#627eba, #485d7c);
  color: #fff;
  font-weight: normal;
}
/* Fixes for Select2 */
/*Fix for clear (x) button not clicking if text to long*/
.select2-container--default .select2-selection--single .select2-selection__clear {
  position: relative;
}
.select2-results__option {
  padding: 2px;
}
.select2-container .select2-selection {
  min-height: 26px;
}
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 26px;
  height: 26px;
}
.select2-container--default .select2-search--inline .select2-search__field,
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  margin-top: 2px;
  margin-bottom: 0;
}
.select2-selection--multiple .select2-selection {
  line-height: 18px;
}
.select2-results ul li:nth-child(even) {
  background-color: #eee;
}
/*Base on: https: //codepen.io/richfergus/pen/pNvRWd*/
/* Timeline */
.timeline,
.timeline-horizontal {
  list-style: none;
  padding: 20px;
  position: relative;
  /*&:before {
        top: 40px;
        bottom: 0;
        position: absolute;
        content: " ";
        width: 3px;
        background-color: #eeeeee;
        left: 50%;
        margin-left: -1.5px;
    }*/
}
.timeline .timeline-item {
  margin-bottom: 20px;
  position: relative;
}
.timeline .timeline-item:before,
.timeline .timeline-item:after {
  content: "";
  display: table;
}
.timeline .timeline-item:after {
  clear: both;
}
.timeline .timeline-item .timeline-badge {
  color: #fff;
  width: 54px;
  height: 54px;
  line-height: 52px;
  font-size: 22px;
  text-align: center;
  position: absolute;
  top: 18px;
  left: 50%;
  margin-left: -25px;
  background-color: #333;
  border: 3px solid #fff;
  z-index: 100;
  border-top-right-radius: 50%;
  border-top-left-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.timeline .timeline-item .timeline-badge i,
.timeline .timeline-item .timeline-badge .fa,
.timeline .timeline-item .timeline-badge .glyphicon {
  top: 2px;
  left: 0px;
}
.timeline .timeline-item .timeline-badge.primary {
  background-color: #428bca;
}
.timeline .timeline-item .timeline-badge.info {
  background-color: #5bc0de;
}
.timeline .timeline-item .timeline-badge.success {
  background-color: #5cb85c;
}
.timeline .timeline-item .timeline-badge.warning {
  background-color: #f0ad4e;
}
.timeline .timeline-item .timeline-badge.danger {
  background-color: #d9534f;
}
.timeline .timeline-item .timeline-panel {
  position: relative;
  width: 46%;
  float: left;
  right: 16px;
  border: 1px solid #777;
  background: #fff;
  border-radius: 2px;
  padding: 20px;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
}
.timeline .timeline-item .timeline-panel:before {
  position: absolute;
  top: 26px;
  right: -16px;
  display: inline-block;
  border-top: 16px solid transparent;
  border-left: 16px solid #777;
  border-right: 0 solid #777;
  border-bottom: 16px solid transparent;
  content: " ";
}
.timeline .timeline-item .timeline-panel .timeline-title {
  margin-top: 0;
  color: inherit;
}
.timeline .timeline-item .timeline-panel .timeline-body > p,
.timeline .timeline-item .timeline-panel .timeline-body > ul {
  margin-bottom: 0;
}
.timeline .timeline-item .timeline-panel .timeline-body > p + p {
  margin-top: 5px;
}
.timeline .timeline-item:last-child:nth-child(even) {
  float: right;
}
.timeline .timeline-item:nth-child(even) .timeline-panel {
  float: right;
  left: 16px;
}
.timeline .timeline-item:nth-child(even) .timeline-panel:before {
  border-left-width: 0;
  border-right-width: 14px;
  left: -14px;
  right: auto;
}
.timeline-horizontal {
  list-style: none;
  position: relative;
  padding: 0px 0px 25px 0px;
  display: block;
  white-space: nowrap;
  /*&:before {
        height: 10px;
        top: auto;
        left:unset;
        width: 100%;
        margin-bottom: 15px;
    }*/
}
.timeline-horizontal .timeline-item {
  display: inline-block;
  max-width: 360px;
  padding-left: 0px;
  padding-right: 20px;
  vertical-align: bottom;
  white-space: normal;
}
.timeline-horizontal .timeline-item .timeline-panel {
  display: inline-block;
  float: none !important;
  left: 0 !important;
  right: 0 !important;
  width: 100%;
  margin-bottom: 5px;
}
.timeline-horizontal .timeline-item .timeline-panel:before {
  top: auto;
  bottom: -16px;
  left: 28px !important;
  right: auto;
  border-right: 16px solid transparent !important;
  border-top: 16px solid #777 !important;
  border-bottom: 0 solid #777 !important;
  border-left: 16px solid transparent !important;
}
.timeline-horizontal .timeline-item:before,
.timeline-horizontal .timeline-item:after {
  display: none;
}
.timeline-horizontal .timeline-item .timeline-badge {
  top: auto;
  bottom: -65px;
  left: 43px;
}
.timeline-horizontal + div {
  width: 100%;
  background-color: #eee;
  position: sticky;
  right: 0;
  left: 0;
  display: block;
  line-height: 10px;
  bottom: 20px;
}
@media screen and (max-width: 992px) {
  .timeline .timeline-item {
    margin-bottom: 0;
    padding-right: 5px;
  }
  .timeline .timeline-item .timeline-body {
    display: none;
  }
  .timeline .timeline-item .timeline-title {
    font-size: 15px;
    margin-bottom: 0;
  }
  .timeline .timeline-item .timeline-panel {
    padding: 10px;
  }
  .timeline .timeline-item .timeline-badge {
    width: 32px;
    height: 32px;
    font-size: 14px;
    line-height: 27px;
  }
  .timeline.timeline-horizontal .timeline-item .timeline-badge {
    bottom: -36px;
    left: 48px;
  }
  .timeline.timeline-horizontal .timeline-item .timeline-panel:before {
    bottom: -10px;
    border-right: 10px solid transparent !important;
    border-top: 10px solid #777 !important;
    border-bottom: 0 solid #777 !important;
    border-left: 10px solid transparent !important;
  }
}
@media screen and (min-width: 993px) and (max-width: 1200px) {
  .timeline .timeline-item {
    margin-bottom: 5px;
    padding-right: 10px;
  }
  .timeline .timeline-item .timeline-title {
    font-size: 15px;
    margin-bottom: 2px;
  }
  .timeline .timeline-item .timeline-panel {
    padding: 10px;
  }
  .timeline .timeline-item .timeline-badge {
    width: 40px;
    height: 40px;
    font-size: 18px;
    line-height: 35px;
  }
  .timeline.timeline-horizontal .timeline-item .timeline-badge {
    bottom: -46px;
    left: 48px;
  }
  .timeline.timeline-horizontal .timeline-item .timeline-panel:before {
    bottom: -13px;
    border-right: 13px solid transparent !important;
    border-top: 13px solid #777 !important;
    border-bottom: 0 solid #777 !important;
    border-left: 13px solid transparent !important;
  }
  .timeline.timeline-horizontal .timeline-body {
    color: #777;
  }
}
@media screen and (min-width: 993px) {
  .timeline .timeline-item .info-link {
    display: none;
  }
}
/* base colors */
/* application */