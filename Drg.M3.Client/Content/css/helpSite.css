body
{
    margin:0;
    padding:0;
}
textarea, input, body, table
{
    font-family:Arial, Sans-Serif;
    font-size:11pt;
    color:#000c35;
}
h1
{
    margin:10px 15px;
    float:left;
    font-size:1.6em;
}
h2
{ 
    font-size:1.4em;
}
h1, h2, h3, h4
{
    margin-bottom:0.2em;
    color:#384369;
}
a, a:visited, a:link
{
    color: #032285;
    text-decoration: none;
}
a:hover
{
    color: #336699;
    text-decoration: underline;
}
#head
{
    height:100px;
    background-image:url(../images/help/header_bg.png);
    background-repeat:repeat-x;
}
#header
{
    float:left;
}
#tabs
{
    padding-bottom:10px;
    height:30px;
}
#tabs ul
{
    margin:0;
    padding:0;
    list-style-type:none;
}
#tabs ul li
{
    display:block;
    float:left;
}
#tabs ul li a
{
    display:block;
    padding:3px 6px;
    text-align:center;
    background-image:url(../images/help/tab_bg.png);
    background-repeat:no-repeat;
    width:89px;
    height:30px;
    font-size:1.15em;
}
#tabs ul li.sel
{
}
#search 
{
    overflow:auto;
    margin-left:5px;
    margin-top:5px;
}
#search input
{
    padding:3px;
    font-size:1.2em;
    border-radius:8px;
    -moz-border-radius:8px;
    -webkit-border-radius:8px;
    width:300px;
}
#body
{
    padding:15px;
    overflow:auto;
}
#body h1
{
    float:none;
    margin:0;
}
#body ul.chargroup
{
    padding-left:20px;
    margin-bottom:10px;
}
div.preview
{
    border:dashed 2px #aaa;
    padding:15px;
    overflow:hidden;
}
div.notes
{
    background-color:#eff5f8;
    border:solid 2px #96b8cc;
    border-radius:8px;
    -moz-border-radius:8px;
    -webkit-border-radius:8px;
    padding:10px;
    width:300px;
    float:right;
    margin-left:10px;
    margin-bottom:10px;
}
div.notes h4
{
    margin:0;
}
div.notes ul
{
    margin:0;
    padding:8px;
    list-style-type:none;
}
div.notes ul li a.sel
{
    font-weight:bold;
}
.move
{
    display:inline-block;
    margin-left:20px;
    text-align:right;
}
.move a.up, .move a.down
{
    display:inline-block;
    vertical-align:middle;
    width:16px;
    height:16px;
    background-position:center top;
    background-repeat:no-repeat;
    cursor:pointer;
    display:none;
}
.move a.up
{
    background-image:url(../images/help/up.png);
}
.move a.down
{
    background-image:url(../images/help/down.png);
}
.move a.up:hover
{
    background-position:center bottom;
}
.move a.down:hover
{
    background-position:center bottom;
}

div.buttons
{
    margin-top:20px;
    margin-bottom:50px;
}
div.buttons input
{
    padding:4px;
    font-size:1.2em;
}
textarea.markdown
{
    width:600px;
    height:300px;
}
a.chargroup
{
    cursor:pointer;
    font-size:1.2em;
}
a.chargroup:hover span
{
    text-decoration:none;
}
div.contents blockquote
{
    background-color:#ddd;
    margin:10px 5px;
    padding:4px;
}
div.contents blockquote p
{
    margin:0;
}
div.contents h1
{
    font-size:1.4em;
}
div.contents h2
{
    font-size:1.2em;
}
div.contents h3
{
    font-size:1.1em;
}
div.contents h4
{
    font-size:1em;
}
#footer
{
    border-top:solid 1px #ccc;
    text-align:center;
    margin:5px;
    padding:10px;
}
.edit  
{
    display:inline-block; 
    width:20px; 
    height:20px; 
    text-indent:-9999px; 
    background-image:url(../images/icons/20/edit.png); 
    vertical-align:baseline;
    margin-left:10px;
}

/* Video Plugin */
#jq-overlay {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 90;
	width: 100%;
}
#jq-floaty {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100;
	text-align: center;
	line-height: 0;
}
#floaty-container-embed {
	position: relative;
	width: 100px;
	height: 100px;
	margin: 0 auto;
}

#floaty-nav {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	z-index: 10;
}
#floaty-container-embed > #floaty-nav {left:0}
#floaty-nav a { outline: none;}
#floaty-close-btn {position: relative;cursor:pointer;width:35px;height:35px;top:-20px;right:-20px;float:right}
#floaty-close-btn:hover {background-position:-35px 0}
#floaty-object{float:left;margin:-25px 10px;}

@media print
{
    
    #head, #footer, #body div.notes,
    .noprint
    {
        display:none;
    }
    #body
    {
        overflow: visible !important;
    }
}

