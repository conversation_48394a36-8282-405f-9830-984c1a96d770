/// <reference path="../../Scripts/jquery-1.11.0.intellisense.js" />
function FormatTime(str) {
    // see pax.date.js for formatting
    var result = Date.guess(str.toLowerCase(),
        [
            "g*i*a", "h*i*a", "G*i*a", "H*i*a", "gi*a", "Gi*a",
            "Gi", "Hi", "g*i", "h*i", "g*a", "h*a", "g"
        ]
    );
    if (result == null) return str;
    return result.dateFormat("g:i a");
}
function FormatDate(str) {
    //str = str.toProperCase();

    var result = Date.guess(str,
        [
            "mdY", "njY",
            "m*d*Y", "n*j*Y",
            "m*j*y", "j*M*y", "j*F*y", "M*y", "F*y", "j M",
            "m*j*Y", "j*M*Y", "j*F*Y", "M*Y", "F*Y", "j F",
            "n*j*Y", "n*j*y", "M*j*Y"
        ]
    );
    if (result == null) return str;
    return result.dateFormat("m/d/Y");
}
function FormatPhone(num) {
    //if contains alpha characters or "+", do nothing to it
    var regex = new RegExp(/[a-zA-Z+]/);
    var match = regex.exec(num);

    if (match == null) {
        var num2 = num.replace(/\D/gi, "");

        var formatted = num2;
        // we're not formatting international codes 
        if (num2.length >= 10) {
            formatted = "(" + num2.substr(0, 3) + ") " + num2.substr(3, 3) + "-" + num2.substr(6, 4);
            if (num2.length > 10)
                formatted += " *" + num2.substr(10);
        }

        return formatted
    }
    return num;
}

function FormatNumber(num) {
    if (num.indexOf(".") == 0) {
        return "0" + num;
    }
    return num;
}
function FormatCurrency(num) {
    if(isNaN(num))
        num = num.replace(/\$|,/g, "");
    if (!isNaN(parseFloat(num))) {
        return "$" + parseFloat(num).toFixed(2);
    }
    return num;
}
function FormatPercent(num) {
    if (!isNaN(parseFloat(num))) {
        return parseFloat(num) + " %";
    }
    return num;
}
//function NumbersOnly(e) {
//    if (IsNavKey(e)) { return true; }
//    if (e.shiftKey) { return false; }
//    if ((e.which >= 48 && e.which <= 57) || (e.which >= 96 && e.which <= 105)) {
//        return true;
//    }
//    return false;
//}
//function IsNavKey(e) {
//    if (e.which >= 35 && e.which <= 40) {
//        return true;
//    }
//    if (e.which == 16 || e.which == 8 || e.which == 0 || e.which == 46) {
//        return true;
//    }
//}

function InitializeDatepicks(elements) {
    elements.each(function () {
        var disabled = $(this).is(":disabled");
        if (disabled)
            $(this).removeAttr("disabled");

        $(this).datepick({
            changeMonth: false,
            changeYear: false,
            onSelect: function (dates) {
                $(this).focus().blur();
                $(this).trigger('change');
            },
            alignment: "right"
        });

        if (disabled)
            $(this).attr("disabled", "");
    });
}

function BindCurrencyFields(elements) {
    elements.blur(function () {
        this.value = FormatCurrency(this.value);
        $(this).change();
    });
}

function InitializeM3UI(parentEl) {
    
    $(parentEl).find("[data-provider=selectfilter]").selectfilter();

    $.validator.addMethod("notEqualElement", function (value, element, param)
    {
        var target = $(param);
        if (value) return value != target.val();
        else return this.optional(element);
    });
    
    $.validator.messages.notEqualElement = "Values must be different.";

    //Bind date picker fields
    var prefix = "";
    if (parentEl == document)
        prefix = "form div.form:not(.ui-widget) ";


    InitializeDatepicks($("input.date, .date.pick", parentEl).filter(function () { return $(this).closest("table.advanced").length == 0; }));
    $("input.date, .date.pick", parentEl).blur(function () {
        this.value = FormatDate(this.value);
    });
    
    //Bind phone fields
    $("input.phone", parentEl).blur(function() {
        this.value = FormatPhone(this.value);
    });
    
    //Bind number fields
    $("input.number", parentEl).blur(function () {
        this.value = FormatNumber(this.value);
    });

    //Bind currency fields
    BindCurrencyFields($("input.currency", parentEl));
    $("form", parentEl).submit(function () {
        $("input.currency").each(function() {
            this.value = this.value.replace(/[$,)]/g, "").replace(/\(/, "-");
        });
    });
    
    //Bind percent fields
    $("input.percent", parentEl).blur(function() {
        this.value = FormatPercent(this.value);
    });
    
    //Bind url fields
    $("input.url", parentEl).blur(function () {
        if (this.value.indexOf(":") == -1) {
            if (this.value.split(".").length >= 2) {
                this.value = "http://" + this.value;
                $(this).parents("form").validate().element(this);
            }// else if (this.value.split(".").length == 3) {
            //    this.value = "http://" + this.value;                
            //    $(this).parents("form").validate().element(this);
            //}
        }
    });
    
    if ($("input.time", parentEl).length > 0) {
        $("input.time", parentEl).attr("placeholder", "12:00 AM");
        $("input.time", parentEl).blur(function () {
            this.value = FormatTime(this.value);
            if ($(this).parents("form").length > 0) {
                $(this).valid();
            }
        });
    }
    //    if ($("input.address-street1", parentEl).length > 0) {
    //        $("input.address-street1", parentEl).Watermark("Street");
    //        $("input.address-street2", parentEl).Watermark("Street Line 2");
    //        $("input.address-city", parentEl).Watermark("City");
    //        $("input.address-state", parentEl).Watermark("State");
    //        $("input.address-zip", parentEl).Watermark("Zip");
    //    }

    //labels to titles
    var labels = $("form label", parentEl);
    if (labels.length < 100) { 
        $("form label", parentEl).each(function () {
            if ($(this).attr("for") != '') {
                $(this).parents("form")
                .find("[name='" + $(this).attr("for") + "']")
                .attr("title", $(this).text().split(":")[0]);
            }
        });
    }
    //else: too big of a form to do this, it will bog down

    // setup validation for legacy forms
    $("form:not(.novalidate):not(.unobtrusive)", parentEl).each(function () {
        var form = $(this);

        form.validate({
            ignore: ".novalidatehidden:hidden, .novalidatehidden :hidden:not(.selectfilter)",
            errorContainer: $("div.errors", form),
            errorLabelContainer: $("div.errors", form),
            errorClass: "invalid",
            onkeyup: false,
            onsubmit: true,
            onfocusout: false,
            focusCleanup: false,
            formElement: $(".form", form)
        });

        form.find("input.digits[max],input.number[max]").each(function () {
            if (!isNaN(parseFloat($(this).attr("max"))) && isFinite(parseFloat($(this).attr("max"))))
                $(this).rules("add", { max: parseFloat($(this).attr("max")) });
        });
        form.find("input.digits[min],input.number[min]").each(function () {
            if (!isNaN(parseFloat($(this).attr("min"))) && isFinite(parseFloat($(this).attr("min"))))
                $(this).rules("add", { min: parseFloat($(this).attr("min")) });
        });
        
        form.find("input:submit[name]").click(function () {
            if (!$(this).parent().find(":hidden[name=" + $(this).attr("name") + "]").length)
                $(this).parent().append("<input type='hidden' name='" + $(this).attr("name") + "' value='" + $(this).val() + "' />");
            else
                $(this).parent().find(":hidden[name=" + $(this).attr("name") + "]").val($(this).val());
        });
        form.find("input:submit.novalidate").click(function() {
            $(this).parents("form").unbind("submit").submit();
        });
        
//        $("a.submit", form).click(function() {
//            $.Watermark.HideAll();
//            var valid = form.valid();
//            if (!valid) {
//                $.Watermark.ShowAll();
//            }
//            else {
//                form.submit();
//            }
//        });

    });

    $("a[data-confirm-message]:not(.griddly a[data-toggle=post], [data-role='griddly-button'])", parentEl).on("click", function (e) {
        var message = $(this).data("confirm-message");

        //if (message == "[delete]") {
        //    message = "This record and all related data will be lost. Are you sure you want to delete this record?"
        //}

        return confirm(message);
    });

    $(parentEl).on("click", "a[data-toggle=post]:not(.griddly a[data-toggle=post], [data-role='griddly-button']), .griddly table a[data-toggle=post]", function (e)
    {
        var url = $(e.currentTarget).data("url");

        if (url == null || url === undefined)
            url = $(e.currentTarget).attr("href");

        $.postGo(url);
        $(e.currentTarget).filter(":not(.no-disable)").attr("disabled", "disabled");

        e.preventDefault();

        return false;
    });
    $(parentEl).on("click", "a[data-toggle=ajax]:not(.griddly a[data-toggle=ajax], [data-role='griddly-button']), .griddly table a[data-toggle=ajax]", function (e)
    {
        var url = $(e.currentTarget).data("url");

        if (url == null || url === undefined)
            url = $(e.currentTarget).attr("href");

        $.post(url, function (data) {
            if (data.redirectUrl)
                window.location = data.redirectUrl;

            var griddly = $(e.currentTarget).closest(".griddly");

            if (griddly.length)
                griddly.griddly("refresh");

            $(e.currentTarget).triggerHandler("ajaxSubmitComplete", data);
        }).always(function () {
            $(e.currentTarget).filter(":not(.no-disable)").attr("disabled", null);
        });

        $(e.currentTarget).filter(":not(.no-disable)").attr("disabled", "disabled");

        return false;
     });
}
$(function ()
{
    jQuery.validator.addMethod("todayorfuturedate", function (value, element)
    {
        var today = new Date();
        today.setHours(0,0,0,0);
        return this.optional(element) || new Date(value) >= today;
    }, "Please enter today or a future date");

    jQuery.validator.addMethod("scheduledpaymentdate", function (value, element) {
        var date = new Date(value);
        if (date.getDay() == 6 || date.getDay() == 0) return false;
        date.setDate(date.getDate() - 1);        
        return this.optional(element) || date >= new Date();
    }, "Payments can't be scheduled on weekends, or earlier than 2 days from the current date");

    jQuery.validator.addMethod("stabperiod", function (value, element) {
        return this.optional(element) && value == "" ||
            !/Invalid|NaN/.test(new Date(value)) ||
            /^\d\d\d\d[ABab]$/i.test(value);
    }, "Please enter a valid stab period");
    jQuery.validator.addClassRules("url", {
        url: true
    });
    jQuery.validator.addMethod('daterange', function (value, element, arg) {
        if (this.optional(element)) {
            return true;
        }

        var startDate = Date.parse(arg[0]),
            endDate = Date.parse(arg[1]),
            enteredDate = Date.parse(value);

        if (isNaN(enteredDate)) {
            return false;
        }

        return ((startDate <= enteredDate) && (enteredDate <= endDate));

    }, $.validator.format("Please specify a date between {0} and {1}."))

    if (jQuery.validator.unobtrusive) {
        jQuery.validator.addMethod("enforcetrue", function (value, element, param) {
            return element.checked;
        });
        jQuery.validator.unobtrusive.adapters.addBool("enforcetrue");
    }

    // Default is to ignore hiddens, but we want select filters validated even though they are hidden. 
    // (Legacy validation overrides this default again so this only applies to unobtrusive)
    jQuery.validator.defaults.ignore = ":hidden:not(.jqselectfilter)";

    InitializeM3UI(document);
});
function AddValidationError(frmId, message) {
    var frm = $("#" + frmId);
    var el;
    message = message.replace(/{.*?}/g, function (str) {
        var el2 = str.substr(1, str.length - 2);
        if (el == null)
            el = el2;
        var str2 = frm.find("label[for=" + el2.replace(".", "_") + "]").text();
        if (str2 == null || str2.length == 0)
            str2 = frm.find("#" + el2.replace(".", "_")).attr("title");
        if (str2 == null || str2.length == 0)
            str2 = str;
        str2 = str2.split(":")[0];

        frm.find("#" + el2.replace(".", "_")).addClass("invalid");
        return str2;
    });
    //var errors = frm.find(".errors");
    //errors.append($("<label/>").attr({ "for": el }).addClass("invalid").html(message));
    //errors.show();
    ////frm.validate().showLabel(el, message);
    //if (el && frm.validate().settings.errorPlacement)
    //    frm.validate().settings.errorPlacement($("<label/>").attr({ "for": el }).addClass("invalid").html(message), $("#" + el));
    //else {
        if (frm.find(".validation-summary-errors").length)
            frm.find(".validation-summary-errors ul").append($("<li/>").html(message));
        else
            frm.find(".errors").append($("<label/>").attr({ "for": el }).addClass("invalid").html(message));
    //}

    frm.find(".errors:has(label)").show();
}
var isValid = null;
function IsValid(frmName) {
    if (isValid == false) return false;
    else return $("form#" + frmName).valid();
}


