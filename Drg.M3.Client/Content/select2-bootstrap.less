/**
 * Select2 Bootstrap CSS 1.0
 * Compatible with select2 3.3.2 and bootstrap 2.3.1
 * MIT License
 */
 
.select2-container {
  vertical-align: middle;
  &.input-mini       { width: 60px; }
  &.input-small      { width: 90px; }
  &.input-medium     { width: 150px; }
  &.input-large      { width: 210px; }
  &.input-xlarge     { width: 270px; }
  &.input-xxlarge    { width: 530px; }
  // Size to the width of a default text input.
  &.input-default    { width: 220px; }
  &[class*="span"] {
    float: none;
    margin-left: 0;
  }
}

.select2-container .select2-choice,
.select2-container-multi .select2-choices {
  height: @baseLineHeight + 8px;
  line-height: @baseLineHeight + 9px;
  border: 1px solid @inputBorder;
  .border-radius(@inputBorderRadius);
  background: none;
  background-color: @inputBackground;
  filter: none;
  .box-shadow(inset 0 1px 1px rgba(0, 0, 0, 0.075));
}

.select2-container .select2-choice,
.select2-container.select2-container-disabled .select2-choice {
  div, .select2-arrow {
    border-left: none;
    background: none;
    filter: none;
  }
}

.control-group.error [class^="select2-choice"] {
  border-color: @errorText;
}

.select2-container-multi .select2-choices .select2-search-field {
  height: 28px;
  line-height: 27px;
}

.select2-container-active .select2-choice,
.select2-container-multi.select2-container-active .select2-choices {
  border-color: rgba(82, 168, 236, 0.8);
  outline: none;
  .box-shadow(~"inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6)");
}

[class^="input-"] .select2-container {
  font-size: @baseFontSize;
}

.input-prepend [class^="select2-choice"] {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-append [class^="select2-choice"] {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.select2-dropdown-open [class^="select2-choice"] {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

[class^="input-"] .select2-offscreen {
  position: absolute;
}

/**
 * This stops the quick flash when a native selectbox is shown and 
 * then replaced by a select2 input when javascript kicks in. This can be 
 * removed if javascript is not present 
 */

select.select2 {
  height: 28px;
  visibility: hidden;
}
