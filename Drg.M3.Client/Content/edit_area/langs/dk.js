editAreaLoader.lang["dk"]={
new_document: "nyt tomt dokument",
search_button: "s&oslash;g og erstat",
search_command: "find n&aelig;ste / &aring;ben s&oslash;gefelt",
search: "s&oslash;g",
replace: "erstat",
replace_command: "erstat / &aring;ben s&oslash;gefelt",
find_next: "find n&aelig;ste",
replace_all: "erstat alle",
reg_exp: "regular expressions",
match_case: "forskel på store/sm&aring; bogstaver<br />",
not_found: "not found.",
occurrence_replaced: "occurences replaced.",
search_field_empty: "Search field empty",
restart_search_at_begin: "End of area reached. Restart at begin.",
move_popup: "flyt søgepopup",
font_size: "--Skriftstørrelse--",
go_to_line: "g&aring; til linie",
go_to_line_prompt: "gå til linienummer:",
undo: "fortryd",
redo: "gentag",
change_smooth_selection: "sl&aring; display funktioner til/fra (smartere display men mere CPU kr&aelig;vende)",
highlight: "sl&aring; syntax highlight til/fra",
reset_highlight: "nulstil highlight (hvis den er desynkroniseret fra teksten)",
word_wrap: "toggle word wrapping mode",
help: "om",
save: "gem",
load: "hent",
line_abbr: "Ln",
char_abbr: "Ch",
position: "Position",
total: "Total",
close_popup: "luk popup",
shortcuts: "Genveje",
add_tab: "tilf&oslash;j tabulation til tekst",
remove_tab: "fjern tabulation fra tekst",
about_notice: "Husk: syntax highlight funktionen b&oslash;r kun bruge til sm&aring; tekster",
toggle: "Sl&aring; editor til / fra",
accesskey: "Accesskey",
tab: "Tab",
shift: "Skift",
ctrl: "Ctrl",
esc: "Esc",
processing: "Processing...",
fullscreen: "fullscreen",
syntax_selection: "--Syntax--",
close_tab: "Close file"
};
