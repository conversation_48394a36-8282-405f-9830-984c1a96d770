using System.Web.Mvc;
using System.Collections.Generic;
using System.Collections.Specialized;

namespace Drg.M3.Client.Validation.Shared.Comm
{
    public class CreateTaskValidationSet : ValidationSet
    {
        public CreateTaskValidationSet()
        {
            Required("subject", "body", "startDate", "assignedTo");
            RequiredWhenVisible("endDate");
            Date("startDate");
        }
    }
}
