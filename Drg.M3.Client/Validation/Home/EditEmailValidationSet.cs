using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text.RegularExpressions;
using Drg.M3.Client.Validation.Validators;

namespace Drg.M3.Client.Validation.Contacts
{
    public class EditEmailValidationSet : ValidationSet
    {
        public EditEmailValidationSet()
        {
            Required("body", "subject");            
        }

        public override bool Validate(System.Collections.Specialized.NameValueCollection values)
        {
            if (!string.IsNullOrEmpty(values["include"]))
            {
                Regex r = new Regex(@"\p{Z}\s");
                string include = r.Replace(values["include"], "");
                string[] addresses = include.Split(';');

                foreach (string address in addresses)
                {
                    Regex email = new Regex(@"^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$", RegexOptions.IgnoreCase);
                    if (!email.IsMatch(address.Trim()))
                    {
                        Errors.Add("\\\"Include\\\" addresses not in correct format. Please double check to ensure email addresses are valid, and separate each complete address with a \\\";\\\" symbol.");
                        break;
                    }
                }
            }
            return base.Validate(values);
        }
    }
}
