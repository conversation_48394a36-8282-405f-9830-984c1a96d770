using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Text.RegularExpressions;

namespace Drg.M3.Client.Validation.Contacts
{
    public class EditSMSValidationSet : ValidationSet
    {
        public EditSMSValidationSet()
        {
            Required("message");
        }

        public override bool Validate(System.Collections.Specialized.NameValueCollection values)
        {
            if (!string.IsNullOrEmpty(values["include"]))
            {
                Regex r = new Regex(@"\p{Z}\s");
                string include = r.<PERSON>(values["include"], "");
                string[] numbers = include.Split(';');

                foreach (string number in numbers)
                {
                    long result;
                    if (!long.TryParse(number, out result))
                        Errors.Add("\\\"Include\\\" numbers not in correct format. Please use digits only, and separate each complete number with a \\\";\\\" symbol.");
                }
            }
            return base.Validate(values);
        }
    }
}
