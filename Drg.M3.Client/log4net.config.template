<?xml version="1.0"?>
<log4net debug="true">
	<!-- Define some output appenders -->
	<appender name="trace" type="log4net.Appender.TraceAppender, log4net">
		<layout type="log4net.Layout.PatternLayout,log4net">
			<param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n" />
		</layout>
	</appender>
	<appender name="console" type="log4net.Appender.ConsoleAppender, log4net">
		<layout type="log4net.Layout.PatternLayout,log4net">
			<param name="ConversionPattern" value="%d [%t] %-5p %c - %m%n" />
		</layout>
	</appender>
	<appender name="NHibernateFileLog" type="log4net.Appender.FileAppender">
		<file value="App_Data/logs/nhibernate.txt" />
		<appendToFile value="false" />
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%d{HH:mm:ss.fff} [%t] %-5p %c - %m%n"  />
		</layout>
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="1MB" />
	</appender>
	<appender name="alertLogFile" type="log4net.Appender.RollingFileAppender,log4net">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%newline[%03thread] %-5level %date &lt;%logger&gt;%newline%message%newline" />
		</layout>
		<file value="App_Data/Log/alerts.txt" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="10MB" />
		<countDirection value="1" />
		<preserveLogFileNameExtension value="true" />
		<staticLogFileName value="true" />
	</appender>
	<appender name="scheduledTasksLogFile" type="log4net.Appender.RollingFileAppender,log4net">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%newline[%03thread] %-5level %date &lt;%logger&gt;%newline%message%newline" />
		</layout>
		<file value="App_Data/Log/scheduled-tasks.txt" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="1MB" />
		<countDirection value="1" />
		<preserveLogFileNameExtension value="true" />
		<staticLogFileName value="true" />
	</appender>
	<appender name="standardDebugLogFile" type="log4net.Appender.RollingFileAppender,log4net">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%newline[%03thread] %-5level %date &lt;%logger&gt;%newline%message%newline" />
		</layout>
		<file value="App_Data/Log/standard-debug.txt" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<maxSizeRollBackups value="10" />
		<maximumFileSize value="1MB" />
		<countDirection value="1" />
		<preserveLogFileNameExtension value="true" />
		<staticLogFileName value="true" />
	</appender>
	<appender name="emailChangeLogFile" type="log4net.Appender.RollingFileAppender,log4net">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%newline[%03thread] %-5level %date &lt;%logger&gt;%newline%message%newline" />
		</layout>
		<file value="App_Data/Log/email-changes.txt" />
		<appendToFile value="true" />
		<rollingStyle value="Size" />
		<maxSizeRollBackups value="100" />
		<maximumFileSize value="10MB" />
		<countDirection value="1" />
		<preserveLogFileNameExtension value="true" />
		<staticLogFileName value="true" />
	</appender>
	<!--<appender name="smtpAppender" type="log4net.Appender.SmtpAppender">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%newline[%03thread] %-5level %date &lt;%logger&gt;%newline%message%newline" />
		</layout>
		<to value="<EMAIL>" />
		<from value="<EMAIL>" />
		<smtpHost value="smtp.gmail.com" />
		<authentication value="Basic" />
		<username value="<EMAIL>" />
		<password value="n1mr0ddrg" />
		<port value ="587"/>
		<enableSsl value="true"/>
		<subject value="EMS Error - Beta" />
		<bufferSize value="10" />
		<lossy value="true" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="WARN"/>
		</evaluator>
	</appender>-->
	<appender name="smtpAppender" type="log4net.Appender.SmtpAppender">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%newline[%03thread] %-5level %date &lt;%logger&gt;%newline%message%newline" />
		</layout>
		<to value="<EMAIL>" />
		<from value="<EMAIL>" />
		<smtpHost value="localhost" />
		<authentication value="None" />
		<username value="" />
		<password value="" />
		<port value ="25"/>
		<subject value="EMS Error" />
		<bufferSize value="10" />
		<lossy value="true" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="WARN"/>
		</evaluator>
	</appender>
	
	<!-- Setup the root category, add the appenders and set the default priority -->
	<root>
		<priority value="INFO" />
		<appender-ref ref="trace" />
	</root>
	<logger name="standardDebug">
		<level value="DEBUG" />
		<appender-ref ref="standardDebugLogFile" />
		<appender-ref ref="smtpAppender" />
	</logger>
	<logger name="alerts">
		<level value="DEBUG" />
		<appender-ref ref="alertLogFile" />
		<appender-ref ref="smtpAppender" />
	</logger>
	<logger name="scheduledTasks">
		<level value="DEBUG" />
		<appender-ref ref="scheduledTasksLogFile" />
		<appender-ref ref="smtpAppender" />
	</logger>
	<logger name="emailChanges">
		<level value="INFO" />
		<appender-ref ref="emailChangeLogFile" />
	</logger>
	<logger name="NHibernate">
		<level value="ERROR" />
	</logger>
	<logger name="NHibernate.SQL">
		<level value="DEBUG" />
		<appender-ref ref="NHibernateFileLog" />
	</logger>
</log4net>